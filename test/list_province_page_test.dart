import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/pages/list_province/list_province_page.dart';

void main() {
  group('ListProvincePage Tests', () {
    test('ListProvincePage should have correct grayColor constant', () {
      expect(ListProvincePage.grayColor, const Color(0xFFF1F5F9));
    });

    test('ListProvincePage should be a ConsumerWidget', () {
      const page = ListProvincePage();
      expect(page, isA<ConsumerWidget>());
    });

    test('ListProvincePage should have const constructor', () {
      // 验证构造函数是const的
      const page1 = ListProvincePage();
      const page2 = ListProvincePage();
      expect(identical(page1, page2), true);
    });
  });
}
