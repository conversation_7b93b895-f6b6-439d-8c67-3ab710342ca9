import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/provider/province_list/province_list_provider.dart';
import 'package:zrreport/common/models/province.dart';

void main() {
  group('ProvinceListProvider Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('Province model should create correctly', () {
      final province = Province(
        id: '1',
        name: '北京市',
        pinyin: 'beijing',
        areaId: '110000',
        code: '11',
        status: 1,
        deleteStatus: 0,
        createBy: 'system',
        createTime: '2023-01-01',
        updateBy: 'system',
        updateTime: '2023-01-01',
        invoiceStatus: '1',
        taxStatus: '1',
        taxLoginUrl: 'https://example.com',
      );

      expect(province.id, '1');
      expect(province.name, '北京市');
      expect(province.pinyin, 'beijing');
      expect(province.status, 1);
    });

    test('Province should create from JSON correctly', () {
      final json = {
        'id': '1',
        'name': '北京市',
        'pinyin': 'beijing',
        'areaId': '110000',
        'code': '11',
        'status': 1,
        'deleteStatus': 0,
        'createBy': 'system',
        'createTime': '2023-01-01',
        'updateBy': 'system',
        'updateTime': '2023-01-01',
        'invoiceStatus': '1',
        'taxStatus': '1',
        'taxLoginUrl': 'https://example.com',
      };

      final province = Province.fromJson(json);
      expect(province.id, '1');
      expect(province.name, '北京市');
      expect(province.pinyin, 'beijing');
      expect(province.areaId, '110000');
    });

    test('ProvinceListNotifier should have correct initial state', () {
      final notifier = container.read(provinceListNotifierProvider.notifier);
      
      // 初始状态应该是loading
      expect(notifier.isLoading, true);
      expect(notifier.hasData, false);
      expect(notifier.hasError, false);
    });

    test('findProvinceById should return null when no data', () {
      final notifier = container.read(provinceListNotifierProvider.notifier);
      
      final result = notifier.findProvinceById('1');
      expect(result, null);
    });

    test('ProvinceListNotifier should be keepAlive', () {
      // 测试provider是否会保持活跃状态
      final provider = provinceListNotifierProvider;
      
      // 检查provider是否配置为keepAlive
      expect(provider.dependencies, null); // keepAlive provider通常没有dependencies
    });
  });
}
