group 'com.kaivean.system_proxy'
version '1.0-SNAPSHOT'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.1'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

android {
    namespace 'com.kaivean.system_proxy'

    compileSdk 33

    defaultConfig {
        minSdk 19
    }
    lintOptions {
        disable 'InvalidPackage'
    }
}
