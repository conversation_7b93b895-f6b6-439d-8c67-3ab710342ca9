import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get/get.dart';

import 'common/index.dart';

/// 一些全局的配置
///
class Global {
  /// 全局的ProviderContainer，用于在非Widget环境中访问Provider
  static ProviderContainer? _container;

  static Future<void> init() async {
    // 插件初始化
    WidgetsFlutterBinding.ensureInitialized();
    await setupGlobalHttpOverrides();
    Loading.init();
    await Get.putAsync<StorageService>(() => StorageService().init());
    await Get.putAsync<AppConfig>(() => AppConfig().init());

    // 初始化服务
    Get.put<SXHttpService>(SXHttpService());
    Get.put<UserService>(UserService());
    Get.put<AppSettingStorage>(AppSettingStorage());

    EasyRefresh.defaultFooterBuilder = () => EasyRefreshFixIn18.getFooter();
    EasyRefresh.defaultHeaderBuilder = () => EasyRefreshFixIn18.getHeader();
  }

  /// 初始化字典类型帮助类
  ///
  /// 需要在ProviderScope创建后调用
  static void initializeDictTypesHelper(ProviderContainer container) {
    _container = container;
    DictTypesHelper.initialize(container);
  }

  /// 预加载字典数据
  ///
  /// 可以在应用启动后调用，提前加载字典数据
  static Future<void> preloadDictData() async {
    try {
      await DictTypesHelper.initializeDictData();
    } catch (e) {
      // 预加载失败不影响应用启动，只记录错误
      debugPrint('预加载字典数据失败: $e');
    }
  }

  /// 获取全局ProviderContainer
  static ProviderContainer? get container => _container;
}
