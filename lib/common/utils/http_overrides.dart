import 'dart:io';

import 'package:system_proxy/system_proxy.dart';

Future<void> setupGlobalHttpOverrides() async {
  String proxy = '';
  do {
    if (proxy.isNotEmpty) return;

    String envProxy = const String.fromEnvironment('PROXY');
    if (envProxy.isNotEmpty) {
      proxy = envProxy;
      break;
    }
    Map<String, String>? systemProxy = await SystemProxy.getProxySettings();
    if (systemProxy != null) {
      String? host = systemProxy['host'];
      String? port = systemProxy['port'];
      proxy = 'PROXY $host:$port';
      break;
    }
  } while (false);

  if (proxy.isNotEmpty) {
    HttpOverrides.global = _HttpOverrides(proxy);
  }
}

class _HttpOverrides extends HttpOverrides {
  _HttpOverrides(this.proxy);
  String proxy;

  @override
  HttpClient createHttpClient(SecurityContext? context) {
    final HttpClient client = super.createHttpClient(context);
    client.badCertificateCallback =
        (X509Certificate cert, String host, int port) => true;
    return client;
  }

  @override
  String findProxyFromEnvironment(_, __) {
    return proxy;
  }
}
