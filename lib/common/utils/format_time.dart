import 'package:intl/intl.dart';

/// 格式化时间字符串
///
/// [timeStr] 输入的时间字符串，支持以下格式：
/// - ISO 8601 格式 (如: "2024-03-20T10:30:00Z")
/// - 时间戳 (毫秒)
/// - 自定义格式 (如: "2024-03-20 10:30:00")
///
/// 返回格式化后的时间字符串，规则如下：
/// - 1小时内：显示 "xx分钟之前"
/// - 6小时内：显示 "xx小时前"
/// - 当天：显示 "HH:mm"
/// - 昨天：显示 "昨天 HH:mm"
/// - 今年：显示 "MM-dd HH:mm"
/// - 其他：显示 "yyyy-MM-dd HH:mm"
String formatTime(String timeStr) {
  if (timeStr.isEmpty) return '';

  DateTime? dateTime;

  // 尝试解析时间戳
  final timestamp = int.tryParse(timeStr);
  if (timestamp != null) {
    dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
  } else {
    // 尝试解析 ISO 8601 格式
    try {
      dateTime = DateTime.parse(timeStr);
    } catch (e) {
      // 尝试解析自定义格式
      try {
        dateTime = DateFormat('yyyy-MM-dd HH:mm:ss').parse(timeStr);
      } catch (e) {
        return '';
      }
    }
  }

  final now = DateTime.now();
  final today = DateTime(now.year, now.month, now.day);
  final yesterday = today.subtract(const Duration(days: 1));
  final date = DateTime(dateTime.year, dateTime.month, dateTime.day);

  // 计算时间差
  final difference = now.difference(dateTime);
  // 5分钟内
  if (difference.inMinutes <= 5) {
    return '刚刚';
  }

  // 1小时内
  if (difference.inMinutes < 60) {
    return '${difference.inMinutes}分钟之前';
  }

  // 6小时内
  if (difference.inHours < 6) {
    return '${difference.inHours}小时前';
  }

  // 当天
  if (date == today) {
    return DateFormat('HH:mm').format(dateTime);
  }

  // 昨天
  if (date == yesterday) {
    return '昨天 ${DateFormat('HH:mm').format(dateTime)}';
  }

  // 今年
  if (dateTime.year == now.year) {
    return DateFormat('MM-dd HH:mm').format(dateTime);
  }

  // 其他情况
  return DateFormat('yyyy-MM-dd HH:mm').format(dateTime);
}
