import 'dart:io';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';
import 'package:url_launcher/url_launcher.dart';

enum MapType {
  baidu,
  tencent,
  gaode;

  String get name {
    switch (this) {
      case MapType.baidu:
        return "百度地图";
      case MapType.tencent:
        return "腾讯地图";
      case MapType.gaode:
        return "高德地图";
    }
  }

  String getUrlForCoord(double latitude, double longitude) {
    switch (this) {
      case MapType.baidu:
        // 百度地图官方文档: https://lbsyun.baidu.com/faq/api?title=webapi/uri/andriod
        var url =
            'baidumap://map/direction?destination=$latitude,$longitude&coord_type=gcj02&mode=driving';
        return url;
      case MapType.gaode:

        /// 高德地图官方文档 - Android: https://lbs.amap.com/api/amap-mobile/guide/android/route
        /// 高德地图官方文档 - iOS: https://lbs.amap.com/api/amap-mobile/guide/ios/route
        var type = Platform.isIOS
            ? 'iosamap://path?sourceApplication=applicationName&'
            : 'amapuri://route/plan/?';
        var url = '${type}dlat=$latitude&dlon=$longitude&t=0';

        return url;
      case MapType.tencent:
        // 腾讯地图官方文档: https://lbs.qq.com/webApi/uriV1/uriGuide/uriMobileRoute
        var url =
            'qqmap://map/routeplan?type=drive&fromcoord=CurrentLocation&tocoord=$latitude,$longitude&referer=IXHBZ-QIZE4-ZQ6UP-DJYEO-HC2K2-EZBXJ';
        return url;
    }
  }
}

class MapUtil {
  static Future<void> showMapSheet(
      BuildContext context, double latitude, double longitude) async {
    try {
      List<MapType> availableMaps = [];

      for (var type in MapType.values) {
        var parse = Uri.parse(type.getUrlForCoord(latitude, longitude));
        debugPrint("parse: $parse");
        bool canLaunch = await canLaunchUrl(parse);
        debugPrint("parse: $parse canLaunch:$canLaunch");
        if (canLaunch) {
          availableMaps.add(type);
        }
      }
      defaultLogger.debug('showMapSheet availableMaps:$availableMaps');

      // if (availableMaps.isEmpty && Platform.isAndroid) {
      //   Loading.toast('没有安装地图app');
      //   return;
      // }

      availableMaps = MapType.values;

      showModalBottomSheet(
        // ignore: use_build_context_synchronously
        // backgroundColor: Colors.,
        context: context,
        builder: (BuildContext context) {
          final style = TextStyle(
              fontSize: 16,
              color: AppColors.textColor1,
              fontWeight: FontWeight.bold);
          return SafeArea(
            child: SingleChildScrollView(
              child: Wrap(
                children: <Widget>[
                  // Container(color: Colors.red, height: 30)
                  //     .clipRRect(topLeft: 8, topRight: 8),
                  for (var map in availableMaps)
                    ListTile(
                      onTap: () async {
                        Navigator.of(context).pop();

                        var parse =
                            Uri.parse(map.getUrlForCoord(latitude, longitude));
                        try {
                          bool isSuccess = await launchUrl(parse);
                          if (!isSuccess) {
                            Loading.toast('打开${map.name}失败，请确认app是否安装');
                          }
                        } catch (e) {
                          Loading.toast('打开${map.name}失败，请确认app是否安装');
                        }
                      },
                      title: Text(
                        map.name,
                        style: style,
                      ).center(),
                    ).backgroundColor(Colors.white),
                  if (Platform.isIOS)
                    ListTile(
                      onTap: () => _gotoAppleMap(longitude, latitude),
                      title: Text(
                        '苹果自带地图',
                        style: style,
                      ).center(),
                    ).backgroundColor(Colors.white),
                ],
              ),
            ),
          );
        },
      );
    } catch (e) {
      defaultLogger.debug('showMapSheet error occur,$e');
    }
  }

  /// 苹果地图
  static Future<bool> _gotoAppleMap(longitude, latitude) async {
    var url = 'http://maps.apple.com/?daddr=$latitude,$longitude';

    var parse = Uri.parse(url);
    bool canLaunch = await canLaunchUrl(parse);

    if (!canLaunch) {
      Loading.toast('打开失败~');
      return false;
    }

    await launchUrl(Uri.parse(url));
    return true;
  }
}
