import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:zrreport/common/index.dart';

void launchPhone(BuildContext context, String phoneNumber) async {
  // 处理多个电话号码的情况
  final phones = phoneNumber.split(',');
  if (phones.length > 1) {
    // 如果有多个号码，弹出底部选择菜单
    final selectedPhone = await showModalBottomSheet<String?>(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.symmetric(vertical: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(bottom: 16),
              child: Text(
                '请选择要拨打的电话',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            ...phones.map((phone) => ListTile(
                  title: Text(
                    phone.trim(),
                    textAlign: TextAlign.center,
                  ),
                  onTap: () => Navigator.pop(context, phone.trim()),
                )),
          ],
        ),
      ),
    );

    if (selectedPhone == null) return; // 用户取消选择
    phoneNumber = selectedPhone;
  }

  String url = 'tel:$phoneNumber';
  try {
    final Uri phoneUri = Uri.parse(url);
    if (await canLaunchUrl(phoneUri)) {
      // 使用LaunchMode.externalApplication确保在Android上正确启动电话应用
      await launchUrl(
        phoneUri,
      );
    } else {
      throw '无法拨打电话: $url';
    }
  } catch (e) {
    // 处理拨打电话失败的情况
    Loading.error('拨打电话失败,$e');
    debugPrint('拨打电话异常: $e');
  }
}
