/// 将数字转换为万或亿为单位的字符串
/// [value] 要转换的数字
/// [decimalPlaces] 保留小数位数，默认2位
String formatLargeNumber(double value, {int decimalPlaces = 2}) {
  if (value == 0) return '0';

  // 亿
  if (value.abs() >= 100000000) {
    return '${(value / 100000000).toStringAsFixed(decimalPlaces)}亿';
  }
  // 万
  else if (value.abs() >= 10000) {
    return '${(value / 10000).toStringAsFixed(decimalPlaces)}万';
  }
  // 小于1万的数字
  else {
    return value.toStringAsFixed(decimalPlaces);
  }
}

String formateLargeStringNumber(String? string) {
  final defaultResult = "-";

  if (string == null) {
    return defaultResult;
  }

  final num = double.tryParse(string);
  if (num == null) {
    return defaultResult;
  }

  return formatLargeNumber(num);
}
