import 'package:dio/dio.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/common/models/taxes_info.dart';
import 'package:zrreport/pages/loan_services/tax_matching/models/tax_supplement_models.dart';

class QueryApi {
  /// 获取公司税号等信息
  /// creditCodeName 统一社会信用代码/企业名称
  static Future<BaseResponse<CompanyInfoModel>> queryCompanyInfo(
      String creditCodeName) async {
    final response = await SXHttpService.to
        .post('/tax/share/login', data: {'creditCodeName': creditCodeName});
    return BaseResponse.fromJson(
      response.data,
      (data) => CompanyInfoModel.fromJson(data as Map<String, dynamic>),
    );
  }

  static Future<BaseResponse<TaxOnlyAccountLoginModel>> taxOnlyAccountLogin(
      {required TaxOnlyAccountLoginEntity entity}) async {
    final response = await SXHttpService.to
        .post('/tax/tax/onlyAccountLogin', data: entity.toJson());
    return BaseResponse.fromJson(
        response.data,
        (data) =>
            TaxOnlyAccountLoginModel.fromJson(data as Map<String, dynamic>));
  }

  static Future<BaseResponse<TaxLoginModel>> taxLogin(
      {required TaxNeedSmsCOdeLoginEntity entity}) async {
    final response =
        await SXHttpService.to.post('/tax/tax/login', data: entity.toJson());
    return BaseResponse.fromJson(
      response.data,
      (data) => TaxLoginModel.fromJson(data as Map<String, dynamic>),
    );
  }

  static Future<BaseResponse> taxSendSmsCode(
      {required TaxSendSmsCodeEntity entity}) async {
    final response =
        await SXHttpService.to.post('/tax/tax/sendCode', data: entity.toJson());
    return BaseResponse.fromJson(response.data, (data) => data);
  }

  static Future<BaseResponse> taxCaptchaLogin(
      {required TaxCaptchaLoginEntity entity}) async {
    final response = await SXHttpService.to
        .post('/tax/tax/captchaLogin', data: entity.toJson());
    return BaseResponse.fromJson(response.data, (data) => data);
  }

  static Future<BaseResponse<Pagination<QueryReport>>> listReport(
      {required ListReportEntity entity, CancelToken? cancelToken}) async {
    final response = await SXHttpService.to.get('/tax/share/listReport',
        cancelToken: cancelToken, params: entity.toJson());
    return BaseResponse.fromJson(
      response.data,
      (data) => Pagination.fromJson(data as Map<String, dynamic>, (data) {
        return QueryReport.fromJson(data);
      }),
    );
  }

  ///
  static Future<BaseResponse<ReportDetailModel>> reportShare(
      String shareCode) async {
    final response = await SXHttpService.to
        .get('/tax/share/getShareReport', params: {'shareCode': shareCode});
    return BaseResponse.fromJson(
      response.data,
      (data) => ReportDetailModel.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 获取企业报告基本信息详情
  static Future<BaseResponse<ReportDetailModel>> reportDetail(
      String shareCode) async {
    final response = await SXHttpService.to
        .get('/tax/share/getShareReport', params: {'shareCode': shareCode});
    return BaseResponse.fromJson(
      response.data,
      (data) => ReportDetailModel.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 纳税信息
  static Future<BaseResponse<TaxesInfo>> taxesDetail(String shareCode) async {
    final response = await SXHttpService.to.get(
        '/tax/share/v2/getEnterprisePaidTaxesAmount',
        params: {'shareCode': shareCode});
    return BaseResponse.fromJson(
      response.data,
      (data) => TaxesInfo.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 正税/附加税信息
  static Future<BaseResponse<List<Datum>>> paidTaxesDatDetail(
      String shareCode, int taxType) async {
    final response = await SXHttpService.to.get(
        '/tax/share/v2/getEnterprisePaidTaxesDataList',
        params: {'shareCode': shareCode, 'taxType': taxType});
    return BaseResponse.fromJson(
      response.data,
      (data) {
        if (data is List) {
          return data
              .map((item) => Datum.fromJson(item as Map<String, dynamic>))
              .toList();
        } else {
          throw Exception('Expected List but got ${data.runtimeType}');
        }
      },
    );
  }

  /// 开具发票
  static Future<BaseResponse<EnterpriseInvoiceInfo>> issueInvoice(
      String shareCode) async {
    final response = await SXHttpService.to.get(
        '/tax/share/v2/getEnterpriseInvoiceInfo',
        params: {'shareCode': shareCode});
    return BaseResponse.fromJson(
      response.data,
      (data) => EnterpriseInvoiceInfo.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 申报数据
  static Future<BaseResponse<EnterpriseApplyAmount>> declarationData(
      String shareCode) async {
    final response = await SXHttpService.to.get(
        '/tax/share/v2/getEnterpriseApplyAmount',
        params: {'shareCode': shareCode});
    return BaseResponse.fromJson(
      response.data,
      (data) => EnterpriseApplyAmount.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 取得发票
  static Future<BaseResponse<EnterpriseInvoiceInfoForIn>> getInvoice(
      String shareCode) async {
    final response = await SXHttpService.to.get(
        '/tax/share/v2/getEnterpriseInvoiceInfoForIn',
        params: {'shareCode': shareCode});
    return BaseResponse.fromJson(
      response.data,
      (data) =>
          EnterpriseInvoiceInfoForIn.fromJson(data as Map<String, dynamic>),
    );
  }


  /// 获取企业报告基本信息详情
  static Future<BaseResponse<EnterpriseCaseModel>> caseInfo(
      String shareCode) async {
    final response = await SXHttpService.to
        .get('/tax/share/getEnterpriseCases', params: {'shareCode': shareCode});
    return BaseResponse.fromJson(
      response.data,
      (data) => EnterpriseCaseModel.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 供应商信息
  static Future<BaseResponse<SupplierInformation>> supplierInformation(
      String shareCode) async {
    final response = await SXHttpService.to.get(
        '/tax/share/getEnterpriseInvoiceTopTen',
        params: {'shareCode': shareCode});
    return BaseResponse.fromJson(
      response.data,
      (data) => SupplierInformation.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 获取指定访问用户列表
  static Future<BaseResponse<List<SpecifyAccessUser>>> getSpecifyAccessUserlist(
      String shareCode) async {
    final response = await SXHttpService.to.get(
        '/tax/share/getSpecifyAccessUserList',
        params: {'shareCode': shareCode});
    return BaseResponse.fromJson(
      response.data,
      (data) => (data as List)
          .map((item) =>
              SpecifyAccessUser.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// 设置权限可见性
  static Future<BaseResponse<bool>> setPermissionVisible(
      PermissionVisibleEntity entity) async {
    final response =
        await SXHttpService.to.put('/tax/share/visible', data: entity.toJson());

    return BaseResponse.fromJson(
      response.data,
      (data) => data as bool,
    );
  }

  ///
  static Future<BaseResponse<UsageStatisticsModel>> usageStatistics() async {
    final response = await SXHttpService.to.get('/tax/share/getTaxQueryShare');
    return BaseResponse.fromJson(
      response.data,
      (data) => UsageStatisticsModel.fromJson(data as Map<String, dynamic>),
    );
  }

  static Future<BaseResponse<List<Province>>> listProvince() async {
    final response = await SXHttpService.to.get('/tax/listProvince');
    return BaseResponse.fromJsonList(
      response.data,
      (data) => Province.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 重新查询接口
  static Future<BaseResponse<RefreshQueryResponse>> refreshQuery(
      RefreshQueryEntity entity) async {
    final response = await SXHttpService.to.post(
      '/tax/app/loginSms',
      data: entity.toJson(),
    );
    return BaseResponse.fromJson(
      response.data,
      (data) => RefreshQueryResponse.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 重新查询--发送验证码接口
  static Future<BaseResponse<bool>> appSendSms(AppSendSmsEntity entity) async {
    final response = await SXHttpService.to.post(
      '/tax/app/sendSms',
      data: entity.toJson(),
    );
    return BaseResponse.fromJson(
      response.data,
      (data) => data as bool,
    );
  }

  /// 重新查询--验证短信验证码接口
  static Future<BaseResponse> appVerifySms(AppVerifySmsEntity entity) async {
    final response = await SXHttpService.to.post(
      '/tax/app/verifySms',
      data: entity.toJson(),
    );
    return BaseResponse.fromJson(response.data, (data) => data);
  }





  static Future<BaseResponse<Pagination<SearchEnterprise>>> searchEnterprise(
      {required SearchEnterpriseEntity entity,
      CancelToken? cancelToken}) async {
    final response = await SXHttpService.to.post(
      '/tax/getEnterpriseSearch',
      cancelToken: cancelToken,
      data: entity.toJson(),
    );
    return BaseResponse.fromJson(
      response.data,
      (data) => Pagination.fromJson(data as Map<String, dynamic>, (data) {
        return SearchEnterprise.fromJson(data);
      }),
    );
  }

  static Future<BaseResponse<List<TaxpayerInfo>>> searchKeyListAll(
      {CancelToken? cancelToken}) async {
    final response = await SXHttpService.to.get(
      '/tax/search/listAll',
      cancelToken: cancelToken,
    );
    return BaseResponse.fromJsonList(
      response.data,
      (data) => TaxpayerInfo.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 创建匹配企业
  static Future<BaseResponse<CreateMatchEnterpriseResponse>>
      createMatchEnterprise({
    required String taskId,
    required String enterpriseId,
    required String provinceName,
    required String creditCode,
    required String phone,
    required String creditPwd,
    required String areaId,
    required String enterpriseName,
    required String personStand,
    int garageStatus = 0,
    String channelType = 'routine',
  }) async {
    final data = {
      'taskId': taskId,
      'enterpriseId': enterpriseId,
      'provinceName': provinceName,
      'creditCode': creditCode,
      'phone': phone,
      'creditPwd': creditPwd,
      'areaId': areaId,
      'garageStatus': garageStatus,
      'enterpriseName': enterpriseName,
      'personStand': personStand,
      'channelType': channelType,
    };

    final response = await SXHttpService.to.post(
      '/cmsMatchEnterprise/createMatchEnterprise',
      data: data,
    );

    return BaseResponse.fromJson(
      response.data,
      (data) =>
          CreateMatchEnterpriseResponse.fromJson(data as Map<String, dynamic>),
    );
  }
}
