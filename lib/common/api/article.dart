import 'package:dio/dio.dart';
import 'package:zrreport/common/index.dart';

class ArticleApi {
  static Future<BaseResponse<Pagination<Article>>> articles(
      {required ListEntity entity, CancelToken? cancelToken}) async {
    final response = await SXHttpService.to.get('/article/page',
        cancelToken: cancelToken, params: entity.toJson());
    return BaseResponse.fromJson(
      response.data,
      (data) => Pagination.fromJson(data as Map<String, dynamic>, (data) {
        return Article.fromJson(data);
      }),
    );
  }

  static Future<BaseResponse<ArticleDetail>> articleDetail(
      {required String articleId}) async {
    final response = await SXHttpService.to.get('/article/info/$articleId');
    return BaseResponse.fromJson(
      response.data,
      (data) => ArticleDetail.fromJson(data),
    );
  }

  //点赞/取消点赞
  static Future<BaseResponse<bool?>> likeVideo(String articleId) async {
    final response = await SXHttpService.to
        .post('/article/like', data: {"articleId": articleId});
    return createBoolBaseResponse(response.data);
  }

//收藏/取消收藏
  static Future<BaseResponse<bool?>> collectVideo(String articleId) async {
    final response = await SXHttpService.to.post('/article/collect', data: {
      "articleId": articleId,
    });
    return createBoolBaseResponse(response.data);
  }
}
