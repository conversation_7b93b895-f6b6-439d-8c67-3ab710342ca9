import 'package:zrreport/common/index.dart';

class UserApi {
  /// 获取登录用户信息
  static Future<BaseResponse<UserProfile>> getUserDetail() async {
    final response = await SXHttpService.to.get('/sso/info');
    return BaseResponse.fromJson(
      response.data,
      (data) => UserProfile.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 上头图片
  static Future<UploadImageResp> uploadUserAvatar(String imagePath) async {
    // final response = await SXHttpService.to.uploadImage('/upload/image',
    //     imagePath: imagePath, imageType: ImageType.avatar);
    // return createUploadImageResp(response.data);
    return upload(imagePath, ImageType.avatar);
  }

  /// 上头图片
  static Future<UploadImageResp> upload(
      String imagePath, ImageType imageType) async {
    final response = await SXHttpService.to.uploadImage('/upload/image',
        imagePath: imagePath, imageType: imageType);
    return createUploadImageResp(response.data);
  }

  static Future<BaseResponse> updateUserProfile(
      UpdateProfileRequestEntity entity) async {
    final response = await SXHttpService.to
        .post('/sso/updateProfile', data: entity.toJson());
    return createBaseResponse(response.data);
  }

  static Future<BaseResponse> feedback(FeedbackEntity entity) async {
    final response =
        await SXHttpService.to.post('/feedback/comment', data: entity.toJson());
    return createBaseResponse(response.data);
  }
}
