import 'package:dio/dio.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/branch/bank_branch/model/bank_branch_model.dart';
import 'package:zrreport/pages/branch/bank_contacts/model/bank_book.dart';
import 'package:zrreport/pages/branch/credit_branch/model/credit_branch_model.dart';

/// 征信网点和银行通讯录API服务
class BranchApi {
  /// 获取征信网点列表
  static Future<BaseResponse<Pagination<CreditBranch>>> getCreditBranch({
    required int pageNum,
    required int pageSize,
    String? keyword,
    double? lat,
    double? lon,
    CancelToken? cancelToken,
  }) async {
    final response = await SXHttpService.to.get(
      '/credit/outlets/closestPage',
      params: {
        'pageNum': pageNum,
        'pageSize': pageSize,
        'lat': lat,
        'lon': lon,
          'outletsName': keyword
      },
        cancelToken: cancelToken
    );
    return BaseResponse.fromJson(
      response.data,
      (json) => Pagination.fromJson(
        json,
        (item) => CreditBranch.fromJson(item),
      ),
    );
  }

  /// 获取银行网点列表
  static Future<BaseResponse<Pagination<BankBranch>>> getBankBranch({
    required int pageNum,
    required int pageSize,
    String? keyword,
    CancelToken? cancelToken,
  }) async {
    final response = await SXHttpService.to.get(
        // '/bankBook/page',
        '/bankInfo/page',
        params: {
          'pageNum': pageNum,
          'pageSize': pageSize,
          'name': keyword,
          'platformType': Constants.platform
        },
        cancelToken: cancelToken);
    return BaseResponse.fromJson(
      response.data,
      (json) => Pagination.fromJson(
        json,
        (item) => BankBranch.fromJson(item),
      ),
    );
  }

  /// 获取银行网点列表
  static Future<BaseResponse<Pagination<BankBook>>> getBankBook({
    required String bankId,
    required double longitude,
    required double latitude,
    required int pageNum,
    required int pageSize,
    CancelToken? cancelToken,
  }) async {
    final response = await SXHttpService.to.get(
        // '/bankBook/page',
        '/bankBook/page',
        params: {
          'bankId': bankId,
          'pageNum': pageNum,
          'pageSize': pageSize,
          'lon': longitude,
          'lat': latitude,
          'platformType': Constants.platform
        },
        cancelToken: cancelToken
    );
    return BaseResponse.fromJson(
      response.data,
      (json) => Pagination.fromJson(
        json,
        (item) => BankBook.fromJson(item),
      ),
    );
  }
}
