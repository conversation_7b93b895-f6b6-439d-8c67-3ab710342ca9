import 'package:zrreport/common/index.dart';

class LoginApi {
  /// 获取短信验证码
  static Future<GetSmsCodeResponse> getSmsCode(String phone) async {
    final response = await SXHttpService.to.get('/sso/getSmsCode', params: {
      'telephone': phone,
    });
    return createGetSmsCodeResponse(response.data);
  }

  /// 使用手机验证码登录
  static Future<LoginResponse> loginByMobile(
      LoginByMobileRequestEntity entity) async {
    final response =
        await SXHttpService.to.post('/sso/login/mobile', data: entity.toJson());
    return createLoginResponse(response.data);
  }

  /// 使用密码登录
  static Future<LoginResponse> loginByPassword(
      LoginByPasswordRequestEntity entity) async {
    final response =
        await SXHttpService.to.post('/sso/login', data: entity.toJson());
    return createLoginResponse(response.data);
  }

  /// 退出登录
  static Future<BaseResponse> logout() async {
    final response = await SXHttpService.to.get('/sso/logout');
    return createBaseResponse(response.data);
  }

  /// 注销
  static Future<BaseResponse> unregister() async {
    final response = await SXHttpService.to.get('/sso/cancel');
    return createBaseResponse(response.data);
  }

  /// 注册
  static Future<BaseResponse> register(RegisterRequestEntity entity) async {
    final response =
        await SXHttpService.to.post('/sso/register', data: entity.toJson());
    return createBaseResponse(response.data);
  }


  /// 获取登录用户信息
  static Future<LoginResponse> refreshToken() async {
    final response = await SXHttpService.to.get('/sso/refreshToken');
    return BaseResponse.fromJson(
      response.data,
      (data) => LoginResponseModel.fromJson(data),
    );
  }

}
