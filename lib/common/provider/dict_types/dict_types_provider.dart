import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'api/dict_types_api.dart';
import 'dict_types_state.dart';
import 'models/dict_type_models.dart';

part 'dict_types_provider.g.dart';

/// 字典类型数据管理器
/// 
/// 这是一个全局的Provider，用于管理字典类型数据
/// 支持自动缓存和按需加载
@Riverpod(keepAlive: true) // 保持存活，不会被销毁
class DictTypesNotifier extends _$DictTypesNotifier {
  @override
  DictTypesState build() {
    return const DictTypesState();
  }

  /// 初始化字典数据
  /// 
  /// 如果数据为空或需要刷新，则从服务器获取最新数据
  Future<void> initialize() async {
    if (state.hasData && !state.needsRefresh) {
      return; // 数据存在且不需要刷新
    }

    await _loadDictTypes();
  }

  /// 强制刷新字典数据
  Future<void> refresh() async {
    await _loadDictTypes();
  }

  /// 加载字典类型数据
  Future<void> _loadDictTypes() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await DictTypesApi.getDictTypes();
      
      if (response.code == 200 && response.data != null) {
        state = state.copyWith(
          isLoading: false,
          data: response.data,
          lastUpdated: DateTime.now(),
          isInitialized: true,
          error: null,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? '获取字典数据失败',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '网络错误: ${e.toString()}',
      );
    }
  }

  /// 根据value查询贷款类型
  /// 
  /// 如果数据未加载，会自动加载数据
  Future<DictItem?> getProductCidByValue(String value) async {
    await _ensureDataLoaded();
    return state.getProductCidByValue(value);
  }

  /// 根据value查询还款方式类型
  /// 
  /// 如果数据未加载，会自动加载数据
  Future<DictItem?> getRepaymentTypeByValue(String value) async {
    await _ensureDataLoaded();
    return state.getRepaymentTypeByValue(value);
  }

  /// 根据value查询还款周期类型
  /// 
  /// 如果数据未加载，会自动加载数据
  Future<DictItem?> getLoanTermByValue(String value) async {
    await _ensureDataLoaded();
    return state.getLoanTermByValue(value);
  }

  /// 获取贷款类型列表
  /// 
  /// 如果数据未加载，会自动加载数据
  Future<List<DictItem>> getProductCidList() async {
    await _ensureDataLoaded();
    return state.productCidList;
  }

  /// 获取还款方式列表
  /// 
  /// 如果数据未加载，会自动加载数据
  Future<List<DictItem>> getRepaymentTypeList() async {
    await _ensureDataLoaded();
    return state.repaymentTypeList;
  }

  /// 获取还款周期列表
  /// 
  /// 如果数据未加载，会自动加载数据
  Future<List<DictItem>> getLoanTermList() async {
    await _ensureDataLoaded();
    return state.loanTermList;
  }

  /// 根据类型获取字典项列表
  /// 
  /// 如果数据未加载，会自动加载数据
  Future<List<DictItem>> getItemsByType(DictTypeEnum type) async {
    await _ensureDataLoaded();
    return state.getItemsByType(type);
  }

  /// 确保数据已加载
  Future<void> _ensureDataLoaded() async {
    if (!state.hasData || state.needsRefresh) {
      await _loadDictTypes();
    }
  }

  /// 清除缓存数据
  void clearCache() {
    state = const DictTypesState();
  }
}
