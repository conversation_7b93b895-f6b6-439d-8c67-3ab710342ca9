import 'package:zrreport/common/index.dart';

/// 字典类型API
class DictTypesApi {
  /// 获取字典类型数据
  /// 
  /// 请求多个字典类型：product_cid,repayment_type,loan_term_list
  /// - product_cid: 贷款类型
  /// - repayment_type: 还款方式类型  
  /// - loan_term_list: 还款周期类型
  static Future<DictTypesResponse> getDictTypes() async {
    const dictTypes = 'product_cid,repayment_type,loan_term_list';
    final response = await SXHttpService.to.get('/dict/data/getDictTypes/$dictTypes');
    return createDictTypesResponse(response.data);
  }

  /// 获取单个字典类型数据
  /// 
  /// [dictType] 字典类型，如：product_cid, repayment_type, loan_term_list
  static Future<DictTypesResponse> getSingleDictType(String dictType) async {
    final response = await SXHttpService.to.get('/dict/data/getDictTypes/$dictType');
    return createDictTypesResponse(response.data);
  }

  /// 获取贷款类型字典
  static Future<DictTypesResponse> getProductCidDict() async {
    return getSingleDictType(DictTypeEnum.productCid.value);
  }

  /// 获取还款方式字典
  static Future<DictTypesResponse> getRepaymentTypeDict() async {
    return getSingleDictType(DictTypeEnum.repaymentType.value);
  }

  /// 获取还款周期字典
  static Future<DictTypesResponse> getLoanTermListDict() async {
    return getSingleDictType(DictTypeEnum.loanTermList.value);
  }
}
