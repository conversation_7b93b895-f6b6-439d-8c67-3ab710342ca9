/// 字典类型数据管理
/// 
/// 提供贷款类型、还款方式、还款周期等字典数据的统一管理
/// 
/// 主要功能：
/// - 自动缓存字典数据
/// - 按需加载数据
/// - 提供便捷的查询接口
/// - 支持数据刷新和清除缓存
/// 
/// 使用示例：
/// ```dart
/// // 在Widget中使用
/// final dictState = ref.watch(dictTypesNotifierProvider);
/// final notifier = ref.read(dictTypesNotifierProvider.notifier);
/// 
/// // 查询贷款类型
/// final productCid = await notifier.getProductCidByValue('1');
/// 
/// // 在非Widget环境中使用
/// DictTypesHelper.initialize(container);
/// final productCid = await DictTypesHelper.getProductCidByValue('1');
/// ```

// 导出所有公共接口
export 'models/dict_type_models.dart';
export 'api/dict_types_api.dart';
export 'dict_types_state.dart';
export 'dict_types_provider.dart';
export 'dict_types_helper.dart';
