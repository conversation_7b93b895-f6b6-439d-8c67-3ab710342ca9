// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dict_type_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_DictItem _$DictItemFromJson(Map<String, dynamic> json) => _DictItem(
      dictCode: json['dictCode'] as String,
      dictSort: (json['dictSort'] as num).toInt(),
      dictLabel: json['dictLabel'] as String,
      dictValue: json['dictValue'] as String,
      dictType: json['dictType'] as String,
      cssClass: json['cssClass'] as String?,
      listClass: json['listClass'] as String?,
      remark: json['remark'] as String?,
    );

Map<String, dynamic> _$DictItemToJson(_DictItem instance) => <String, dynamic>{
      'dictCode': instance.dictCode,
      'dictSort': instance.dictSort,
      'dictLabel': instance.dictLabel,
      'dictValue': instance.dictValue,
      'dictType': instance.dictType,
      'cssClass': instance.cssClass,
      'listClass': instance.listClass,
      'remark': instance.remark,
    };

_DictTypesData _$DictTypesDataFromJson(Map<String, dynamic> json) =>
    _DictTypesData(
      loanTermList: (json['loanTermList'] as List<dynamic>?)
              ?.map((e) => DictItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      repaymentType: (json['repaymentType'] as List<dynamic>?)
              ?.map((e) => DictItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      productCid: (json['productCid'] as List<dynamic>?)
              ?.map((e) => DictItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$DictTypesDataToJson(_DictTypesData instance) =>
    <String, dynamic>{
      'loanTermList': instance.loanTermList,
      'repaymentType': instance.repaymentType,
      'productCid': instance.productCid,
    };
