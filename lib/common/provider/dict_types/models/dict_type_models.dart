import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:zrreport/common/index.dart';

part 'dict_type_models.freezed.dart';
part 'dict_type_models.g.dart';

/// 字典项模型
@freezed
abstract class DictItem with _$DictItem {
  const factory DictItem({
    required String dictCode,
    required int dictSort,
    required String dictLabel,
    required String dictValue,
    required String dictType,
    String? cssClass,
    String? listClass,
    String? remark,
  }) = _DictItem;

  factory DictItem.fromJson(Map<String, dynamic> json) =>
      _$DictItemFromJson(json);
}

/// 字典类型数据模型
@freezed
abstract class DictTypesData with _$DictTypesData {
  const factory DictTypesData({
    @Default([]) List<DictItem> loanTermList,
    @Default([]) List<DictItem> repaymentType,
    @Default([]) List<DictItem> productCid,
  }) = _DictTypesData;

  factory DictTypesData.fromJson(Map<String, dynamic> json) =>
      _$DictTypesDataFromJson({
        'loanTermList': json['loan_term_list'] ?? [],
        'repaymentType': json['repayment_type'] ?? [],
        'productCid': json['product_cid'] ?? [],
      });
}

/// 字典类型响应模型
typedef DictTypesResponse = BaseResponse<DictTypesData>;

/// 创建字典类型响应的工厂方法
DictTypesResponse createDictTypesResponse(dynamic json) {
  return BaseResponse.fromJson(
    json,
    (data) => DictTypesData.fromJson(data as Map<String, dynamic>),
  );
}

/// 字典类型枚举
enum DictTypeEnum {
  /// 贷款类型
  productCid('product_cid'),
  /// 还款方式类型
  repaymentType('repayment_type'),
  /// 还款周期类型
  loanTermList('loan_term_list');

  const DictTypeEnum(this.value);
  final String value;
}

/// 扩展方法
extension DictTypesDataX on DictTypesData {
  /// 根据类型获取字典项列表
  List<DictItem> getItemsByType(DictTypeEnum type) {
    switch (type) {
      case DictTypeEnum.productCid:
        return productCid;
      case DictTypeEnum.repaymentType:
        return repaymentType;
      case DictTypeEnum.loanTermList:
        return loanTermList;
    }
  }

  /// 根据value查询贷款类型
  DictItem? getProductCidByValue(String value) {
    try {
      return productCid.firstWhere((item) => item.dictValue == value);
    } catch (e) {
      return null;
    }
  }

  /// 根据value查询还款方式类型
  DictItem? getRepaymentTypeByValue(String value) {
    try {
      return repaymentType.firstWhere((item) => item.dictValue == value);
    } catch (e) {
      return null;
    }
  }

  /// 根据value查询还款周期类型
  DictItem? getLoanTermByValue(String value) {
    try {
      return loanTermList.firstWhere((item) => item.dictValue == value);
    } catch (e) {
      return null;
    }
  }

  /// 检查是否为空
  bool get isEmpty => 
      productCid.isEmpty && repaymentType.isEmpty && loanTermList.isEmpty;

  /// 检查是否有数据
  bool get isNotEmpty => !isEmpty;
}
