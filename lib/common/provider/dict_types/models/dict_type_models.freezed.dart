// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'dict_type_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DictItem {
  String get dictCode;
  int get dictSort;
  String get dictLabel;
  String get dictValue;
  String get dictType;
  String? get cssClass;
  String? get listClass;
  String? get remark;

  /// Create a copy of DictItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DictItemCopyWith<DictItem> get copyWith =>
      _$DictItemCopyWithImpl<DictItem>(this as DictItem, _$identity);

  /// Serializes this DictItem to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DictItem &&
            (identical(other.dictCode, dictCode) ||
                other.dictCode == dictCode) &&
            (identical(other.dictSort, dictSort) ||
                other.dictSort == dictSort) &&
            (identical(other.dictLabel, dictLabel) ||
                other.dictLabel == dictLabel) &&
            (identical(other.dictValue, dictValue) ||
                other.dictValue == dictValue) &&
            (identical(other.dictType, dictType) ||
                other.dictType == dictType) &&
            (identical(other.cssClass, cssClass) ||
                other.cssClass == cssClass) &&
            (identical(other.listClass, listClass) ||
                other.listClass == listClass) &&
            (identical(other.remark, remark) || other.remark == remark));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, dictCode, dictSort, dictLabel,
      dictValue, dictType, cssClass, listClass, remark);

  @override
  String toString() {
    return 'DictItem(dictCode: $dictCode, dictSort: $dictSort, dictLabel: $dictLabel, dictValue: $dictValue, dictType: $dictType, cssClass: $cssClass, listClass: $listClass, remark: $remark)';
  }
}

/// @nodoc
abstract mixin class $DictItemCopyWith<$Res> {
  factory $DictItemCopyWith(DictItem value, $Res Function(DictItem) _then) =
      _$DictItemCopyWithImpl;
  @useResult
  $Res call(
      {String dictCode,
      int dictSort,
      String dictLabel,
      String dictValue,
      String dictType,
      String? cssClass,
      String? listClass,
      String? remark});
}

/// @nodoc
class _$DictItemCopyWithImpl<$Res> implements $DictItemCopyWith<$Res> {
  _$DictItemCopyWithImpl(this._self, this._then);

  final DictItem _self;
  final $Res Function(DictItem) _then;

  /// Create a copy of DictItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dictCode = null,
    Object? dictSort = null,
    Object? dictLabel = null,
    Object? dictValue = null,
    Object? dictType = null,
    Object? cssClass = freezed,
    Object? listClass = freezed,
    Object? remark = freezed,
  }) {
    return _then(_self.copyWith(
      dictCode: null == dictCode
          ? _self.dictCode
          : dictCode // ignore: cast_nullable_to_non_nullable
              as String,
      dictSort: null == dictSort
          ? _self.dictSort
          : dictSort // ignore: cast_nullable_to_non_nullable
              as int,
      dictLabel: null == dictLabel
          ? _self.dictLabel
          : dictLabel // ignore: cast_nullable_to_non_nullable
              as String,
      dictValue: null == dictValue
          ? _self.dictValue
          : dictValue // ignore: cast_nullable_to_non_nullable
              as String,
      dictType: null == dictType
          ? _self.dictType
          : dictType // ignore: cast_nullable_to_non_nullable
              as String,
      cssClass: freezed == cssClass
          ? _self.cssClass
          : cssClass // ignore: cast_nullable_to_non_nullable
              as String?,
      listClass: freezed == listClass
          ? _self.listClass
          : listClass // ignore: cast_nullable_to_non_nullable
              as String?,
      remark: freezed == remark
          ? _self.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _DictItem implements DictItem {
  const _DictItem(
      {required this.dictCode,
      required this.dictSort,
      required this.dictLabel,
      required this.dictValue,
      required this.dictType,
      this.cssClass,
      this.listClass,
      this.remark});
  factory _DictItem.fromJson(Map<String, dynamic> json) =>
      _$DictItemFromJson(json);

  @override
  final String dictCode;
  @override
  final int dictSort;
  @override
  final String dictLabel;
  @override
  final String dictValue;
  @override
  final String dictType;
  @override
  final String? cssClass;
  @override
  final String? listClass;
  @override
  final String? remark;

  /// Create a copy of DictItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DictItemCopyWith<_DictItem> get copyWith =>
      __$DictItemCopyWithImpl<_DictItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$DictItemToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DictItem &&
            (identical(other.dictCode, dictCode) ||
                other.dictCode == dictCode) &&
            (identical(other.dictSort, dictSort) ||
                other.dictSort == dictSort) &&
            (identical(other.dictLabel, dictLabel) ||
                other.dictLabel == dictLabel) &&
            (identical(other.dictValue, dictValue) ||
                other.dictValue == dictValue) &&
            (identical(other.dictType, dictType) ||
                other.dictType == dictType) &&
            (identical(other.cssClass, cssClass) ||
                other.cssClass == cssClass) &&
            (identical(other.listClass, listClass) ||
                other.listClass == listClass) &&
            (identical(other.remark, remark) || other.remark == remark));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, dictCode, dictSort, dictLabel,
      dictValue, dictType, cssClass, listClass, remark);

  @override
  String toString() {
    return 'DictItem(dictCode: $dictCode, dictSort: $dictSort, dictLabel: $dictLabel, dictValue: $dictValue, dictType: $dictType, cssClass: $cssClass, listClass: $listClass, remark: $remark)';
  }
}

/// @nodoc
abstract mixin class _$DictItemCopyWith<$Res>
    implements $DictItemCopyWith<$Res> {
  factory _$DictItemCopyWith(_DictItem value, $Res Function(_DictItem) _then) =
      __$DictItemCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String dictCode,
      int dictSort,
      String dictLabel,
      String dictValue,
      String dictType,
      String? cssClass,
      String? listClass,
      String? remark});
}

/// @nodoc
class __$DictItemCopyWithImpl<$Res> implements _$DictItemCopyWith<$Res> {
  __$DictItemCopyWithImpl(this._self, this._then);

  final _DictItem _self;
  final $Res Function(_DictItem) _then;

  /// Create a copy of DictItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? dictCode = null,
    Object? dictSort = null,
    Object? dictLabel = null,
    Object? dictValue = null,
    Object? dictType = null,
    Object? cssClass = freezed,
    Object? listClass = freezed,
    Object? remark = freezed,
  }) {
    return _then(_DictItem(
      dictCode: null == dictCode
          ? _self.dictCode
          : dictCode // ignore: cast_nullable_to_non_nullable
              as String,
      dictSort: null == dictSort
          ? _self.dictSort
          : dictSort // ignore: cast_nullable_to_non_nullable
              as int,
      dictLabel: null == dictLabel
          ? _self.dictLabel
          : dictLabel // ignore: cast_nullable_to_non_nullable
              as String,
      dictValue: null == dictValue
          ? _self.dictValue
          : dictValue // ignore: cast_nullable_to_non_nullable
              as String,
      dictType: null == dictType
          ? _self.dictType
          : dictType // ignore: cast_nullable_to_non_nullable
              as String,
      cssClass: freezed == cssClass
          ? _self.cssClass
          : cssClass // ignore: cast_nullable_to_non_nullable
              as String?,
      listClass: freezed == listClass
          ? _self.listClass
          : listClass // ignore: cast_nullable_to_non_nullable
              as String?,
      remark: freezed == remark
          ? _self.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$DictTypesData {
  List<DictItem> get loanTermList;
  List<DictItem> get repaymentType;
  List<DictItem> get productCid;

  /// Create a copy of DictTypesData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DictTypesDataCopyWith<DictTypesData> get copyWith =>
      _$DictTypesDataCopyWithImpl<DictTypesData>(
          this as DictTypesData, _$identity);

  /// Serializes this DictTypesData to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DictTypesData &&
            const DeepCollectionEquality()
                .equals(other.loanTermList, loanTermList) &&
            const DeepCollectionEquality()
                .equals(other.repaymentType, repaymentType) &&
            const DeepCollectionEquality()
                .equals(other.productCid, productCid));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(loanTermList),
      const DeepCollectionEquality().hash(repaymentType),
      const DeepCollectionEquality().hash(productCid));

  @override
  String toString() {
    return 'DictTypesData(loanTermList: $loanTermList, repaymentType: $repaymentType, productCid: $productCid)';
  }
}

/// @nodoc
abstract mixin class $DictTypesDataCopyWith<$Res> {
  factory $DictTypesDataCopyWith(
          DictTypesData value, $Res Function(DictTypesData) _then) =
      _$DictTypesDataCopyWithImpl;
  @useResult
  $Res call(
      {List<DictItem> loanTermList,
      List<DictItem> repaymentType,
      List<DictItem> productCid});
}

/// @nodoc
class _$DictTypesDataCopyWithImpl<$Res>
    implements $DictTypesDataCopyWith<$Res> {
  _$DictTypesDataCopyWithImpl(this._self, this._then);

  final DictTypesData _self;
  final $Res Function(DictTypesData) _then;

  /// Create a copy of DictTypesData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loanTermList = null,
    Object? repaymentType = null,
    Object? productCid = null,
  }) {
    return _then(_self.copyWith(
      loanTermList: null == loanTermList
          ? _self.loanTermList
          : loanTermList // ignore: cast_nullable_to_non_nullable
              as List<DictItem>,
      repaymentType: null == repaymentType
          ? _self.repaymentType
          : repaymentType // ignore: cast_nullable_to_non_nullable
              as List<DictItem>,
      productCid: null == productCid
          ? _self.productCid
          : productCid // ignore: cast_nullable_to_non_nullable
              as List<DictItem>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _DictTypesData implements DictTypesData {
  const _DictTypesData(
      {final List<DictItem> loanTermList = const [],
      final List<DictItem> repaymentType = const [],
      final List<DictItem> productCid = const []})
      : _loanTermList = loanTermList,
        _repaymentType = repaymentType,
        _productCid = productCid;
  factory _DictTypesData.fromJson(Map<String, dynamic> json) =>
      _$DictTypesDataFromJson(json);

  final List<DictItem> _loanTermList;
  @override
  @JsonKey()
  List<DictItem> get loanTermList {
    if (_loanTermList is EqualUnmodifiableListView) return _loanTermList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_loanTermList);
  }

  final List<DictItem> _repaymentType;
  @override
  @JsonKey()
  List<DictItem> get repaymentType {
    if (_repaymentType is EqualUnmodifiableListView) return _repaymentType;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_repaymentType);
  }

  final List<DictItem> _productCid;
  @override
  @JsonKey()
  List<DictItem> get productCid {
    if (_productCid is EqualUnmodifiableListView) return _productCid;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_productCid);
  }

  /// Create a copy of DictTypesData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DictTypesDataCopyWith<_DictTypesData> get copyWith =>
      __$DictTypesDataCopyWithImpl<_DictTypesData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$DictTypesDataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DictTypesData &&
            const DeepCollectionEquality()
                .equals(other._loanTermList, _loanTermList) &&
            const DeepCollectionEquality()
                .equals(other._repaymentType, _repaymentType) &&
            const DeepCollectionEquality()
                .equals(other._productCid, _productCid));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_loanTermList),
      const DeepCollectionEquality().hash(_repaymentType),
      const DeepCollectionEquality().hash(_productCid));

  @override
  String toString() {
    return 'DictTypesData(loanTermList: $loanTermList, repaymentType: $repaymentType, productCid: $productCid)';
  }
}

/// @nodoc
abstract mixin class _$DictTypesDataCopyWith<$Res>
    implements $DictTypesDataCopyWith<$Res> {
  factory _$DictTypesDataCopyWith(
          _DictTypesData value, $Res Function(_DictTypesData) _then) =
      __$DictTypesDataCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<DictItem> loanTermList,
      List<DictItem> repaymentType,
      List<DictItem> productCid});
}

/// @nodoc
class __$DictTypesDataCopyWithImpl<$Res>
    implements _$DictTypesDataCopyWith<$Res> {
  __$DictTypesDataCopyWithImpl(this._self, this._then);

  final _DictTypesData _self;
  final $Res Function(_DictTypesData) _then;

  /// Create a copy of DictTypesData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? loanTermList = null,
    Object? repaymentType = null,
    Object? productCid = null,
  }) {
    return _then(_DictTypesData(
      loanTermList: null == loanTermList
          ? _self._loanTermList
          : loanTermList // ignore: cast_nullable_to_non_nullable
              as List<DictItem>,
      repaymentType: null == repaymentType
          ? _self._repaymentType
          : repaymentType // ignore: cast_nullable_to_non_nullable
              as List<DictItem>,
      productCid: null == productCid
          ? _self._productCid
          : productCid // ignore: cast_nullable_to_non_nullable
              as List<DictItem>,
    ));
  }
}

// dart format on
