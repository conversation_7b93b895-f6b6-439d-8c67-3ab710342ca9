// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'dict_types_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DictTypesState {
  /// 是否正在加载
  bool get isLoading;

  /// 字典数据
  DictTypesData? get data;

  /// 错误信息
  String? get error;

  /// 最后更新时间
  DateTime? get lastUpdated;

  /// 是否已初始化
  bool get isInitialized;

  /// Create a copy of DictTypesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DictTypesStateCopyWith<DictTypesState> get copyWith =>
      _$DictTypesStateCopyWithImpl<DictTypesState>(
          this as DictTypesState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DictTypesState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated) &&
            (identical(other.isInitialized, isInitialized) ||
                other.isInitialized == isInitialized));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, isLoading, data, error, lastUpdated, isInitialized);

  @override
  String toString() {
    return 'DictTypesState(isLoading: $isLoading, data: $data, error: $error, lastUpdated: $lastUpdated, isInitialized: $isInitialized)';
  }
}

/// @nodoc
abstract mixin class $DictTypesStateCopyWith<$Res> {
  factory $DictTypesStateCopyWith(
          DictTypesState value, $Res Function(DictTypesState) _then) =
      _$DictTypesStateCopyWithImpl;
  @useResult
  $Res call(
      {bool isLoading,
      DictTypesData? data,
      String? error,
      DateTime? lastUpdated,
      bool isInitialized});

  $DictTypesDataCopyWith<$Res>? get data;
}

/// @nodoc
class _$DictTypesStateCopyWithImpl<$Res>
    implements $DictTypesStateCopyWith<$Res> {
  _$DictTypesStateCopyWithImpl(this._self, this._then);

  final DictTypesState _self;
  final $Res Function(DictTypesState) _then;

  /// Create a copy of DictTypesState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? data = freezed,
    Object? error = freezed,
    Object? lastUpdated = freezed,
    Object? isInitialized = null,
  }) {
    return _then(_self.copyWith(
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      data: freezed == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as DictTypesData?,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      lastUpdated: freezed == lastUpdated
          ? _self.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isInitialized: null == isInitialized
          ? _self.isInitialized
          : isInitialized // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of DictTypesState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DictTypesDataCopyWith<$Res>? get data {
    if (_self.data == null) {
      return null;
    }

    return $DictTypesDataCopyWith<$Res>(_self.data!, (value) {
      return _then(_self.copyWith(data: value));
    });
  }
}

/// @nodoc

class _DictTypesState implements DictTypesState {
  const _DictTypesState(
      {this.isLoading = false,
      this.data,
      this.error,
      this.lastUpdated,
      this.isInitialized = false});

  /// 是否正在加载
  @override
  @JsonKey()
  final bool isLoading;

  /// 字典数据
  @override
  final DictTypesData? data;

  /// 错误信息
  @override
  final String? error;

  /// 最后更新时间
  @override
  final DateTime? lastUpdated;

  /// 是否已初始化
  @override
  @JsonKey()
  final bool isInitialized;

  /// Create a copy of DictTypesState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DictTypesStateCopyWith<_DictTypesState> get copyWith =>
      __$DictTypesStateCopyWithImpl<_DictTypesState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DictTypesState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated) &&
            (identical(other.isInitialized, isInitialized) ||
                other.isInitialized == isInitialized));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, isLoading, data, error, lastUpdated, isInitialized);

  @override
  String toString() {
    return 'DictTypesState(isLoading: $isLoading, data: $data, error: $error, lastUpdated: $lastUpdated, isInitialized: $isInitialized)';
  }
}

/// @nodoc
abstract mixin class _$DictTypesStateCopyWith<$Res>
    implements $DictTypesStateCopyWith<$Res> {
  factory _$DictTypesStateCopyWith(
          _DictTypesState value, $Res Function(_DictTypesState) _then) =
      __$DictTypesStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      DictTypesData? data,
      String? error,
      DateTime? lastUpdated,
      bool isInitialized});

  @override
  $DictTypesDataCopyWith<$Res>? get data;
}

/// @nodoc
class __$DictTypesStateCopyWithImpl<$Res>
    implements _$DictTypesStateCopyWith<$Res> {
  __$DictTypesStateCopyWithImpl(this._self, this._then);

  final _DictTypesState _self;
  final $Res Function(_DictTypesState) _then;

  /// Create a copy of DictTypesState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isLoading = null,
    Object? data = freezed,
    Object? error = freezed,
    Object? lastUpdated = freezed,
    Object? isInitialized = null,
  }) {
    return _then(_DictTypesState(
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      data: freezed == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as DictTypesData?,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      lastUpdated: freezed == lastUpdated
          ? _self.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isInitialized: null == isInitialized
          ? _self.isInitialized
          : isInitialized // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of DictTypesState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DictTypesDataCopyWith<$Res>? get data {
    if (_self.data == null) {
      return null;
    }

    return $DictTypesDataCopyWith<$Res>(_self.data!, (value) {
      return _then(_self.copyWith(data: value));
    });
  }
}

// dart format on
