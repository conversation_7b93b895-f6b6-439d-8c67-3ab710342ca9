import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dict_types_provider.dart';
import 'models/dict_type_models.dart';

/// 字典类型帮助类
///
/// 提供便捷的静态方法来访问字典数据
class DictTypesHelper {
  /// 私有构造函数，防止实例化
  DictTypesHelper._();

  /// 全局的ProviderContainer，用于在非Widget环境中访问Provider
  static ProviderContainer? _container;

  /// 初始化容器
  static void initialize(ProviderContainer container) {
    _container = container;
  }

  /// 获取字典类型管理器
  static DictTypesNotifier get _notifier {
    if (_container == null) {
      throw Exception('DictTypesHelper未初始化，请先调用initialize方法');
    }
    return _container!.read(dictTypesNotifierProvider.notifier);
  }

  /// 根据value查询贷款类型
  ///
  /// [value] 贷款类型值
  /// 返回对应的字典项，如果未找到返回null
  static Future<DictItem?> getProductCidByValue(String value) async {
    return await _notifier.getProductCidByValue(value);
  }

  /// 根据value查询还款方式类型
  ///
  /// [value] 还款方式值
  /// 返回对应的字典项，如果未找到返回null
  static Future<DictItem?> getRepaymentTypeByValue(String value) async {
    return await _notifier.getRepaymentTypeByValue(value);
  }

  /// 根据value查询还款周期类型
  ///
  /// [value] 还款周期值
  /// 返回对应的字典项，如果未找到返回null
  static Future<DictItem?> getLoanTermByValue(String value) async {
    return await _notifier.getLoanTermByValue(value);
  }

  /// 获取贷款类型列表
  static Future<List<DictItem>> getProductCidList() async {
    return await _notifier.getProductCidList();
  }

  /// 获取还款方式列表
  static Future<List<DictItem>> getRepaymentTypeList() async {
    return await _notifier.getRepaymentTypeList();
  }

  /// 获取还款周期列表
  static Future<List<DictItem>> getLoanTermList() async {
    return await _notifier.getLoanTermList();
  }

  /// 根据类型获取字典项列表
  static Future<List<DictItem>> getItemsByType(DictTypeEnum type) async {
    return await _notifier.getItemsByType(type);
  }

  /// 初始化字典数据
  static Future<void> initializeDictData() async {
    await _notifier.initialize();
  }

  /// 刷新字典数据
  static Future<void> refresh() async {
    await _notifier.refresh();
  }

  /// 清除缓存
  static void clearCache() {
    _notifier.clearCache();
  }

  /// 格式化贷款类型显示文本
  ///
  /// [value] 贷款类型值
  /// [defaultText] 默认显示文本，当未找到对应字典项时显示
  static Future<String> formatProductCid(String value,
      {String defaultText = '未知类型'}) async {
    final item = await getProductCidByValue(value);
    return item?.dictLabel ?? defaultText;
  }

  /// 格式化还款方式显示文本
  ///
  /// [value] 还款方式值
  /// [defaultText] 默认显示文本，当未找到对应字典项时显示
  static Future<String> formatRepaymentType(String value,
      {String defaultText = '未知方式'}) async {
    try {
      final item = await getRepaymentTypeByValue(value);
      return item?.dictLabel ?? defaultText;
    } catch (e) {
      debugPrint("e:$e");
      return "";
    }
  }

  /// 格式化还款周期显示文本
  ///
  /// [value] 还款周期值
  /// [defaultText] 默认显示文本，当未找到对应字典项时显示
  static Future<String> formatLoanTerm(String value,
      {String defaultText = '未知周期'}) async {
    final item = await getLoanTermByValue(value);
    return item?.dictLabel ?? defaultText;
  }

  /// 格式化还款周期显示文本
  ///
  /// [value] 还款周期值
  /// [defaultText] 默认显示文本，当未找到对应字典项时显示
  static Future<String> formatLoanTerms(String values,
      {String defaultText = '未知周期'}) async {
    return values
        .split(',')
        .map((e) async => (await formatLoanTerm(e)))
        .toList()
        .join("/");
    // final item = await getLoanTermByValue(value);
    // return item?.dictLabel ?? defaultText;
  }

  /// 获取贷款类型图标URL
  ///
  /// [value] 贷款类型值
  /// 返回图标URL，如果未找到返回null
  static Future<String?> getProductCidIcon(String value) async {
    final item = await getProductCidByValue(value);
    return item?.remark; // remark字段存储图标URL
  }

  /// 批量格式化显示文本
  ///
  /// [values] 值列表
  /// [type] 字典类型
  /// [defaultText] 默认显示文本
  static Future<List<String>> batchFormat(
      List<String> values, DictTypeEnum type,
      {String defaultText = '未知'}) async {
    final results = <String>[];

    for (final value in values) {
      String text;
      switch (type) {
        case DictTypeEnum.productCid:
          text = await formatProductCid(value, defaultText: defaultText);
          break;
        case DictTypeEnum.repaymentType:
          text = await formatRepaymentType(value, defaultText: defaultText);
          break;
        case DictTypeEnum.loanTermList:
          text = await formatLoanTerm(value, defaultText: defaultText);
          break;
      }
      results.add(text);
    }

    return results;
  }
}
