import 'package:freezed_annotation/freezed_annotation.dart';
import 'models/dict_type_models.dart';

part 'dict_types_state.freezed.dart';

/// 字典类型状态
@freezed
abstract class DictTypesState with _$DictTypesState {
  const factory DictTypesState({
    /// 是否正在加载
    @Default(false) bool isLoading,
    
    /// 字典数据
    DictTypesData? data,
    
    /// 错误信息
    String? error,
    
    /// 最后更新时间
    DateTime? lastUpdated,
    
    /// 是否已初始化
    @Default(false) bool isInitialized,
  }) = _DictTypesState;
}

/// 扩展方法
extension DictTypesStateX on DictTypesState {
  /// 是否有数据
  bool get hasData => data != null && data!.isNotEmpty;
  
  /// 是否需要刷新数据（超过1小时）
  bool get needsRefresh {
    if (lastUpdated == null) return true;
    final now = DateTime.now();
    final difference = now.difference(lastUpdated!);
    return difference.inHours >= 1; // 1小时后需要刷新
  }
  
  /// 获取贷款类型列表
  List<DictItem> get productCidList => data?.productCid ?? [];
  
  /// 获取还款方式列表
  List<DictItem> get repaymentTypeList => data?.repaymentType ?? [];
  
  /// 获取还款周期列表
  List<DictItem> get loanTermList => data?.loanTermList ?? [];
  
  /// 根据value查询贷款类型
  DictItem? getProductCidByValue(String value) {
    return data?.getProductCidByValue(value);
  }
  
  /// 根据value查询还款方式类型
  DictItem? getRepaymentTypeByValue(String value) {
    return data?.getRepaymentTypeByValue(value);
  }
  
  /// 根据value查询还款周期类型
  DictItem? getLoanTermByValue(String value) {
    return data?.getLoanTermByValue(value);
  }
  
  /// 根据类型获取字典项列表
  List<DictItem> getItemsByType(DictTypeEnum type) {
    return data?.getItemsByType(type) ?? [];
  }
}
