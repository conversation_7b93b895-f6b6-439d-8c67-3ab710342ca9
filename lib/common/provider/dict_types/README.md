# 字典类型数据管理

## 📚 概述

字典类型数据管理模块提供了对贷款相关字典数据的统一管理，包括：

- **贷款类型** (product_cid): 税贷、发票贷、个人贷、信用贷、其他
- **还款方式** (repayment_type): 等额本息、等额本金、先息后本
- **还款周期** (loan_term_list): 各种期数和天数选项

## 🎯 核心特性

- ✅ **自动缓存**: 数据自动缓存，避免重复请求
- ✅ **按需加载**: 首次使用时自动加载数据
- ✅ **智能刷新**: 超过1小时自动刷新数据
- ✅ **全局访问**: 支持在Widget和非Widget环境中使用
- ✅ **类型安全**: 完整的TypeScript风格类型定义
- ✅ **错误处理**: 完善的错误处理和重试机制

## 📦 API接口

### 数据源
```
GET https://manage.shengdaiqifu.com/portal/dict/data/getDictTypes/product_cid,repayment_type,loan_term_list
```

### 返回数据结构
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "product_cid": [...],
    "repayment_type": [...],
    "loan_term_list": [...]
  }
}
```

## 🚀 使用方法

### 1. 在Widget中使用

```dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/provider/dict_types/index.dart';

class MyWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dictState = ref.watch(dictTypesNotifierProvider);
    final notifier = ref.read(dictTypesNotifierProvider.notifier);

    return FutureBuilder<DictItem?>(
      future: notifier.getProductCidByValue('1'),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return Text(snapshot.data?.dictLabel ?? '未知');
        }
        return CircularProgressIndicator();
      },
    );
  }
}
```

### 2. 在非Widget环境中使用

```dart
// 在main.dart中初始化
void main() {
  final container = ProviderContainer();
  DictTypesHelper.initialize(container);
  
  runApp(
    UncontrolledProviderScope(
      container: container,
      child: MyApp(),
    ),
  );
}

// 在任何地方使用
class SomeService {
  Future<String> getProductTypeName(String value) async {
    return await DictTypesHelper.formatProductCid(value);
  }
}
```

### 3. 查询接口

```dart
// 根据value查询具体项目
final productCid = await notifier.getProductCidByValue('1');
final repaymentType = await notifier.getRepaymentTypeByValue('10');
final loanTerm = await notifier.getLoanTermByValue('12');

// 获取完整列表
final productList = await notifier.getProductCidList();
final repaymentList = await notifier.getRepaymentTypeList();
final termList = await notifier.getLoanTermList();

// 格式化显示文本
final productName = await DictTypesHelper.formatProductCid('1'); // "税贷"
final repaymentName = await DictTypesHelper.formatRepaymentType('10'); // "等额本息"
final termName = await DictTypesHelper.formatLoanTerm('12'); // "12期"

// 获取图标URL（仅贷款类型有图标）
final iconUrl = await DictTypesHelper.getProductCidIcon('1');
```

### 4. 数据管理

```dart
// 初始化数据（可选，首次使用时会自动初始化）
await notifier.initialize();

// 强制刷新数据
await notifier.refresh();

// 清除缓存
notifier.clearCache();
```

## 📊 数据结构

### DictItem
```dart
class DictItem {
  final String dictCode;     // 字典编码
  final int dictSort;        // 排序
  final String dictLabel;    // 显示标签
  final String dictValue;    // 字典值
  final String dictType;     // 字典类型
  final String? cssClass;    // CSS类名
  final String? listClass;   // 列表类名
  final String? remark;      // 备注（贷款类型中存储图标URL）
}
```

### 字典类型枚举
```dart
enum DictTypeEnum {
  productCid,      // 贷款类型
  repaymentType,   // 还款方式
  loanTermList,    // 还款周期
}
```

## 🔄 缓存机制

- **自动缓存**: 数据获取后自动缓存在内存中
- **智能刷新**: 超过1小时自动刷新数据
- **按需加载**: 只有在需要时才加载数据
- **全局共享**: 整个应用共享同一份缓存数据

## ⚡ 性能优化

- **keepAlive**: Provider设置为keepAlive，不会被自动销毁
- **懒加载**: 只有在首次使用时才请求数据
- **批量请求**: 一次请求获取所有字典类型数据
- **内存缓存**: 避免重复的网络请求

## 🛠 错误处理

```dart
final dictState = ref.watch(dictTypesNotifierProvider);

if (dictState.isLoading) {
  // 显示加载状态
} else if (dictState.error != null) {
  // 显示错误信息
  Text('加载失败: ${dictState.error}');
} else if (dictState.hasData) {
  // 显示数据
}
```

## 📝 注意事项

1. **初始化**: 在非Widget环境中使用前需要调用`DictTypesHelper.initialize()`
2. **异步操作**: 所有查询方法都是异步的，需要使用`await`
3. **空值处理**: 查询不到数据时返回`null`，需要做空值检查
4. **网络依赖**: 首次使用需要网络连接获取数据
5. **缓存时效**: 数据缓存1小时，超时后会自动刷新

## 🧪 测试

```dart
// 测试数据加载
test('should load dict types data', () async {
  final container = ProviderContainer();
  final notifier = container.read(dictTypesNotifierProvider.notifier);
  
  await notifier.initialize();
  final state = container.read(dictTypesNotifierProvider);
  
  expect(state.hasData, true);
  expect(state.error, null);
});

// 测试查询功能
test('should find product cid by value', () async {
  final container = ProviderContainer();
  final notifier = container.read(dictTypesNotifierProvider.notifier);
  
  final item = await notifier.getProductCidByValue('1');
  expect(item?.dictLabel, '税贷');
});
```
