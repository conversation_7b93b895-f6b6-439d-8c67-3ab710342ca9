import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'index.dart';

/// 字典类型帮助类测试工具
/// 
/// 用于测试和验证字典类型帮助类的初始化和功能
class DictTypesHelperTest {
  /// 测试字典类型帮助类的初始化
  static Future<void> testInitialization() async {
    debugPrint('开始测试字典类型帮助类初始化...');
    
    try {
      // 测试格式化方法
      final productName = await DictTypesHelper.formatProductCid('1');
      debugPrint('贷款类型 1: $productName');
      
      final repaymentName = await DictTypesHelper.formatRepaymentType('10');
      debugPrint('还款方式 10: $repaymentName');
      
      final termName = await DictTypesHelper.formatLoanTerm('12');
      debugPrint('还款周期 12: $termName');
      
      // 测试列表获取
      final productList = await DictTypesHelper.getProductCidList();
      debugPrint('贷款类型列表长度: ${productList.length}');
      
      final repaymentList = await DictTypesHelper.getRepaymentTypeList();
      debugPrint('还款方式列表长度: ${repaymentList.length}');
      
      final termList = await DictTypesHelper.getLoanTermList();
      debugPrint('还款周期列表长度: ${termList.length}');
      
      debugPrint('字典类型帮助类测试完成！');
    } catch (e) {
      debugPrint('字典类型帮助类测试失败: $e');
    }
  }
  
  /// 测试批量格式化
  static Future<void> testBatchFormat() async {
    debugPrint('开始测试批量格式化...');
    
    try {
      final results = await DictTypesHelper.batchFormat(
        ['1', '2', '3'], 
        DictTypeEnum.productCid
      );
      debugPrint('批量格式化结果: $results');
    } catch (e) {
      debugPrint('批量格式化测试失败: $e');
    }
  }
}
