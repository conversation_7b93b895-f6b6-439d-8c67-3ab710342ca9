// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dict_types_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$dictTypesNotifierHash() => r'b11cc18831b10d39b157c1c8f2bd9c549d0e79f4';

/// 字典类型数据管理器
///
/// 这是一个全局的Provider，用于管理字典类型数据
/// 支持自动缓存和按需加载
///
/// Copied from [DictTypesNotifier].
@ProviderFor(DictTypesNotifier)
final dictTypesNotifierProvider =
    NotifierProvider<DictTypesNotifier, DictTypesState>.internal(
  DictTypesNotifier.new,
  name: r'dictTypesNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$dictTypesNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DictTypesNotifier = Notifier<DictTypesState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
