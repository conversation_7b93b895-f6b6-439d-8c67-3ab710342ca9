// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'usage_statistics_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$usageStatisticsNotifierHash() =>
    r'724d40ba5d460775c8f09dc24af033562dc56e94';

/// 用于获取和刷新使用统计数据的异步Notifier
///
/// Copied from [UsageStatisticsNotifier].
@ProviderFor(UsageStatisticsNotifier)
final usageStatisticsNotifierProvider = AsyncNotifierProvider<
    UsageStatisticsNotifier, UsageStatisticsModel>.internal(
  UsageStatisticsNotifier.new,
  name: r'usageStatisticsNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$usageStatisticsNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UsageStatisticsNotifier = AsyncNotifier<UsageStatisticsModel>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
