import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';

part 'usage_statistics_provider.g.dart';

/// 用于获取和刷新使用统计数据的异步Notifier
@Riverpod(keepAlive: true)
class UsageStatisticsNotifier extends _$UsageStatisticsNotifier {
  @override
  Future<UsageStatisticsModel> build() async {
    try {
      final resp = await QueryApi.usageStatistics();
      return resp.data!;
    } catch (e, st) {
      // 发生异常时，Riverpod会自动将其转为AsyncError状态
      throw e;
    }
  }

  /// 手动刷新数据
  Future<void> refresh() async {
    try {
      final resp = await QueryApi.usageStatistics();
      if (state.value != resp.data) {
        state = AsyncData(resp.data!);
      }
    } catch (e, st) {}
  }
}
