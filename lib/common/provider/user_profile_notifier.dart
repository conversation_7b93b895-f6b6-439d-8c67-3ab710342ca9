import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';

part 'user_profile_notifier.g.dart';

@Riverpod(keepAlive: true)
class UserProfileNotifier extends _$UserProfileNotifier {
  @override
  UserProfile? build() {
    return null;
  }

  Future<void> init() {
    return refreshUserProfile();
  }

  Future<void> refreshUserProfile() async {
    if (!UserService.to.isLogin) return;
    try {
      final resp = await UserApi.getUserDetail();
      if (resp.data == null) {
        throw Exception('User data is null');
      }
      state = resp.data!; 
    } catch (e) {
      defaultLogger.error('Error fetching user profile: $e');
    }
  }

  void updateNickName(String nickname) {
    state = state?.copyWith(nickname: nickname);
  }

  void updateAvatar(String avatarUrl) {
    state = state?.copyWith(icon: avatarUrl);
  }

  void updateProfile({required String nickname, required String avatarUrl}) {
    state = state?.copyWith(icon: avatarUrl, nickname: nickname);
  }

  void clearProfile() {
    state = null;
  }
}
