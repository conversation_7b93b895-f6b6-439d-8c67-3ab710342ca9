# ProvinceListProvider 使用说明

## 概述

`ProvinceListProvider` 是一个全局的省份列表数据提供者，使用 Riverpod 进行状态管理。它提供了省份数据的获取、缓存、查找和刷新功能。

## 特性

- ✅ **持久化**: 使用 `@Riverpod(keepAlive: true)` 确保provider一直存在，不会自动销毁
- ✅ **智能缓存**: 如果已经获取成功，直接返回缓存数据；如果失败，自动重新获取
- ✅ **错误处理**: 完善的错误处理和重试机制
- ✅ **状态查询**: 提供多种状态查询方法
- ✅ **ID查找**: 根据省份ID快速查找对应的Province对象

## API接口

### 数据源
使用 `QueryApi.listProvince()` 获取省份列表数据，接口路径为 `/tax/listProvince`。

### 响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "1",
      "name": "北京市",
      "pinyin": "beijing",
      "areaId": "110000",
      "code": "11",
      "status": 1,
      "deleteStatus": 0,
      "createBy": "system",
      "createTime": "2023-01-01",
      "updateBy": "system", 
      "updateTime": "2023-01-01",
      "invoiceStatus": "1",
      "taxStatus": "1",
      "taxLoginUrl": "https://example.com"
    }
  ]
}
```

## 使用方法

### 1. 基本使用

```dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/provider/province_list/province_list_provider.dart';

class MyWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final provinceListAsync = ref.watch(provinceListNotifierProvider);
    final provinceListNotifier = ref.read(provinceListNotifierProvider.notifier);
    
    return provinceListAsync.when(
      data: (provinces) {
        return ListView.builder(
          itemCount: provinces.length,
          itemBuilder: (context, index) {
            final province = provinces[index];
            return ListTile(
              title: Text(province.name ?? ''),
              subtitle: Text('ID: ${province.id}'),
            );
          },
        );
      },
      loading: () => CircularProgressIndicator(),
      error: (error, stack) => Text('加载失败: $error'),
    );
  }
}
```

### 2. 获取省份列表

```dart
// 智能获取：如果有缓存返回缓存，如果失败自动重试
final provinces = await ref.read(provinceListNotifierProvider.notifier).getProvinceList();

// 或者直接监听状态变化
ref.listen(provinceListNotifierProvider, (previous, next) {
  next.when(
    data: (provinces) => print('获取到 ${provinces.length} 个省份'),
    loading: () => print('正在加载...'),
    error: (error, stack) => print('加载失败: $error'),
  );
});
```

### 3. 根据ID查找省份

```dart
final notifier = ref.read(provinceListNotifierProvider.notifier);

// 查找指定ID的省份
final province = notifier.findProvinceById('1');
if (province != null) {
  print('找到省份: ${province.name}');
} else {
  print('未找到指定省份');
}
```

### 4. 刷新数据

```dart
final notifier = ref.read(provinceListNotifierProvider.notifier);

// 手动刷新数据
await notifier.refresh();
```

### 5. 状态查询

```dart
final notifier = ref.read(provinceListNotifierProvider.notifier);

// 检查各种状态
bool hasData = notifier.hasData;        // 是否已有数据
bool isLoading = notifier.isLoading;    // 是否正在加载
bool hasError = notifier.hasError;      // 是否有错误
String? error = notifier.errorMessage;  // 错误信息
```

## 完整示例

```dart
class ProvinceSelector extends ConsumerStatefulWidget {
  final Function(Province) onProvinceSelected;
  
  const ProvinceSelector({
    super.key,
    required this.onProvinceSelected,
  });

  @override
  ConsumerState<ProvinceSelector> createState() => _ProvinceSelectorState();
}

class _ProvinceSelectorState extends ConsumerState<ProvinceSelector> {
  Province? selectedProvince;

  @override
  Widget build(BuildContext context) {
    final provinceListAsync = ref.watch(provinceListNotifierProvider);
    final notifier = ref.read(provinceListNotifierProvider.notifier);

    return Column(
      children: [
        // 状态栏
        Container(
          padding: EdgeInsets.all(8),
          child: Row(
            children: [
              if (notifier.isLoading) 
                CircularProgressIndicator(strokeWidth: 2),
              Text('状态: ${notifier.hasData ? "已加载" : notifier.isLoading ? "加载中" : "未加载"}'),
              Spacer(),
              IconButton(
                icon: Icon(Icons.refresh),
                onPressed: () => notifier.refresh(),
              ),
            ],
          ),
        ),
        
        // 省份列表
        Expanded(
          child: provinceListAsync.when(
            data: (provinces) => ListView.builder(
              itemCount: provinces.length,
              itemBuilder: (context, index) {
                final province = provinces[index];
                return ListTile(
                  title: Text(province.name ?? ''),
                  subtitle: Text('${province.pinyin} (${province.id})'),
                  selected: selectedProvince?.id == province.id,
                  onTap: () {
                    setState(() {
                      selectedProvince = province;
                    });
                    widget.onProvinceSelected(province);
                  },
                );
              },
            ),
            loading: () => Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('加载失败: $error'),
                  ElevatedButton(
                    onPressed: () => notifier.refresh(),
                    child: Text('重试'),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
```

## 注意事项

1. **持久化**: 该provider使用 `keepAlive: true`，会一直保持在内存中，直到应用关闭
2. **网络依赖**: 首次使用需要网络连接获取数据
3. **错误处理**: 建议在UI中处理加载状态和错误状态
4. **性能**: 数据会被缓存，重复调用 `getProvinceList()` 不会重复请求网络

## 测试

运行以下命令进行测试：

```bash
flutter test test/province_list_provider_test.dart
```
