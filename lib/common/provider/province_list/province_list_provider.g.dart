// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'province_list_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$provinceListNotifierHash() =>
    r'1930c23913484d685fcb49b2701ebcbfce0d36c2';

/// 省份列表数据提供者
/// 这个provider会一直存在，不会自动销毁
///
/// Copied from [ProvinceListNotifier].
@ProviderFor(ProvinceListNotifier)
final provinceListNotifierProvider =
    AsyncNotifierProvider<ProvinceListNotifier, List<Province>>.internal(
  ProvinceListNotifier.new,
  name: r'provinceListNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$provinceListNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ProvinceListNotifier = AsyncNotifier<List<Province>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
