import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';

part 'province_list_provider.g.dart';

/// 省份列表数据提供者
/// 这个provider会一直存在，不会自动销毁
@Riverpod(keepAlive: true)
class ProvinceListNotifier extends _$ProvinceListNotifier {
  @override
  Future<List<Province>> build() async {
    return await _fetchProvinceList();
  }

  /// 获取省份列表数据
  Future<List<Province>> _fetchProvinceList() async {
    try {
      final response = await QueryApi.listProvince();

      if (response.code == 200 && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.message.isNotEmpty
            ? response.message
            : '获取省份列表失败');
      }
    } catch (e) {
      throw Exception('网络请求失败: $e');
    }
  }

  /// 刷新省份列表数据
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _fetchProvinceList());
  }

  /// 根据ID查找对应的Province
  /// 如果不存在返回null
  Province? findProvinceById(String id) {
    return state.whenOrNull(
      data: (provinces) {
        try {
          return provinces.firstWhere((province) => province.id == id);
        } catch (e) {
          return null;
        }
      },
    );
  }

  /// 获取省份列表
  /// 如果之前没有获取成功，就重新获取
  /// 如果已经获取成功，就返回已有数据
  Future<List<Province>> getProvinceList() async {
    return state.when(
      data: (provinces) {
        // 如果已经有数据，直接返回
        return Future.value(provinces);
      },
      loading: () {
        // 如果正在加载，等待加载完成
        return future;
      },
      error: (error, stackTrace) {
        // 如果之前获取失败，重新获取
        return refresh().then((_) {
          return state.when(
            data: (provinces) => provinces,
            loading: () => <Province>[],
            error: (error, stackTrace) => throw error,
          );
        });
      },
    );
  }

  /// 检查是否已经成功获取数据
  bool get hasData => state.hasValue && state.value != null;

  /// 检查是否正在加载
  bool get isLoading => state.isLoading;

  /// 检查是否有错误
  bool get hasError => state.hasError;

  /// 获取错误信息
  String? get errorMessage => state.error?.toString();
}