import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';

class EasyRefreshFixIn18 {
  static Header getHeader() {
    return ClassicHeader(
      dragText: '下拉刷新',
      armedText: '松开后开始刷新',
      readyText: '准备刷新',
      processingText: '刷新中...',
      processedText: '刷新成功',
      failedText: '刷新失败',
      noMoreText: '没有更多',
      messageText: '更新于 %T',
      showMessage: true,
      spacing: 16,
      mainAxisAlignment: MainAxisAlignment.center,
      succeededIcon: Icon(Icons.arrow_upward),
      failedIcon: Icon(Icons.arrow_downward),
      noMoreIcon: Icon(Icons.arrow_downward),
    );
  }

  static Footer getFooter() {
    return ClassicFooter(
      dragText: '上拉加载',
      armedText: '松开后开始加载',
      readyText: '准备加载',
      processingText: '加载中...',
      processedText: '加载完成',
      failedText: '加载失败',
      noMoreText: '没有更多',
      showText: true,
      messageText: '更新于 %T',
      showMessage: true,
    );
  }
}
