import 'package:flutter/material.dart';

class MyAutocomplete<T extends Object> extends StatefulWidget {
  const MyAutocomplete({
    Key? key,
    required this.optionsBuilder,
    this.displayStringForOption = RawAutocomplete.defaultStringForOption,
    this.fieldViewBuilder = _defaultFieldViewBuilder,
    this.onSelected,
    this.optionsViewBuilder,
    this.optionsMaxHeight = 200.0,
  }) : super(key: key);

  final AutocompleteOptionsBuilder<T> optionsBuilder;
  final AutocompleteOptionToString<T> displayStringForOption;
  final AutocompleteFieldViewBuilder fieldViewBuilder;
  final ValueChanged<T>? onSelected;
  final AutocompleteOptionsViewBuilder<T>? optionsViewBuilder;
  final double optionsMaxHeight;


  @override
  _MyAutocompleteState<T> createState() => _MyAutocompleteState<T>();

  static Widget _defaultFieldViewBuilder(
    BuildContext context,
    TextEditingController textEditingController,
    FocusNode focusNode,
    VoidCallback onFieldSubmitted,
  ) {
    return TextFormField(
      controller: textEditingController,
      focusNode: focusNode,
      decoration: const InputDecoration(
        border: OutlineInputBorder(),
      ),
      onFieldSubmitted: (String value) {
        onFieldSubmitted();
      },
    );
  }
}

class _MyAutocompleteState<T extends Object> extends State<MyAutocomplete<T>> {
  final TextEditingController _textEditingController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;

  Iterable<T> _options = Iterable<T>.empty();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        _updateOptions();
      } else {
        _hideOptions();
      }
    });

    _textEditingController.addListener(() {
      if (_focusNode.hasFocus) {
        _updateOptions();
      }
    });
  }

  @override
  void dispose() {
    _hideOptions();
    _textEditingController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  _updateOptions() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final options = await widget.optionsBuilder(
        TextEditingValue(text: _textEditingController.text),
      );

      if (!mounted) return;

      setState(() {
        _options = options;
        _isLoading = false;
      });

      if (_options.isNotEmpty) {
        _showOptions();
      } else {
        _hideOptions();
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });
      _hideOptions();
    }
  }

  void _showOptions() {
    _hideOptions();
    if (_options.isEmpty) return;

    final overlay = Overlay.of(context);
    _overlayEntry = OverlayEntry(
      builder: (context) {
        return _OptionsView<T>(
          layerLink: _layerLink,
          options: _options,
          isLoading: _isLoading,
          displayStringForOption: widget.displayStringForOption,
          onSelected: (T selection) {
            _hideOptions();
            widget.onSelected?.call(selection);
          },
          optionsViewBuilder: widget.optionsViewBuilder,
          maxHeight: widget.optionsMaxHeight,
        );
      },
    );

    overlay.insert(_overlayEntry!);
  }

  void _hideOptions() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _onFieldSubmitted() {
    // if (_options.isNotEmpty) {
    //   final firstOption = _options.first;
    //   widget.onSelected?.call(firstOption);
    // }
    // _hideOptions();
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: widget.fieldViewBuilder(
        context,
        _textEditingController,
        _focusNode,
        _onFieldSubmitted,
      ),
    );
  }
}

class _OptionsView<T extends Object> extends StatelessWidget {
  const _OptionsView({
    Key? key,
    required this.layerLink,
    required this.options,
    required this.isLoading,
    required this.displayStringForOption,
    required this.onSelected,
    required this.optionsViewBuilder,
    required this.maxHeight,
  }) : super(key: key);

  final LayerLink layerLink;
  final Iterable<T> options;
  final bool isLoading;
  final AutocompleteOptionToString<T> displayStringForOption;
  final ValueChanged<T> onSelected;
  final AutocompleteOptionsViewBuilder<T>? optionsViewBuilder;
  final double maxHeight;

  @override
  Widget build(BuildContext context) {
    return CompositedTransformFollower(
      link: layerLink,
      showWhenUnlinked: false,
      offset: Offset(0.0, 40 + 10.0),
      child: isLoading
          ? const Center(child: CircularProgressIndicator())
          : optionsViewBuilder != null
              ? optionsViewBuilder!(
                  context,
                  onSelected,
                  options,
                )
              : ListView.builder(
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  itemCount: options.length,
                  itemBuilder: (BuildContext context, int index) {
                    final T option = options.elementAt(index);
                    return InkWell(
                      onTap: () {
                        onSelected(option);
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(displayStringForOption(option)),
                      ),
                    );
                  },
                ),
    );
  }
}
