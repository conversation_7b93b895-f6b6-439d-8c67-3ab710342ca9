import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:zrreport/common/index.dart';

/// 登录/注册页面上，输入框组件
Widget buildTextFiledWithHeader(
    {String? svgPath,
    String? pngPath,
    required String header,
    Widget? headerSuffixIcon,
    bool clearable = true,
    required String hintText,
    required TextEditingController textEditingController,
    TextInputType keyboardType = TextInputType.phone}) {
  return [
    [
      if (svgPath != null) SvgPicture.asset(svgPath, width: 24, height: 24),
      if (pngPath != null) Image.asset(pngPath, width: 24, height: 24),
      SizedBox(width: 4),
      Text(header,
          style: TextStyle(
              color: AppColors.textColor1,
              fontSize: 15,
              fontWeight: FontWeight.w700)),
      Spacer(),
      if (headerSuffixIcon != null) headerSuffixIcon,
    ].toRow().height(36),
    ClearableTextfield(
      controller: textEditingController,
      hitText: hintText,
      keyboardType: keyboardType,
      clearable: clearable,
    ),
  ].toColumn();
}

/// 登录注册欢迎header
Widget buildBackground() {
  return Image.asset(
    AssetsImages.loginBackgroundPng,
    fit: BoxFit.cover,
  );
}

Widget buildTermView(bool isAgree, Function(bool) onChanged) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.start,
    children: [
      Checkbox(
        value: isAgree,
        shape: CircleBorder(),
        onChanged: (value) => onChanged(value ?? false),
      ).tightSize(30),
      RichText(
        maxLines: 2,
        text: TextSpan(
          style: const TextStyle(color: Colors.grey),
          children: [
            const TextSpan(text: '请您先阅读并同意'),
            TextSpan(
                text: '《用户服务协议》',
                style:
                    TextStyle(color: Get.context?.colors.primary, fontSize: 14),
                recognizer: TapGestureRecognizer()
                  ..onTap =
                      () => gotoWeb(UrlPath.userAgreement, title: '用户协议')),
            const TextSpan(text: '和'),
            TextSpan(
              text: '《隐私政策》',
              style:
                  TextStyle(color: Get.context?.colors.primary, fontSize: 14),
              recognizer: TapGestureRecognizer()
                ..onTap = () => gotoWeb(UrlPath.privacyPolicy, title: '隐私政策'),
            ),
          ],
        ),
      ).expanded(),
    ],
  );
}

Widget buildTermDialogView() {
  return Row(
    mainAxisAlignment: MainAxisAlignment.start,
    children: [
      SizedBox(width: 16),
      RichText(
        maxLines: 2,
        text: TextSpan(
          style: const TextStyle(color: Colors.grey),
          children: [
            const TextSpan(text: '请您先同意'),
            TextSpan(
                text: '《用户服务协议》',
                style: TextStyle(color: AppColors.primary, fontSize: 14),
                recognizer: TapGestureRecognizer()
                  ..onTap =
                      () => gotoWeb(UrlPath.userAgreement, title: '用户协议')),
            const TextSpan(text: '和'),
            TextSpan(
              text: '《隐私政策》',
              style: TextStyle(color: AppColors.primary, fontSize: 14),
              recognizer: TapGestureRecognizer()
                ..onTap = () => gotoWeb(UrlPath.privacyPolicy, title: '隐私政策'),
            ),
          ],
        ),
      ).expanded(),
      SizedBox(width: 16),
    ],
  );
}
