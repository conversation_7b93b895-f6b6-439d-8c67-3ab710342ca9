import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';

/// 监控一个Widget的可见和不可见。visibilityThreshold
class VisibilityMonitor extends StatefulWidget {
  final Widget child;
  final String? keyPrefix;
  final double visibilityThreshold;
  final ValueChanged<bool>? onVisibilityChanged;
  final ValueChanged<double>? onVisibilityFractionChanged;
  final VoidCallback? onBecomeVisible;
  final VoidCallback? onBecomeInvisible;

  const VisibilityMonitor({
    super.key,
    required this.child,
    this.keyPrefix,
    this.visibilityThreshold = 0.0,
    this.onVisibilityChanged,
    this.onVisibilityFractionChanged,
    this.onBecomeVisible,
    this.onBecomeInvisible,
  });

  @override
  // ignore: library_private_types_in_public_api
  _VisibilityMonitorState createState() => _VisibilityMonitorState();
}

class _VisibilityMonitorState extends State<VisibilityMonitor> {
  bool _isVisible = false;
  late String _visibilityKey;

  @override
  void initState() {
    super.initState();
    _visibilityKey = widget.keyPrefix != null
        ? '${widget.keyPrefix}-${UniqueKey()}'
        : UniqueKey().toString();

    // 可选：调整检测频率
    VisibilityDetectorController.instance.updateInterval =
        const Duration(milliseconds: 500);
  }

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: Key(_visibilityKey),
      onVisibilityChanged: (visibilityInfo) {
        final visibleFraction = visibilityInfo.visibleFraction;
        final isVisible = visibleFraction > widget.visibilityThreshold;

        widget.onVisibilityFractionChanged?.call(visibleFraction);

        if (isVisible != _isVisible) {
          _isVisible = isVisible;

          widget.onVisibilityChanged?.call(_isVisible);

          if (_isVisible) {
            widget.onBecomeVisible?.call();
          } else {
            widget.onBecomeInvisible?.call();
          }
        }
      },
      child: widget.child,
    );
  }
}
