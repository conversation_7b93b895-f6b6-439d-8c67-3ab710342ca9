import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';

class EmptyWidget extends StatelessWidget {
  final String text;
  final VoidCallback? onAttempt;
  const EmptyWidget(
      {super.key, this.text = '暂无数据', this.emptyImageWidget, this.onAttempt});

  final Widget? emptyImageWidget;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onAttempt,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            emptyImageWidget ??
                Image.asset(AssetsImages.emptyPng, width: 174, height: 174),
            Text(text,
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.textColor9,
                )),
          ],
        ),
      ),
    );
  }
}

class ErrorStatusWidget extends StatelessWidget {
  final String text;
  final VoidCallback onAttempt;
  const ErrorStatusWidget(
      {super.key, this.text = "加载失败，点击重试", required this.onAttempt});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onAttempt,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(AssetsImages.emptyPng, width: 174, height: 174),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(text,
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.textColor9,
                )),
          ),
        ],
      ),
    );
  }
}

class LoadingWidget extends StatelessWidget {
  const LoadingWidget({super.key, this.color});

  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: CircularProgressIndicator(
        color: color,
      ),
    );
  }
}

class NotLoginWidget extends StatelessWidget {
  final VoidCallback? onLoginAction;
  final String title;
  final String subTitle;
  const NotLoginWidget(
      {super.key,
      this.title = "您还暂未登录",
      this.subTitle = "登录账号查看精彩内容",
      this.onLoginAction});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onLoginAction,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                color: Colors.white,
                fontWeight: FontWeight.w600,
                height: 22.5 / 16,
              ),
            ),
            SizedBox(height: 8.5),
            Text(
              subTitle,
              style: TextStyle(
                fontSize: 13,
                color: Color(0xFFCBCBCB),
                height: 18.5 / 13,
              ),
            ),
            SizedBox(height: 71),
            buildFilledButton('登录',
                width: 165,
                backgroundColor: AppColors.orange,
                onPressed: onLoginAction)
          ],
        ),
      ),
    );
  }
}
