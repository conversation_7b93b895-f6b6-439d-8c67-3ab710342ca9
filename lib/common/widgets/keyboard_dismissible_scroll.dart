import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

/// 智能键盘管理组件
///
/// 功能包括：
/// 1. 滚动时自动关闭键盘（避免下拉刷新误触）
/// 2. 点击非输入框区域时关闭键盘
/// 使用方向判断来避免下拉刷新等操作误触关闭键盘
class KeyboardDismissibleScroll extends StatefulWidget {
  /// 子组件，通常是可滚动的组件
  final Widget child;

  /// 是否启用点击关闭键盘功能
  final bool enableTapToDismiss;

  /// 是否启用滚动关闭键盘功能
  final bool enableScrollToDismiss;

  const KeyboardDismissibleScroll({
    super.key,
    required this.child,
    this.enableTapToDismiss = true,
    this.enableScrollToDismiss = true,
  });

  @override
  State<KeyboardDismissibleScroll> createState() => _KeyboardDismissibleScrollState();
}

class _KeyboardDismissibleScrollState extends State<KeyboardDismissibleScroll> {
  /// 当前滚动方向
  ScrollDirection _scrollDirection = ScrollDirection.idle;

  @override
  Widget build(BuildContext context) {
    Widget child = widget.child;

    // 如果启用了滚动关闭功能，包装 NotificationListener
    if (widget.enableScrollToDismiss) {
      child = NotificationListener<ScrollNotification>(
        onNotification: (ScrollNotification notification) {
          // 只处理用户主动滚动的情况
          if (notification is UserScrollNotification) {
            // 当滚动方向从idle变为其他方向时，关闭键盘
            if (_scrollDirection == ScrollDirection.idle &&
                notification.direction != _scrollDirection) {
              // 关闭键盘
              FocusManager.instance.primaryFocus?.unfocus();
            }
            // 更新滚动方向
            _scrollDirection = notification.direction;
          }
          return false; // 不阻止事件继续传播
        },
        child: child,
      );
    }

    // 如果启用了点击关闭功能，包装 Listener
    if (widget.enableTapToDismiss) {
      child = Listener(
        onPointerDown: (event) {
          // 获取当前焦点
          final currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus &&
              currentFocus.focusedChild != null) {
            // 如果有焦点且不是主焦点，则关闭键盘
            currentFocus.unfocus();
          }
        },
        child: child,
      );
    }

    return child;
  }
}
