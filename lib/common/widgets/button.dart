import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';

/// 扁平圆角按钮
Widget buildFilledButton(
  String title, {
  VoidCallback? onPressed,
  double width = double.infinity,
  double height = 49,
  Color? backgroundColor,
  Color? fontColor,
  double fontSize = 18,
  FontWeight fontWeight = FontWeight.w400,
  double radius = 24.5,
}) {
  backgroundColor ??= AppColors.primary;
  fontColor ??= Colors.white;
  return SizedBox(
    width: width,
    height: height,
    child: TextButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        disabledBackgroundColor: backgroundColor,
        backgroundColor: backgroundColor,
        padding: EdgeInsets.symmetric(vertical: 0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radius),
        ),
      ),
      child: Text(
        title,
        style: TextStyle(
            color: fontColor, fontSize: fontSize, fontWeight: fontWeight),
      ),
    ),
  );
}
