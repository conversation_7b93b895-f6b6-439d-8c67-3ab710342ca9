import 'package:flutter/material.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:zrreport/common/index.dart';

/// 通用对话框
class CommonDialog extends StatelessWidget {
  const CommonDialog(
      {super.key,
      required this.title,
      required this.content,
      this.showTextField = false,
      this.textFieldHint,
      this.onTextChanged,
      this.textFieldController,
      this.cancelText = '否',
      this.confirmText = '清空',
      this.onCancel,
      this.onConfirm,
      this.child});

  /// 标题
  final String title;

  /// 内容
  final String content;

  /// 是否显示输入框
  final bool showTextField;

  /// 输入框提示文字
  final String? textFieldHint;

  /// 输入框内容变化回调
  final ValueChanged<String>? onTextChanged;

  /// 输入框控制器
  final TextEditingController? textFieldController;

  /// 取消按钮文字
  final String cancelText;

  /// 确认按钮文字
  final String confirmText;

  /// 取消回调
  final VoidCallback? onCancel;

  /// 确认回调
  final VoidCallback? onConfirm;

  /// 子组件
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(30),
      ),
      child: Container(
        width: 300.w,
        // color: AppColors.background,
        color: AppColors.background,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(height: 16),
            // 标题
            Text(
              title,
              style: TextStyle(
                  fontSize: 17,
                  color: AppColors.textColor1,
                  fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 4),
            Divider(color: AppColors.dividerColor, thickness: 0.5),
            // 内容
            child ??
                Text(content,
                        style: const TextStyle(
                            fontSize: 15, color: AppColors.textColor9),
                        textAlign: TextAlign.center)
                    .padding(top: 16, left: 8, right: 8),
            if (showTextField) ...[
              const SizedBox(height: 16),
              // 输入框
              _textField(),
            ],
            const SizedBox(height: 24),
            // 按钮
            Row(
              children: [
                _cancel(context),
                const SizedBox(width: 12),
                _confirm(context),
              ],
            ).paddingHorizontal(32),
            SizedBox(height: 26),
          ],
        ),
      ).clipRRect(all: 30),
    );
  }

  TextField _textField() {
    return TextField(
      controller: textFieldController,
      onChanged: onTextChanged,
      decoration: InputDecoration(
        hintText: textFieldHint,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: Colors.grey[300]!,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: Colors.grey[300]!,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(
            color: AppColors.dividerColor,
          ),
        ),
      ),
    );
  }

  Expanded _confirm(BuildContext context) {
    return Expanded(
      child: TextButton(
        onPressed: () {
          Navigator.of(context).pop();
          onConfirm?.call();
        },
        style: TextButton.styleFrom(
          backgroundColor: AppColors.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
        child: Text(
          confirmText,
          style: const TextStyle(
            fontSize: 16,
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Expanded _cancel(BuildContext context) {
    return Expanded(
      child: TextButton(
        onPressed: () {
          Navigator.of(context).pop();
          onCancel?.call();
        },
        style: TextButton.styleFrom(
          backgroundColor: AppColors.disableBackground,
          side: BorderSide(
            color: Color(0xFFD7D7D7),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
        child: Text(
          cancelText,
          style: const TextStyle(
            fontSize: 16,
            color: AppColors.textColor9,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}

/// 显示通用对话框
Future<bool?> showCommonDialog(BuildContext context,
    {required String title,
    required String content,
    bool showTextField = false,
    String? textFieldHint,
    ValueChanged<String>? onTextChanged,
    TextEditingController? textFieldController,
    String cancelText = '取消',
    String confirmText = '确定',
    VoidCallback? onCancel,
    VoidCallback? onConfirm,
    Widget? child}) {
  return showDialog<bool>(
    context: context,
    builder: (context) => CommonDialog(
        title: title,
        content: content,
        showTextField: showTextField,
        textFieldHint: textFieldHint,
        onTextChanged: onTextChanged,
        textFieldController: textFieldController,
        cancelText: cancelText,
        confirmText: confirmText,
        onCancel: onCancel,
        onConfirm: onConfirm,
        child: child),
  );
}

/// 通用底部对话框
class CommonBottomDialog extends StatelessWidget {
  const CommonBottomDialog({
    super.key,
    required this.title,
    required this.content,
    this.showTextField = false,
    this.textFieldHint,
    this.onTextChanged,
    this.textFieldController,
    this.cancelText = '否',
    this.confirmText = '清空',
    this.onCancel,
    this.onConfirm,
    this.child,
    this.hideCancelButton = false, // 新增参数，默认不隐藏取消按钮
    this.hideConfirmButton = false, // 新增参数，默认不隐藏确认按钮
  });

  /// 标题
  final String title;

  /// 内容
  final String content;

  /// 是否显示输入框
  final bool showTextField;

  /// 输入框提示文字
  final String? textFieldHint;

  /// 输入框内容变化回调
  final ValueChanged<String>? onTextChanged;

  /// 输入框控制器
  final TextEditingController? textFieldController;

  /// 取消按钮文字
  final String cancelText;

  /// 确认按钮文字
  final String confirmText;

  /// 取消回调
  final VoidCallback? onCancel;

  /// 确认回调
  final VoidCallback? onConfirm;

  /// 子组件
  final Widget? child;

  /// 是否隐藏取消按钮
  final bool hideCancelButton;

  /// 是否隐藏确认按钮
  final bool hideConfirmButton;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: 16),
          // 标题
          Text(
            title,
            style: TextStyle(
              fontSize: 17,
              color: AppColors.textColor1,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Divider(color: AppColors.dividerColor, thickness: 0.5),
          const SizedBox(height: 16),
          // 内容
          child ??
              Text(
                content,
                style: const TextStyle(
                  fontSize: 15,
                  color: AppColors.textColor9,
                ),
                textAlign: TextAlign.center,
              ),
          if (showTextField) ...[
            const SizedBox(height: 16),
            // 输入框
            _textField(),
          ],
          const SizedBox(height: 24),
          // 按钮
          if (!hideCancelButton || !hideConfirmButton) ...[
            Row(
              children: [
                if (!hideCancelButton) _cancel(context),
                if (!hideCancelButton && !hideConfirmButton)
                  const SizedBox(width: 12),
                if (!hideConfirmButton) _confirm(context),
              ],
            ).paddingHorizontal(32),
            SizedBox(height: 26),
          ],
        ],
      ),
    );
  }

  TextField _textField() {
    return TextField(
      controller: textFieldController,
      onChanged: onTextChanged,
      decoration: InputDecoration(
        hintText: textFieldHint,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: Colors.grey[300]!,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: Colors.grey[300]!,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(
            color: AppColors.dividerColor,
          ),
        ),
      ),
    );
  }

  Expanded _confirm(BuildContext context) {
    return Expanded(
      child: TextButton(
        onPressed: () {
          Navigator.of(context).pop();
          onConfirm?.call();
        },
        style: TextButton.styleFrom(
          backgroundColor: AppColors.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
        child: Text(
          confirmText,
          style: const TextStyle(
            fontSize: 16,
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Expanded _cancel(BuildContext context) {
    return Expanded(
      child: TextButton(
        onPressed: () {
          Navigator.of(context).pop();
          onCancel?.call();
        },
        style: TextButton.styleFrom(
          backgroundColor: AppColors.disableBackground,
          side: BorderSide(
            color: Color(0xFFD7D7D7),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
        child: Text(
          cancelText,
          style: const TextStyle(
            fontSize: 16,
            color: AppColors.textColor9,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}

/// 显示通用底部对话框
Future<bool?> showCommonBottomDialog(
  BuildContext context, {
  required String title,
  required String content,
  bool showTextField = false,
  String? textFieldHint,
  ValueChanged<String>? onTextChanged,
  TextEditingController? textFieldController,
  String cancelText = '取消',
  String confirmText = '确定',
  VoidCallback? onCancel,
  VoidCallback? onConfirm,
  Widget? child,
  bool hideCancelButton = false, // 新增参数，默认不隐藏取消按钮
  bool hideConfirmButton = false, // 新增参数，默认不隐藏确认按钮
}) {
  return showModalBottomSheet<bool>(
    context: context,
    backgroundColor: Colors.transparent,
    builder: (context) => CommonBottomDialog(
      title: title,
      content: content,
      showTextField: showTextField,
      textFieldHint: textFieldHint,
      onTextChanged: onTextChanged,
      textFieldController: textFieldController,
      cancelText: cancelText,
      confirmText: confirmText,
      onCancel: onCancel,
      onConfirm: onConfirm,
      child: child,
      hideCancelButton: hideCancelButton, // 传递参数
      hideConfirmButton: hideConfirmButton, // 传递参数
    ),
  );
}

/// 显示单选对话框
Future<T?> showRadioDialog<T>({
  required BuildContext context,
  String? title,
  required List<String> items,
  required int groupValue,
  required Function(int) onChanged,
  String? cancelText,
  String? confirmText,
}) {
  return showDialog<T>(
    context: context,
    builder: (context) => Dialog(
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (title != null) ...[
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
            ],
            ...items.asMap().entries.map((entry) => RadioListTile<int>(
                  title: Text(entry.value),
                  value: entry.key,
                  groupValue: groupValue,
                  onChanged: (value) {
                    if (value != null) {
                      onChanged(value);
                    }
                  },
                )),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (cancelText != null)
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(cancelText),
                  ),
                if (confirmText != null)
                  TextButton(
                    onPressed: () => Navigator.pop(context, groupValue),
                    child: Text(confirmText),
                  ),
              ],
            ),
          ],
        ),
      ),
    ),
  );
}

/// 使用示例:
/// ```dart
/// showCommonDialog(
///   context,
///   title: '聊天内容',
///   content: '是否清空聊天内容',
///   cancelText: '否',
///   confirmText: '清空',
///   onConfirm: () {
///     // 处理确认逻辑
///   },
/// );
/// ```
/// 
/// 带输入框的示例:
/// ```dart
/// final controller = TextEditingController();
/// showCommonDialog(
///   context,
///   title: '设置备注名',
///   content: '请输入备注名',
///   showTextField: true,
///   textFieldHint: '请输入备注名',
///   textFieldController: controller,
///   cancelText: '取消',
///   confirmText: '确定',
///   onConfirm: () {
///     print('输入的内容: ${controller.text}');
///   },
/// );
/// ``` 