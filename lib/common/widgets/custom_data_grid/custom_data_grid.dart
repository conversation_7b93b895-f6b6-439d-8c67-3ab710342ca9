import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

import 'dynamic_data_source.dart';
import 'section_header.dart';

class CustomDataGrid extends StatelessWidget {
  const CustomDataGrid({
    super.key,
    required this.title,
    required this.header,
    required this.tableData,
    this.horizalScrollEnable = false,
    this.frozenColumnsCount = 0,
    this.firstColumnWidth = double.nan,
  });

  final String title;
  final List<String> header;
  final List<List<String>> tableData;
  final bool horizalScrollEnable;
  final int frozenColumnsCount;
  final double firstColumnWidth;

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Column(
          children: [
            SectionHeader(title: title),
            SizedBox(height: 10),
            SfDataGrid(
              source: DynamicDataSource(
                data: tableData,
                columns: header,
                cellBuilder: (context, cell, index) {
                  return Container(
                    alignment: Alignment.center,
                    padding: EdgeInsets.symmetric(horizontal: 4),
                    color: Colors.red,
                    child: Text(
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      cell.value ?? '',
                    ),
                  );
                },
              ),
              frozenColumnsCount: frozenColumnsCount, // 第一列固定
              gridLinesVisibility: GridLinesVisibility.both,
              headerGridLinesVisibility: GridLinesVisibility.both,
              columnWidthMode: horizalScrollEnable
                  ? ColumnWidthMode.none
                  : ColumnWidthMode.fill,
              verticalScrollPhysics: NeverScrollableScrollPhysics(),
              shrinkWrapRows: true,
              showHorizontalScrollbar: false,
              columns: header.asMap().entries.map((entry) {
                final e = entry.value;
                return GridColumn(
                    columnName: e,
                    width: firstColumnWidth,
                    label: Container(
                        color: Color(0xffF1F5F9),
                        padding: EdgeInsets.all(16.0),
                        alignment: Alignment.center,
                        child: Text(
                          e,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        )));
              }).toList(),
              // onQueryRowHeight: (details) {
              //   return details.getIntrinsicRowHeight(details.rowIndex);
              // },
            ),
            if (tableData.isEmpty)
              Container(
                height: 50,
                decoration: BoxDecoration(
                  border: Border(
                    left: BorderSide(color: Color(0xffE5E7EB), width: 1),
                    right: BorderSide(color: Color(0xffE5E7EB), width: 1),
                    bottom: BorderSide(color: Color(0xffE5E7EB), width: 1),
                  ),
                ),
                child: Center(
                  child: Text("暂无数据"),
                ),
              ),
          ],
        ));
  }
}
