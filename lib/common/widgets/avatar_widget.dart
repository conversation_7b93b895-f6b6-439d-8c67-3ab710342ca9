import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:zrreport/common/index.dart';

/// 头像组件
class AvatarWidget extends StatelessWidget {
  final String? imageUrl;
  final double size;
  final double borderWidth;
  final Color borderColor;

  const AvatarWidget({
    Key? key,
    this.imageUrl,
    this.size = 60,
    this.borderWidth = 2,
      this.borderColor = Colors.white
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        border: Border.all(color: borderColor, width: borderWidth),
        borderRadius: BorderRadius.circular(size / 2),
      ),
      child: imageUrl?.isNotEmpty == true
          ? CachedNetworkImage(
                  imageUrl: imageUrl ?? "",
                  fit: BoxFit.cover,
                  placeholder: (context, url) => const Icon(Icons.person))
              .clipRRect(all: size / 2)
          : SizedBox(
              width: size,
              height: size,
              child: Image.asset(AssetsImages.defaultAvatarPng),
            ),
    );
  }
}
