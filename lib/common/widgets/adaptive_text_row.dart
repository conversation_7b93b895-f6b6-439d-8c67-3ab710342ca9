import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AdaptiveTextRow extends StatelessWidget {
  final String label;
  final String value;
  final bool isOdd;
  final bool canCopy;
  final int maxLines;

  const AdaptiveTextRow({
    super.key,
    required this.label,
    required this.value,
    this.isOdd = true,
    this.canCopy = false,
    this.maxLines = 2,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final textPainter = TextPainter(
          text: TextSpan(
            text: value,
            style: const TextStyle(fontSize: 16),
          ),
          maxLines: 1,
          textDirection: TextDirection.ltr,
        )..layout(maxWidth: constraints.maxWidth); 

        final isSingleLine = !textPainter.didExceedMaxLines;

        return Container(
          color: isOdd ? Color(0xffF1F5F9) : Colors.transparent,
          padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 10.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
               SizedBox(
              width: 120,
              child: Text(
                label,
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey[600],
                ),
              ),
            ),
              const SizedBox(width: 60),
              Expanded(
                child: Column(
                  crossAxisAlignment: isSingleLine 
                      ? CrossAxisAlignment.end 
                      : maxLines > 2 ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                  children: [
                     Row(
                      mainAxisAlignment: isSingleLine
                          ? MainAxisAlignment.end
                          : MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Flexible(
                          child: this.canCopy ?
                            RichText(
                            text: TextSpan(
                              style: TextStyle(fontSize: 13, fontWeight: FontWeight.w600, color: Colors.black),
                              children: [
                               TextSpan(text: value),
                                TextSpan(
                                  text: '  复制',
                                  style: TextStyle(color: Color(0xff276FF7)),
                                  recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    Clipboard.setData(ClipboardData(text: value));
                                      ScaffoldMessenger.of(context).showSnackBar(
                                        const SnackBar(content: Text('已复制到剪贴板')),
                                      );
                                  },
                                ),
                              ],
                            ),
                          )
                           : Text(
                            value,
                            maxLines: maxLines,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(fontSize: 13, fontWeight: FontWeight.w600, color: Colors.black),
                          ),
                        ),
                      ],
                    ),
                    // if (_needsShowAllButton(value, context))
                    //   GestureDetector(
                    //     onTap: () {
                    //       _showFullContentDialog(context, label, value);
                    //     },
                    //     child: const Padding(
                    //       padding: EdgeInsets.only(top: 4),
                    //       child: Text(
                    //         '全部',
                    //         style: TextStyle(
                    //           fontSize: 14,
                    //           color: Color(0xff276FF7),
                    //         ),
                    //       ),
                    //     ),
                    //   ),
                  ],
                ),
              )
            ],
          ),
        );
      },
    );
  }

  // bool _needsShowAllButton(String text, BuildContext context) {
  //   final textPainter = TextPainter(
  //     text: TextSpan(
  //       text: text,
  //       style: const TextStyle(fontSize: 16),
  //     ),
  //     maxLines: maxLines,
  //     textDirection: TextDirection.ltr,
  //   )..layout(maxWidth: MediaQuery.of(context).size.width - 180); // 减去标签、边距和复制按钮宽度
    
  //   return textPainter.didExceedMaxLines;
  // }

  // void _showFullContentDialog(BuildContext context, String title, String content) {
  //   showDialog(
  //     context: context,
  //     builder: (context) => AlertDialog(
  //       title: Text(title),
  //       content: SingleChildScrollView(
  //         child: Text(content),
  //       ),
  //       actions: [
  //         TextButton(
  //           onPressed: () => Navigator.pop(context),
  //           child: const Text('关闭'),
  //         ),
  //       ],
  //     ),
  //   );
  // }
}





