import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zrreport/common/index.dart';

// ignore: must_be_immutable
class ClearableTextfield extends StatefulWidget {
  ClearableTextfield({
    Key? key,
    required this.controller,
    required this.hitText,
    required this.keyboardType,
    this.clearable = true,
  }) : super(key: key);

  TextEditingController controller;
  String hitText;
  TextInputType keyboardType;

  bool clearable = true;

  @override
  _ClearableTextfieldState createState() => _ClearableTextfieldState();
}

class _ClearableTextfieldState extends State<ClearableTextfield> {
  @override
  void initState() {
    super.initState();

    widget.controller.addListener(listener);
  }

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: widget.controller,
      keyboardType: widget.keyboardType,
      obscureText: widget.keyboardType == TextInputType.visiblePassword,
      style: TextStyle(fontSize: 16, color: AppColors.textColor6),
      decoration: InputDecoration(
        suffixIcon:
            widget.clearable && widget.controller.text.isNotEmpty
            ? InkWell(
                onTap: () => widget.controller.clear(),
                child: Icon(Icons.close, color: Colors.grey))
            : SizedBox(),
        hintText: widget.hitText,
        hintStyle: TextStyle(color: Colors.grey[400], fontSize: 16),
        border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide.none),
        contentPadding: EdgeInsets.only(left: 0, right: 0, bottom: 10),
      ),
    ).border(
        bottom: 0.5, color: Get.context?.colors.divider ?? Colors.transparent);
  }

  void listener() {
    setState(() {});
  }

  @override
  void dispose() {
    super.dispose();
    widget.controller.removeListener(listener);
  }
}
