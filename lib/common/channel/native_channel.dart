import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:zrreport/common/index.dart';

const MethodChannel _channel = MethodChannel('com.shunshun.native_plugin');

class UploadVideoResult {
  final String videoId;
  final String coverUrl;
  UploadVideoResult(this.videoId, this.coverUrl);

  factory UploadVideoResult.fromMap(dynamic map) {
    return UploadVideoResult(map['video_id'], map['cover_url']);
  }
}
class NativeChannelBridge {
  static NativeChannelBridge share = NativeChannelBridge();

  String? channel;

  Future getAll() async {
    final result = await _channel.invokeMethod('getAll');
    defaultLogger.debug('$TAG getAll result:$result');
  }

  Future<String> getChannel() async {
    if (channel == null) {
      do {
        String envChannel = const String.fromEnvironment('CHANNEL');
        defaultLogger.debug('$TAG geChannel envChannel:$envChannel');
        if (envChannel.isNotEmpty) {
          channel = envChannel;
          break;
        }
        final result =
            await _channel.invokeMethod<String>('getChannel') ?? "unknow";
        defaultLogger.debug('$TAG geChannel result:$result');
        channel = result;
      } while (false);
    }
    return channel!;
  }

  Future<void> openVideoRecorder() async {
    await _channel.invokeMethod('openRecord');
  }

  static const String TAG = 'NativeChannelBridge';

  //设置上传视频通道
  Future<UploadVideoResult> setUploadVideoChannel(
      {required String video_path,
      required String token,
      required String accessKey,
      required String secretKey,
      required String spaceName}) async {
    final result = await _channel.invokeMethod('setUploadVideoChannel', {
      "video_path": video_path,
      "accessKey": accessKey,
      "secretKey": secretKey,
      "token": token,
      "spaceName": spaceName
    });
    debugPrint('$TAG setUploadVideoChannel result:$result');
    return UploadVideoResult.fromMap(result);
  }
}
