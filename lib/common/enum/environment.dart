import 'package:get/get.dart';

enum Environment {
  test(1),
  uat(2),
  prod(3);

  final int value;
  const Environment(this.value);

  final testBaseUrl = 'https://test-manage.julimeng.com';
  final uatBaseUrl = 'https://uat-manage.shengdaiqifu.com';
  final prodBaseUrl = 'https://manage.shengdaiqifu.com';

  String get baseUrl {
    switch (this) {
      case Environment.test:
        return testBaseUrl;
      case Environment.uat:
        return uatBaseUrl;
      case Environment.prod:
        return prodBaseUrl;
    }
  }

  String get homeUrl {
    switch (this) {
      case Environment.test:
        return 'https://test-manage.julimeng.com/3h5/#';
      case Environment.uat:
        return 'https://dev-manage.julimeng.com/3h5/#';
      case Environment.prod:
        return 'https://dev-manage.julimeng.com/3h5/#';
    }
  }

  static Environment? getType(int value) => Environment.values
      .firstWhereOrNull((activity) => activity.value == value);
}
