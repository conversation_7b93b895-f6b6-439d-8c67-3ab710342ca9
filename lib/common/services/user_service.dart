import 'package:get/get.dart';
import 'package:zrreport/common/index.dart';

/// 存储登录状态，以及用户信息
class UserService extends GetxService {
  static UserService get to => Get.find();

  // 是否登录
  bool get isLogin => _isLogin.value;
  RxBool get isLoginRx => _isLogin;
  final _isLogin = false.obs;


  String get token => _token;
  String _token = "";


  @override
  void onInit() {
    super.onInit();
    _token = StorageService.to.getString(_Constants.storageTokenKey);
    if (token.isNotEmpty) {
      _isLogin.value = true;

      LoginApi.refreshToken().then((value) {
        if (value.data != null) {
          saveToken(value.data!.tokenHead, value.data!.token);
        }
      }).catchError((error) {
        userLogger.error("refresh token error: $error");
      });
    }
  }

  saveToken(String tokenHeader, String token) {
    _token = '$tokenHeader$token';
    _isLogin.value = true;
    StorageService.to.setString(_Constants.storageTokenKey, _token);
  }

  Future<void> login(String phone, String password, String code) async {
    LoginResponseModel? loginResponse;
    if (code.isNotEmpty) {
      final response = await LoginApi.loginByMobile(
          LoginByMobileRequestEntity(phone: phone, code: code));
      userLogger.debug("login by mobile response:${response.data?.token}");
      loginResponse = response.data;
    } else {
      final response = await LoginApi.loginByPassword(
          LoginByPasswordRequestEntity(account: phone, password: password));
      userLogger.debug("login by password response:${response.data?.token}");
      loginResponse = response.data;
    }
    if (loginResponse == null) {
      throw ErrorModel(code: -1, message: "登录失败,获取token失败");
    }

    saveToken(loginResponse.tokenHead, loginResponse.token);
  }

  Future<void> logout() async {
    await StorageService.to.remove(_Constants.storageTokenKey);
    _token = '';
    _isLogin.value = false;
  }

  @override
  void onClose() {
    super.onClose();
  }
}

class _Constants extends GetxService {
  //
  static const storageTokenKey = 'token';
}

Future<bool> loginIfNotLogin() async {
  if (UserService.to.isLogin) {
    return true;
  } else {
    await Get.toNamed(RouteNames.systemLogin);
    return UserService.to.isLogin;
  }
}
