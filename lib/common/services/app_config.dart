import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:zrreport/common/index.dart';

import 'storage.dart';

class _Constant {
  static String environmentKey = 'app_config_environment_key';
  // static String homeUrlStorageKey = 'app_config_home_url_key';
}

// app的一些设置
class AppConfig extends GetxService {
  Future<AppConfig> init() async {
    final channel = await getChannel();
    if (channel == "develop" || channel == "pgyer") {
      isDevelopModel = true;
    }
    if (isDevelopModel) {
      var environmentValue = StorageService.to.getInt(_Constant.environmentKey);
      if (environmentValue != null) {
        final environmentType = Environment.getType(environmentValue);
        environment = environmentType ?? Environment.prod;
      } else {
        environment = Environment.test;
      }
    }
    return this;
  }

  bool isDevelopModel = false;

  Future<bool> switchEnvironment(Environment environment) {
    final result = StorageService.to
        .setInt('app_config_environment_key', environment.value);
    return result;
  }

  static AppConfig get to => Get.find();

  /// 运行的接口环境
  Environment environment = Environment.prod;

  String get baseUrl => environment.baseUrl;

  // 接口api
  String get apiUrl => '$baseUrl/portal';

  /// 首页地址
  String get homeUrl => environment.homeUrl;

  Future<String> getChannel() async {
    String envChannel = const String.fromEnvironment('CHANNEL');
    defaultLogger.debug('[AppConfig] envChannel:$envChannel');
    if (envChannel.isNotEmpty) {
      return envChannel;
    } else if (kDebugMode) {
      return 'develop';
    } else {
      return "appstore";
    }
  }
}
