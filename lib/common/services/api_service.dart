import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:dio/dio.dart';
import 'package:get/get.dart' hide Response, FormData, MultipartFile;
import 'package:zrreport/common/index.dart';

import 'package:crypto/crypto.dart';

import 'app_config.dart';

class SXHttpService extends GetxService {
  static SXHttpService get to => Get.find();
  late final Dio _dio;

  @override
  void onInit() {
    super.onInit();

    // 初始 dio
    var options = BaseOptions(
      baseUrl: AppConfig.to.apiUrl,
      connectTimeout: const Duration(seconds: 60),
      // 10000, // 10秒
      receiveTimeout: const Duration(seconds: 60),
      // 5000, // 5秒
      headers: {},
      contentType: 'application/json; charset=utf-8',
      responseType: ResponseType.json,
    );
    _dio = Dio(options);

    // 拦截器
    _dio.interceptors.add(RequestInterceptors());
    //logger拦截器
    // _dio.interceptors.add(LogInterceptor(
    //   request: true,
    //   requestHeader: true,
    //   requestBody: true,
    //   responseHeader: true,
    //   responseBody: true,
    // ));

    // _dio.interceptors.add(InterceptorsWrapper(
    //   onRequest: (options, handler) {
    //     final originalHeaders = options.headers;
    //     options.headers = {
    //       for (var entry in originalHeaders.entries)
    //         entry.key: entry.value, // 保持原样
    //     };
    //     return handler.next(options);
    //   },
    // ));
  }

  Future<Response> get(
    String url, {
    Map<String, dynamic>? params,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    Options requestOptions = options ?? Options();
    Response response = await _dio.get(
      url,
      queryParameters: params,
      options: requestOptions,
      cancelToken: cancelToken,
    );
    return response;
  }

  Future<Response> delete(
    String url, {
    dynamic data,
    Map<String, dynamic>? params,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    Options requestOptions = options ?? Options();
    Response response = await _dio.delete(
      url,
      data: data,
      queryParameters: params,
      options: requestOptions,
      cancelToken: cancelToken,
    );
    return response;
  }

  Future<Response> put(
    String url, {
    dynamic data,
    Map<String, dynamic>? params,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    Options requestOptions = options ?? Options();
    Response response = await _dio.put(
      url,
      data: data,
      queryParameters: params,
      options: requestOptions,
      cancelToken: cancelToken,
    );
    return response;
  }

  Future<Response> post(
    String url, {
    dynamic data,
    Map<String, dynamic>? params,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    var requestOptions = options ?? Options();
    Response response = await _dio.post(
      url,
      queryParameters: params,
      data: data ?? {},
      options: requestOptions,
      cancelToken: cancelToken,
    );
    return response;
  }

  Future<Response> uploadImage(
    String url, {
    String? imagePath,
    dynamic data,
    Map<String, dynamic>? params,
    Options? options,
    CancelToken? cancelToken,
    required ImageType imageType,
  }) async {
    // final result = await FlutterImageCompress.compressWithFile(
    //   imagePath ?? "",
    //   minWidth: 800,
    //   minHeight: 800,
    //   quality: 80,
    // );
    // String compressedPath = "";
    // if (result != null) {
    //   final tempDir = Directory.systemTemp;
    //   final compressedImage = File('${tempDir.path}/${UUIDCenter.uuid}.jpg');
    //   await compressedImage.writeAsBytes(result);
    //   compressedPath = compressedImage.path;
    // }

    FormData formData = FormData.fromMap({
      'type': imageType.value,
      'multipart': await MultipartFile.fromFile(imagePath!,
          contentType: DioMediaType.parse('image/jpeg'))
    });
    var requestOptions = options ?? Options();
    Response response = await _dio.post(
      url,
      queryParameters: params,
      data: formData,
      options: requestOptions,
      cancelToken: cancelToken,
    );
    return response;
  }
}

/// 拦截
class RequestInterceptors extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // 在这里处理请请求的公共参数
    if (UserService.to.token.isNotEmpty) {
      options.headers['Authorization'] = UserService.to.token;
    }

    if (options.method == "GET") {
      final sortedKeys = options.queryParameters.keys.toList();
      sortedKeys.sort();
      List<String> params = sortedKeys.where((key) {
        if (options.queryParameters[key] is String) {
          return (options.queryParameters[key] as String).isNotEmpty;
        } else if (options.queryParameters[key] is num) {
          return true;
        }
        return false;
      }).map((key) {
        return "$key=${options.queryParameters[key]}";
      }).toList();

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final nonce = UUIDCenter.uuid;
      final secret =
          "WQBymFuoi5okgiODmLSCbSIvT2PQla8ctWJxkDH7PEqBmfZmnTu8zeFRS4jAo7";

      // "topicRecommend=10&timestamp=1723527659212&nonce=ZQPGfPNe9DjVPcAHN8cfXOtuRza1Wp4Y&secret"
      // + "=WQBymFuoi5okgiODmLSCbSIvT2PQla8ctWJxkDH7PEqBmfZmnTu8zeFRS4jAo7"

      params.add("timestamp=$timestamp");
      params.add("nonce=$nonce");
      params.add("secret=$secret");

      String str = params.join("&");
      String sign = generateMd5(str).toUpperCase();

      options.headers["X-Timestamp"] = timestamp;
      options.headers["X-Nonce"] = nonce;
      options.headers["X-Sign"] = sign;
    }

    return handler.next(options);
  }

  String generateSecureRandomString(int length) {
    final random = Random.secure();
    final values = List<int>.generate(length, (i) => random.nextInt(256));
    return base64UrlEncode(values).substring(0, length);
  }

  String generateMd5(String input) {
    // Convert the input string to bytes using UTF-8 encoding
    final bytes = utf8.encode(input);
    // Generate the MD5 hash
    final digest = md5.convert(bytes);
    // Convert the hash to a hexadecimal string
    return digest.toString();
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // http原本的状态码
    if (response.statusCode == 200) {
      var data = response.data;
      if (data['code'] != 200) {
        handler.reject(
            DioException(
                requestOptions: response.requestOptions,
                response: response,
                type: DioExceptionType.badResponse),
            true);
      } else {
        handler.next(response);
      }
    } else {
      handler.reject(
        DioException(
          requestOptions: response.requestOptions,
          response: response,
          type: DioExceptionType.badResponse,
        ),
        true,
      );
    }
  }

  @override
  Future<void> onError(
      DioException err, ErrorInterceptorHandler handler) async {
    final response = err.response;
    switch (err.type) {
      case DioExceptionType.badResponse:
        // http 状态码 != 200。 服务端 code != 200触发
        if (response?.statusCode == 200) {
          // 业务层报错
          var baseResponse =
              BaseResponse.fromJson(response?.data, (data) => null);
          var errorModel = ErrorModel(
              code: baseResponse.code, message: baseResponse.message);
          err = err.copyWith(error: errorModel);

          if (baseResponse.code == 401) {
            /// 401 跳转登录
            _onTokenInvalid();
          }

          // 这里在UI层直接打印这个error.toString()就直接展示的接口返回的message, 方便一些
          err.stringBuilder = (DioException e) {
            if (e.error is ErrorModel) {
              return e.error.toString();
            }
            return e.toString();
          };
        } else {
          /// 测试发现， http code ！= 200 时，先看看是不是 response?.data 是不是 Map<String, dynamic>
          if (response?.data is Map<String, dynamic>) {
            try {
              var baseResponse =
                  BaseResponse.fromJson(response?.data, (data) => null);
              if (baseResponse.code == 401) {
                /// 401 跳转登录
                _onTokenInvalid();
              }
              var errorModel = ErrorModel(
                  code: baseResponse.code, message: baseResponse.message);
              err = err.copyWith(error: errorModel);
            } catch (e) {
              defaultLogger.debug("不可识别的错误，error:${e}");
            }
          }
        }
        break;
      case DioExceptionType.unknown:
        break;
      case DioExceptionType.cancel:
        break;
      case DioExceptionType.connectionTimeout:
        break;
      default:
        break;
    }
    handler.next(err);
  }

  // token失效
  Future<void> _onTokenInvalid() async {
    if (UserService.to.token.isNotEmpty) {
      Loading.toast('您的登录信息已失效');
      await UserService.to.logout();
    }
  }
}
