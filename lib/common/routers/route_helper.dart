import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/index.dart';

Future<void> gotoWeb(String path,
    {String title = '', bool showNavBar = false}) async {
  final url = "${AppConfig.to.homeUrl}/$path";
  await Get.toNamed(RouteNames.systemWeb,
      arguments: {"url": url, 'title': title, 'showNavigationBar': showNavBar});
}

