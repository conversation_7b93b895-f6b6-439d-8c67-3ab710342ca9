import "package:flutter/material.dart";

class AppColors {
  static const Color primary = Color(0xFF488AFD);
  static const Color grey = Color(0x33999999);
  static const Color lightGrey = Color(0x11BBBBBB);

static const Color white = Colors.white;
  static const Color background = Colors.white;
  // static const Color primary = Colors.red;
  static const Color chatBubbleHilightColor = Color(0xFF276FF7);
  static const Color chatBubbleNormalColor = Color(0xFFF5F5F5);

  static const Color dividerColor = Color(0xFFE5E5E5);

  static const Color surfaceTint = Colors.white;

  static const Color textColor1 = Color(0xFF111111);
  static const Color textColor3 = Color(0xFF333333);
  static const Color textColor6 = Color(0xFF666666);
  static const Color textColor8 = Color(0xFF888888);
  static const Color textColor9 = Color(0xFF999999);

  /// 对话框“取消”按钮背景色
  static const Color disableBackground = Color(0x44999999);

  static const Color orange = Color(0xFFFA7700);
}

ColorScheme lightColorScheme() {
  return const ColorScheme(
    brightness: Brightness.light,
    primary: AppColors.primary, // 主要颜色
    onPrimary: Colors.white, //
    secondary: Colors.green,
    onSecondary: Colors.white,
    surfaceTint: AppColors.surfaceTint,
    error: Colors.red,
    onError: Colors.white,
    surface: Colors.white, //影响Appbar背景
    onSurface: Colors.black,
    // outline: Colors.cyan,
    // onPrimaryContainer: Color(0xff001b3e),
    // secondaryContainer: Color(0xffdae2f9),
    // onSecondaryContainer: Color(0xff131c2b),
    // tertiary: Color(0xff705575),
    // onTertiary: Color(0xffffffff),
    // tertiaryContainer: Color(0xfffad8fd),
    // onTertiaryContainer: Color(0xff28132e),
    // error: Color(0xffba1a1a),
    // onError: Color(0xffffffff),
    // errorContainer: Color(0xffffdad6),
    // onErrorContainer: Color(0xff410002),
    // surface: Color(0xfff9f9ff),
    // onSurface: Color(0xff191c20),
    // onSurfaceVariant: Color(0xff44474e),
    // outline: Color(0xff74777f),
    // outlineVariant: Color(0xffc4c6d0),
    // shadow: Color(0xff000000),
    // scrim: Color(0xff000000),
    // inverseSurface: Color(0xff2e3036),
    // inversePrimary: Color(0xffaac7ff),
    // primaryFixed: Color(0xffd6e3ff),
    // onPrimaryFixed: Color(0xff001b3e),
    // primaryFixedDim: Color(0xffaac7ff),
    // onPrimaryFixedVariant: Color(0xff284777),
    // secondaryFixed: Color(0xffdae2f9),
    // onSecondaryFixed: Color(0xff131c2b),
    // secondaryFixedDim: Color(0xffbec6dc),
    // onSecondaryFixedVariant: Color(0xff3e4759),
    // tertiaryFixed: Color(0xfffad8fd),
    // onTertiaryFixed: Color(0xff28132e),
    // tertiaryFixedDim: Color(0xffddbce0),
    // onTertiaryFixedVariant: Color(0xff573e5c),
    // surfaceDim: Color(0xffd9d9e0),
    // surfaceBright: Color(0xfff9f9ff),
    // surfaceContainerLowest: Color(0xffffffff),
    // surfaceContainerLow: Color(0xfff3f3fa),
    // surfaceContainer: Color(0xffededf4),
    // surfaceContainerHigh: Color(0xffe7e8ee),
    // surfaceContainerHighest: Color(0xffe2e2e9),
  );
}
