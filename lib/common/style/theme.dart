import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'colors.dart';

/// 主题
class AppTheme {

  /// 系统样式
  static SystemUiOverlayStyle get systemStyle => const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent, // 状态栏颜色
        statusBarBrightness: Brightness.light, // 状态栏亮度
        statusBarIconBrightness: Brightness.dark, // 状态栏图标亮度
        systemNavigationBarDividerColor: Colors.transparent, // 系统导航栏分隔线颜色
        systemNavigationBarColor: Colors.white, // 系统导航栏颜色
        systemNavigationBarIconBrightness: Brightness.dark, // 系统导航栏图标亮度
      );

  /// 亮色系统样式
  static SystemUiOverlayStyle get systemStyleLight => systemStyle.copyWith(
        statusBarColor: Colors.transparent, // 状态栏颜色
        statusBarBrightness: Brightness.light, // 状态栏亮度
        statusBarIconBrightness: Brightness.dark, // 状态栏图标亮度
        systemNavigationBarIconBrightness: Brightness.dark, // 系统导航栏图标亮度
      );

  static void setSystemStyle() {
    SystemChrome.setSystemUIOverlayStyle(systemStyleLight);
  }


  /// 亮色主题
  static ThemeData get light {
    ColorScheme scheme = lightColorScheme();
    return _getTheme(scheme);
  }

  /// 获取主题
  static ThemeData _getTheme(ColorScheme scheme) {
    return ThemeData(
      useMaterial3: false,
      colorScheme: scheme,
      scaffoldBackgroundColor: AppColors.background,
      dividerColor: AppColors.dividerColor,
      primaryColor: AppColors.primary,
      disabledColor: AppColors.disableBackground,
      radioTheme: RadioThemeData(
        fillColor:
            WidgetStateProperty<Color>.fromMap(<WidgetStatesConstraint, Color>{
          WidgetState.selected: scheme.primary,
          WidgetState.any: AppColors.textColor9,
        }),
      ),
      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: Colors.white, // 背景色
        elevation: 0, // 阴影
      ),
      dialogTheme: DialogTheme(
        titleTextStyle: TextStyle(
          color: AppColors.textColor1,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
        contentTextStyle: TextStyle(
          color: AppColors.textColor1,
          fontSize: 14,
          fontWeight: FontWeight.w400,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
      tabBarTheme: TabBarTheme(
        labelColor: scheme.onSurface,
        unselectedLabelColor: scheme.onSurface.withOpacity(0.6),
        labelStyle: TextStyle(
          fontSize: 17,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(fontSize: 14),
      ),
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.all(Colors.transparent),
        checkColor:
            WidgetStateProperty<Color>.fromMap(<WidgetStatesConstraint, Color>{
          WidgetState.selected: scheme.primary,
          WidgetState.any: Colors.transparent,
        }),
          
      ),
      switchTheme: SwitchThemeData(
        thumbColor:
            WidgetStateProperty<Color>.fromMap(<WidgetStatesConstraint, Color>{
          WidgetState.selected: Colors.white,
          WidgetState.any: Colors.white,
        }),
        trackColor:
            WidgetStateProperty<Color>.fromMap(<WidgetStatesConstraint, Color>{
          WidgetState.selected: scheme.primary,
          WidgetState.any: Color(0xFFE6E6E6),
        }),
        // trackOutlineWidth: WidgetStateProperty.all(1),
        // trackOutlineColor: WidgetStateProperty.all(Colors.red),
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.white, // 背景
        // scrolledUnderElevation: 0, // 滚动阴影
        elevation: 0.1, // 阴影
        centerTitle: true, // 标题居中
        toolbarHeight: 56, // 高度
        iconTheme: IconThemeData(
          color: scheme.onSurface, 
          size: 22, // 图标大小
        ),
        titleTextStyle: TextStyle(
          color: AppColors.textColor1,
          fontSize: 18, // 字体大小
          fontWeight: FontWeight.w600, // 字体粗细
          height: 1.2, // 行高
        ),
        toolbarTextStyle: TextStyle(
          color: scheme.onSurface, 
          fontSize: 22, // 字体大小
          fontWeight: FontWeight.w600, // 字体粗细
          height: 1.2, // 行高
        ),
      ),
    );
  }
}
