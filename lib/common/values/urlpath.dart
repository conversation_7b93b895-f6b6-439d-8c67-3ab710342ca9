class UrlPath {

  /// 我的报告
  static const String myReport = 'pageUser/userReport/index';

  /// 客户管理
  static const String customerManagement = 'CustomerAddress/index';
  
  /// 我的订单
  static const String myOrder = 'order/list';

  /// 全部功能
  static const String allFunctions = 'pages/toolbox/index';

  /// 问答详情
  static const String questionDetail = 'postfixOpsimport/Square/detail?id=';

  /// 收藏的详情
  static const String articleDetail = 'postfixOpsimport/Postbodydelive?id=';

  /// 修改手机号
  static const String modifyPhone = 'pageUser/user/modifyPhone';

  /// 修改密码
  static const String replacePassword = 'pageUser/user/replacePassword';

  /// 意见反馈
  static const String feedBack = 'pageUser/user/feedBack';

  /// 关于我们  
  static const String aboutUs = 'pageUser/user/aboutUs';

  /// 隐私政策
  static const String privacyPolicy = 'pageUser/user/privacyPolicy';

  /// 用户协议
  static const String userAgreement = 'pages/privacy/index?privacyType=0';

  /// 粉丝列表
  static const String userInfo = 'pageUser/users/fan';

  /// 实名认证
  static const String livenessView = 'pageUser/livenessView/livenessView';

  /// 他人主页
  static const String otherHomePage = 'pageUser/otherUser/otherUser?uid=';

  /// 关注列表
  static const String attentionList = 'pageUser/users/fans';

  /// 系统通知
  static const String systemNotice = 'message/index?msgType=0';

  /// 点赞和收藏
  static const String likeAndCollect = 'message/index?msgType=1&eventType=like,collect';

  /// 评论和@
  static const String comments = 'message/index?msgType=1&eventType=comment';

  /// 我的关注
  static const String myFollows = 'pages/users/yourself?activeIndex=';

  /// 问答专区
  static const String qaSquare = 'postfixOpsimport/Square/index';

  /// 银行卡
  static const String bankCard = 'pageToolbox/BankCard/index';

  /// 还款提醒
  static const String returnMoney = 'pageToolbox/returnMoney/index';
  
}

class Urls {
  static const String weixinCustomerService =
      'https://work.weixin.qq.com/kfid/kfc389dffe7ae981936';
}
