
import 'package:zrreport/pages/system/edit_profile/profile_state.dart';

class UserProfile {
  final String? id;
  final String? memberId;
  final String? adminId;
  final String? deptId;
  final String? socialId;
  final int? userType;
  final String? username;
  final String? nickname;
  final String? realname;
  final String? phone;
  final String? cardId;
  final String? inviteCode;
  final int? status;
  final String? icon;
  final int? gender;
  final String? birthday;
  final String? province;
  final String? city;
  final String? job;
  final String? personalizedSignature;
  final int? sourceType;
  final String? lastIp;
  final String? lastLocation;
  final String? createTime;
  final String? lastLoginTime;
  final String? spreadUid;
  final int? spreadCount;
  final String? spreadTime;
  final String? officialAuthnName;
  final int? type;
  final int? faceAuthnStatus;
  final bool? followStatus;
  final String? backdropUrl;
  final int? collectionPublic;

  const UserProfile({
    this.id,
    this.memberId,
    this.adminId,
    this.deptId,
    this.socialId,
    this.userType,
    this.username,
    this.nickname,
    this.realname,
    this.phone,
    this.cardId,
    this.inviteCode,
    this.status,
    this.icon,
    this.gender,
    this.birthday,
    this.province,
    this.city,
    this.job,
    this.personalizedSignature,
    this.sourceType,
    this.lastIp,
    this.lastLocation,
    this.createTime,
    this.lastLoginTime,
    this.spreadUid,
    this.spreadCount,
    this.spreadTime,
    this.officialAuthnName,
    this.type,
    this.faceAuthnStatus,
    this.followStatus,
    this.backdropUrl,
    this.collectionPublic,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'] as String?,
      memberId: json['memberId'] as String?,
      adminId: json['adminId'] as String?,
      deptId: json['deptId'] as String?,
      socialId: json['socialId'] as String?,
      userType: json['userType'] as int?,
      username: json['username'] as String?,
      nickname: json['nickname'] as String?,
      realname: json['realname'] as String?,
      phone: json['phone'] as String?,
      cardId: json['cardId'] as String?,
      inviteCode: json['inviteCode'] as String?,
      status: json['status'] as int?,
      icon: json['icon'] as String?,
      gender: json['gender'] as int?,
      birthday: json['birthday'] as String?,
      province: json['province'] as String?,
      city: json['city'] as String?,
      job: json['job'] as String?,
      personalizedSignature: json['personalizedSignature'] as String?,
      sourceType: json['sourceType'] as int?,
      lastIp: json['lastIp'] as String?,
      lastLocation: json['lastLocation'] as String?,
      createTime: json['createTime'] as String?,
      lastLoginTime: json['lastLoginTime'] as String?,
      spreadUid: json['spreadUid'] as String?,
      spreadCount: json['spreadCount'] as int?,
      spreadTime: json['spreadTime'] as String?,
      officialAuthnName: json['officialAuthnName'] as String?,
      type: json['type'] as int?,
      faceAuthnStatus: json['faceAuthnStatus'] as int?,
      followStatus: json['followStatus'] as bool?,
      backdropUrl: json['backdropUrl'] as String?,
      collectionPublic: json['collectionPublic'] as int?,
    );
  }


  UserProfile copyWith({
    String? id,
    String? memberId,
    String? adminId,
    String? deptId,
    String? socialId,
    int? userType,
    String? username,
    String? nickname,
    String? realname,
    String? phone,
    String? cardId,
    String? inviteCode,
    int? status,
    String? icon,
    int? gender,
    String? birthday,
    String? province,
    String? city,
    String? job,
    String? personalizedSignature,
    int? sourceType,
    String? lastIp,
    String? lastLocation,
    String? createTime,
    String? lastLoginTime,
    String? spreadUid,
    int? spreadCount,
    String? spreadTime,
    String? officialAuthnName,
    int? type,
    int? faceAuthnStatus,
    bool? followStatus,
    String? backdropUrl,
    int? collectionPublic,
  }) {
    return UserProfile(
      id: id ?? this.id,
      memberId: memberId ?? this.memberId,
      adminId: adminId ?? this.adminId,
      deptId: deptId ?? this.deptId,
      socialId: socialId ?? this.socialId,
      userType: userType ?? this.userType,
      username: username ?? this.username,
      nickname: nickname ?? this.nickname,
      realname: realname ?? this.realname,
      phone: phone ?? this.phone,
      cardId: cardId ?? this.cardId,
      inviteCode: inviteCode ?? this.inviteCode,
      status: status ?? this.status,
      icon: icon ?? this.icon,
      gender: gender ?? this.gender,
      birthday: birthday ?? this.birthday,
      province: province ?? this.province,
      city: city ?? this.city,
      job: job ?? this.job,
      personalizedSignature: personalizedSignature ?? this.personalizedSignature,
      sourceType: sourceType ?? this.sourceType,
      lastIp: lastIp ?? this.lastIp,
      lastLocation: lastLocation ?? this.lastLocation,
      createTime: createTime ?? this.createTime,
      lastLoginTime: lastLoginTime ?? this.lastLoginTime,
      spreadUid: spreadUid ?? this.spreadUid,
      spreadCount: spreadCount ?? this.spreadCount,
      spreadTime: spreadTime ?? this.spreadTime,
      officialAuthnName: officialAuthnName ?? this.officialAuthnName,
      type: type ?? this.type,
      faceAuthnStatus: faceAuthnStatus ?? this.faceAuthnStatus,
      followStatus: followStatus ?? this.followStatus,
      backdropUrl: backdropUrl ?? this.backdropUrl,
      collectionPublic: collectionPublic ?? this.collectionPublic,
    );
  }
}