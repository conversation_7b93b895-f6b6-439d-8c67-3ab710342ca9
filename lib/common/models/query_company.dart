import 'package:zrreport/common/index.dart';

class CompanyInfoModel {
  final String provinceId;
  final String provinceName;
  final String? legalPerson;
  final String taxpayerName;
  final String source;
  final String creditCodeOrTaxPayerId;
  final String? phoneNumber;

  CompanyInfoModel({
    required this.provinceId,
    required this.provinceName,
    this.legalPerson,
    required this.taxpayerName,
    required this.source,
    required this.creditCodeOrTaxPayerId,
    this.phoneNumber,
  });

  factory CompanyInfoModel.fromJson(Map<String, dynamic> json) {
    return CompanyInfoModel(
      provinceId: json['provinceId'] as String,
      provinceName: json['provinceName'] as String,
      legalPerson: json['legalPerson'] as String?,
      taxpayerName: json['taxpayerName'] as String,
      source: json['source'] as String,
      creditCodeOrTaxPayerId: json['creditCodeOrTaxPayerId'] as String,
      phoneNumber: json['phoneNumber'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'provinceId': provinceId,
      'provinceName': provinceName,
      'legalPerson': legalPerson,
      'taxpayerName': taxpayerName,
      'source': source,
      'creditCodeOrTaxPayerId': creditCodeOrTaxPayerId,
      'phoneNumber': phoneNumber,
    };
  }
}

/// 不需要验证码登录税务系统请求数据
class TaxOnlyAccountLoginEntity {
// {
//     "queryChannel": "1",
//     "provinceName": "zhejiang",
//     "creditCode": "91330110MA2GLF8W3C",
//     "phone": "***********",
//     "password": "a12345678",
//     "provinceId": "15",
//     "taxpayerName": "杭州智鹏汽车销售服务有限公司",
//     "shareUid": 0,
//     "source": "query",
//     "channelType": "h5",
//     "appid": "wx3843eed9287d8daa"
// }
  final String queryChannel;
  final String provinceName;
  final String creditCode;
  final String phone;
  final String password;
  final String provinceId;
  final String taxpayerName;
  final int shareUid;
  final String? source;
  final String? channelType;
  final String? appid;
  final String? personStand;

  TaxOnlyAccountLoginEntity({
    required this.queryChannel,
    required this.provinceName,
    required this.creditCode,
    required this.phone,
    required this.password,
    required this.provinceId,
    required this.taxpayerName,
    this.shareUid = 0,
    this.source,
    this.channelType,
    this.appid,
    this.personStand,
  });

  Map<String, dynamic> toJson() {
    return {
      'queryChannel': queryChannel,
      'provinceName': provinceName,
      'creditCode': creditCode,
      'phone': phone,
      'password': password,
      'provinceId': provinceId,
      'taxpayerName': taxpayerName,
      'shareUid': shareUid,
      'source': source,
      'channelType': channelType,
      'appid': appid,
      'personStand': personStand,
    };
  }
}

/// 不需要验证码登录税务系统请求数据
class TaxNeedSmsCOdeLoginEntity {
  // {
  //   "queryChannel": "1",
  //   "creditCodeOrTaxPayerId": "91410102397788464P",
  //   "phoneNumber": "***********",
  //   "provinceName": "henan",
  //   "provinceId": "4",
  //   "taxpayerName": "河南省医药超市有限公司郑州郑密路店",
  //   "password": "123456789aA",
  //   "shareUid": 0,
  //   "source": "query",
  //   "channelType": "h5",
  //   "appid": "wx3843eed9287d8daa"
  // }

  final String queryChannel;
  final String creditCodeOrTaxPayerId;
  final String phoneNumber;
  final String provinceName;
  final String provinceId;
  final String taxpayerName;
  final String password;
  final int shareUid;
  final String source;
  final String channelType;
  final String appid;

  TaxNeedSmsCOdeLoginEntity({
    required this.queryChannel,
    required this.creditCodeOrTaxPayerId,
    required this.phoneNumber,
    required this.provinceName,
    required this.provinceId,
    required this.taxpayerName,
    required this.password,
    this.shareUid = 0,
    required this.source,
    required this.channelType,
    required this.appid,
  });

  Map<String, dynamic> toJson() {
    return {
      'queryChannel': queryChannel,
      'creditCodeOrTaxPayerId': creditCodeOrTaxPayerId,
      'phoneNumber': phoneNumber,
      'provinceName': provinceName,
      'provinceId': provinceId,
      'taxpayerName': taxpayerName,
      'password': password,
      'shareUid': shareUid,
      'source': source,
      'channelType': channelType,
      'appid': appid,
    };
  }
}


/// 不需要验证码登录的返回数据结构
class TaxOnlyAccountLoginModel {
  final String id;
  final String phone;
  final String? province;
  final String enterpriseName;
  final String enterpriseId;
  final String registerAddress;
  final int queryStatus;
  final String legalPerson;
  final String createTime;
  final bool sendMsgStatus;

  TaxOnlyAccountLoginModel({
    required this.id,
    required this.phone,
    this.province,
    required this.enterpriseName,
    required this.enterpriseId,
    required this.registerAddress,
    required this.queryStatus,
    required this.legalPerson,
    required this.createTime,
    required this.sendMsgStatus,
  });

  factory TaxOnlyAccountLoginModel.fromJson(Map<String, dynamic> json) {
    return TaxOnlyAccountLoginModel(
      id: json['id'] as String,
      phone: json['phone'] as String,
      province: json['province'] as String?,
      enterpriseName: json['enterpriseName'] as String,
      enterpriseId: json['enterpriseId'] as String,
      registerAddress: json['registerAddress'] as String,
      queryStatus: json['queryStatus'] as int,
      legalPerson: json['legalPerson'] as String,
      createTime: json['createTime'] as String,
      sendMsgStatus: json['sendMsgStatus'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'phone': phone,
      'province': province,
      'enterpriseName': enterpriseName,
      'enterpriseId': enterpriseId,
      'registerAddress': registerAddress,
      'queryStatus': queryStatus,
      'legalPerson': legalPerson,
      'createTime': createTime,
      'sendMsgStatus': sendMsgStatus,
    };
  }
}

/// 查询需要验证码
/// 登录税务系统第一步请求返回结果
class TaxLoginModel {
  /*
  {
        "code": 0,
        "message": "税务系统：已输入账号信息",
        "uuid": "01f3884bf2ae4de7a32376663d31acb2",
        "enterpriseId": "216",
        "queryTaskId": "2596",
        "queryTaskStatus": 0,
        "needSendCaptchaCode": 1,
        "needSendCaptchaCodeMessage": "需要发送验证码",
        "sendMsgStatus": false
    }
    */
  final int code;
  final String message;
  final String uuid;
  final String enterpriseId;
  final String queryTaskId;
  final int queryTaskStatus;
  final int needSendCaptchaCode;
  final String needSendCaptchaCodeMessage;
  final bool sendMsgStatus;

  TaxLoginModel({
    required this.code,
    required this.message,
    required this.uuid,
    required this.enterpriseId,
    required this.queryTaskId,
    required this.queryTaskStatus,
    required this.needSendCaptchaCode,
    required this.needSendCaptchaCodeMessage,
    required this.sendMsgStatus,
  });

  factory TaxLoginModel.fromJson(Map<String, dynamic> json) {
    return TaxLoginModel(
      code: json['code'] as int,
      message: json['message'] as String,
      uuid: json['uuid'] as String,
      enterpriseId: json['enterpriseId'] as String,
      queryTaskId: json['queryTaskId'] as String,
      queryTaskStatus: json['queryTaskStatus'] as int,
      needSendCaptchaCode: json['needSendCaptchaCode'] as int,
      needSendCaptchaCodeMessage: json['needSendCaptchaCodeMessage'] as String,
      sendMsgStatus: json['sendMsgStatus'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'uuid': uuid,
      'enterpriseId': enterpriseId,
      'queryTaskId': queryTaskId,
      'queryTaskStatus': queryTaskStatus,
      'needSendCaptchaCode': needSendCaptchaCode,
      'needSendCaptchaCodeMessage': needSendCaptchaCodeMessage,
      'sendMsgStatus': sendMsgStatus,
    };
  }
}

/// portal/tax/tax/sendCode
class TaxSendSmsCodeEntity {
  final String phone;
  final String creditCodeOrTaxPayerId;
  final String uuid;

  TaxSendSmsCodeEntity({
    required this.phone,
    required this.creditCodeOrTaxPayerId,
    required this.uuid,
  });

  Map<String, dynamic> toJson() {
    return {
      'phone': phone,
      'creditCodeOrTaxPayerId': creditCodeOrTaxPayerId,
      'uuid': uuid,
    };
  }
}

class TaxCaptchaLoginEntity {
  final String provinceName;
  final String creditCodeOrTaxPayerId;
  final String phoneNumber;
  final String password;
  final String code;
  final String enterpriseId;
  final String uuid;
  final String? source;

  TaxCaptchaLoginEntity({
    required this.provinceName,
    required this.creditCodeOrTaxPayerId,
    required this.phoneNumber,
    required this.password,
    required this.code,
    required this.enterpriseId,
    required this.uuid,
    this.source,
  });

  Map<String, dynamic> toJson() {
    return {
      'provinceName': provinceName,
      'creditCodeOrTaxPayerId': creditCodeOrTaxPayerId,
      'phoneNumber': phoneNumber,
      'password': password,
      'code': code,
      'enterpriseId': enterpriseId,
      'uuid': uuid,
      'source': source,
    };
  }
}

class ListReportEntity {
  int pageNum;
  int pageSize;
  String likeName;
  String queryStatus;
  int queryType;

  ListReportEntity(this.pageNum, this.pageSize,
      {required this.likeName,
      required this.queryStatus,
      required this.queryType});

  Map<String, dynamic> toJson() {
    return {
      'pageNum': pageNum,
      'pageSize': pageSize,
      'likeName': likeName,
      'queryStatus': queryStatus,
      'queryType': queryType,
    };
  }
}

class QueryReport {
  final String parentId;
  final String queryStatus;
  final String taxpayerName;
  final String? legalPerson;
  final String? registerAddress;
  final String? shareCode;
  final String? enterpriseId;
  final String? createTime;
  final String? freeQueryNum;
  final String id;
  final String taskId;
  final String channel;
  final String provinceName;
  final String provinceId;
  final String? nickname;
  final String? icon;

  QueryReport({
    required this.parentId,
    required this.queryStatus,
    required this.taxpayerName,
    required this.legalPerson,
    required this.registerAddress,
    required this.shareCode,
    required this.enterpriseId,
    required this.createTime,
    this.freeQueryNum,
    required this.id,
    required this.taskId,
    required this.channel,
    required this.provinceName,
    required this.provinceId,
    this.nickname,
    this.icon,
  });

  factory QueryReport.fromJson(Map<String, dynamic> json) {
    return QueryReport(
      parentId: json['parentId'] as String,
      queryStatus: json['queryStatus'] as String,
      taxpayerName: json['taxpayerName'] as String,
      legalPerson: json['legalPerson'] as String?,
      registerAddress: json['registerAddress'] as String?,
      shareCode: json['shareCode'] as String?,
      enterpriseId: json['enterpriseId'] as String?,
      createTime: json['createTime'] as String?,
      freeQueryNum: json['freeQueryNum'] as String?,
      id: json['id'] as String,
      taskId: json['taskId'] as String,
      channel: json['channel'] as String,
      provinceName: json['provinceName'] as String,
      provinceId: json['provinceId'] as String,
      nickname: json['nickname'] as String?,
      icon: json['icon'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'parentId': parentId,
      'queryStatus': queryStatus,
      'taxpayerName': taxpayerName,
      'legalPerson': legalPerson,
      'registerAddress': registerAddress,
      'shareCode': shareCode,
      'enterpriseId': enterpriseId,
      'createTime': createTime,
      'freeQueryNum': freeQueryNum,
      'id': id,
      'taskId': taskId,
      'channel': channel,
      'provinceName': provinceName,
      'provinceId': provinceId,
      'nickname': nickname,
      'icon': icon,
    };
  }
}

/// 报告详情

class ReportDetailModel {
  final String? id;
  final String taskId;
  final bool dateStatus;
  final String shareCode;
  final String enterpriseId;
  final String enterpriseName;
  final String legalPerson;
  final int age;
  final bool stockStatus;
  final double? stockPercent;
  final String registerDate;
  final String creditCode;
  final String industry;
  final String loginRegisterType;
  final String registerAddress;
  final String phone;
  final int registerMonth;
  final String qualification;
  final String openAccountBank;
  final String businessScope;
  final bool legalChangeStatus;
  final EnterpriseChange? enterpriseChange;
  final List<EnterpriseChange> qccEnterpriseChangeList;
  final String bankTaxRecord;
  final List<EnterprisePartner> qccEnterprisePartnersList;
  final String source;
  final String createTime;
  final int? visibleType; // visibleType 可见类型：0->全公开；1->部分公开；2->私密；3->指定访问

  ReportDetailModel({
    this.id,
    required this.taskId,
    required this.dateStatus,
    required this.shareCode,
    required this.enterpriseId,
    required this.enterpriseName,
    required this.legalPerson,
    required this.age,
    required this.stockStatus,
    this.stockPercent,
    required this.registerDate,
    required this.creditCode,
    required this.industry,
    required this.loginRegisterType,
    required this.registerAddress,
    required this.phone,
    required this.registerMonth,
    required this.qualification,
    required this.openAccountBank,
    required this.businessScope,
    required this.legalChangeStatus,
    this.enterpriseChange,
    required this.qccEnterpriseChangeList,
    required this.bankTaxRecord,
    required this.qccEnterprisePartnersList,
    required this.source,
    required this.createTime,
    this.visibleType,
  });

  factory ReportDetailModel.fromJson(Map<String, dynamic> json) {
    return ReportDetailModel(
      id: json['id'] as String?,
      taskId: (json['taskId']?.toString()) ?? '',
      dateStatus: json['dateStatus'] as bool,
      shareCode: (json['shareCode'] as String?) ?? '',
      enterpriseId: (json['enterpriseId']?.toString()) ?? '',
      enterpriseName: (json['enterpriseName'] as String?) ?? '',
      legalPerson: (json['legalPerson'] as String?) ?? '',
      age: json['age'] as int,
      stockStatus: json['stockStatus'] as bool,
      stockPercent: json['stockPercent'] as double?,
      registerDate: (json['registerDate'] as String?) ?? '',
      creditCode: (json['creditCode'] as String?) ?? '',
      industry: (json['industry'] as String?) ?? '',
      loginRegisterType: (json['loginRegisterType'] as String?) ?? '',
      registerAddress: (json['registerAddress'] as String?) ?? '',
      phone: (json['phone'] as String?) ?? '',
      registerMonth: json['registerMonth'] as int,
      qualification: (json['qualification'] as String?) ?? '',
      openAccountBank: (json['openAccountBank'] as String?) ?? '',
      businessScope: (json['businessScope'] as String?) ?? '',
      legalChangeStatus: json['legalChangeStatus'] as bool,
      enterpriseChange: json['enterpriseChange'] != null
          ? EnterpriseChange.fromJson(
              json['enterpriseChange'] as Map<String, dynamic>)
          : null,
      qccEnterpriseChangeList: (json['qccEnterpriseChangeList'] as List? ?? [])
          .map((e) => EnterpriseChange.fromJson(e as Map<String, dynamic>))
          .toList(),
      bankTaxRecord: (json['bankTaxRecord'] as String?) ?? '',
      qccEnterprisePartnersList:
          (json['qccEnterprisePartnersList'] as List? ?? [])
              .map((e) => EnterprisePartner.fromJson(e as Map<String, dynamic>))
              .toList(),
      source: (json['source'] as String?) ?? '',
      createTime: (json['createTime'] as String?) ?? '',
      visibleType: json['visibleType'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'dateStatus': dateStatus,
      'shareCode': shareCode,
      'enterpriseId': enterpriseId,
      'enterpriseName': enterpriseName,
      'legalPerson': legalPerson,
      'age': age,
      'stockStatus': stockStatus,
      'stockPercent': stockPercent,
      'registerDate': registerDate,
      'creditCode': creditCode,
      'industry': industry,
      'loginRegisterType': loginRegisterType,
      'registerAddress': registerAddress,
      'phone': phone,
      'registerMonth': registerMonth,
      'qualification': qualification,
      'openAccountBank': openAccountBank,
      'businessScope': businessScope,
      'legalChangeStatus': legalChangeStatus,
      'enterpriseChange': enterpriseChange?.toJson(),
      'qccEnterpriseChangeList':
          qccEnterpriseChangeList.map((e) => e.toJson()).toList(),
      'bankTaxRecord': bankTaxRecord,
      'qccEnterprisePartnersList': qccEnterprisePartnersList,
      'source': source,
      'createTime': createTime,
      'visibleType': visibleType,
    };
  }
}

class EnterpriseChange {
  final String id;
  final String name;
  final String beforeContent;
  final String affterContent;
  final String changeDate;

  EnterpriseChange({
    required this.id,
    required this.name,
    required this.beforeContent,
    required this.affterContent,
    required this.changeDate,
  });

  factory EnterpriseChange.fromJson(Map<String, dynamic> json) {
    return EnterpriseChange(
      id: (json['id']?.toString()) ?? '',
      name: (json['name'] as String?) ?? '',
      beforeContent: (json['beforeContent'] as String?) ?? '',
      affterContent: (json['affterContent'] as String?) ?? '',
      changeDate: (json['changeDate'] as String?) ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'beforeContent': beforeContent,
      'affterContent': affterContent,
      'changeDate': changeDate,
    };
  }
}

class EnterprisePartner {
  final String stockName;
  final String stockType;
  final String stockPercent;
  final String shouldCapi;
  final String realCapi;
  final String capiDate;
  final String shoudDate;
  final String finalBenefitPercent;

  EnterprisePartner({
    required this.stockName,
    required this.stockType,
    required this.stockPercent,
    required this.shouldCapi,
    required this.realCapi,
    required this.capiDate,
    required this.shoudDate,
    required this.finalBenefitPercent,
  });

  factory EnterprisePartner.fromJson(Map<String, dynamic> json) {
    return EnterprisePartner(
      stockName: (json['stockName'] as String?) ?? '',
      stockType: (json['stockType'] as String?) ?? '',
      stockPercent: (json['stockPercent'] as String?) ?? '',
      shouldCapi: (json['shouldCapi'] as String?) ?? '',
      realCapi: (json['realCapi'] as String?) ?? '',
      capiDate: (json['capiDate'] as String?) ?? '',
      shoudDate: (json['shoudDate'] as String?) ?? '',
      finalBenefitPercent: (json['finalBenefitPercent'] as String?) ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'stockName': stockName,
      'stockType': stockType,
      'stockPercent': stockPercent,
      'shouldCapi': shouldCapi,
      'realCapi': realCapi,
      'capiDate': capiDate,
      'shoudDate': shoudDate,
      'finalBenefitPercent': finalBenefitPercent,
    };
  }
}

/// 权限设置请求实体
class PermissionVisibleEntity {
  final int? id;
  final int taskId;
  final String shareCode;
  final int visibleType;
  final List<String>? specifyAccessUsers;

  PermissionVisibleEntity({
    this.id,
    required this.taskId,
    required this.shareCode,
    required this.visibleType,
    this.specifyAccessUsers,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'shareCode': shareCode,
      'visibleType': visibleType,
      'specifyAccessUsers': specifyAccessUsers,
    };
  }
}

/// 重新查询请求实体
class RefreshQueryEntity {
  final String taskId;
  final String creditCode;

  RefreshQueryEntity({
    required this.taskId,
    required this.creditCode,
  });

  Map<String, dynamic> toJson() {
    return {
      'taskId': taskId,
      'creditCode': creditCode,
    };
  }
}

/// 重新查询响应数据模型
class RefreshQueryResponse {
  final String code;
  final String msg;
  final String status;
  final String uuid;
  final String account;
  final bool needVerifyCode;
  final String queryTaskId;
  final QueryTaskVo? queryTaskVo;

  RefreshQueryResponse({
    required this.code,
    required this.msg,
    required this.status,
    required this.uuid,
    required this.account,
    required this.needVerifyCode,
    required this.queryTaskId,
    this.queryTaskVo,
  });

  factory RefreshQueryResponse.fromJson(Map<String, dynamic> json) {
    return RefreshQueryResponse(
      code: (json['code']?.toString()) ?? '0',
      msg: (json['msg'] as String?) ?? '',
      status: (json['status'] as String?) ?? '',
      uuid: (json['uuid'] as String?) ?? '',
      account: (json['account'] as String?) ?? '',
      needVerifyCode: json['needVerifyCode'] as bool,
      queryTaskId: (json['queryTaskId']?.toString()) ?? '0',
      queryTaskVo: json['queryTaskVo'] != null
          ? QueryTaskVo.fromJson(json['queryTaskVo'] as Map<String, dynamic>)
          : null,
    );
  }
}

/// 查询任务信息
class QueryTaskVo {
  final String id;
  final String phone;
  final String enterpriseName;
  final String enterpriseId;
  final String registerAddress;
  final String queryStatus;
  final String legalPerson;
  final String createTime;
  final bool sendMsgStatus;
  final String? province;

  QueryTaskVo({
    required this.id,
    required this.phone,
    required this.enterpriseName,
    required this.enterpriseId,
    required this.registerAddress,
    required this.queryStatus,
    required this.legalPerson,
    required this.createTime,
    required this.sendMsgStatus,
    this.province,
  });

  factory QueryTaskVo.fromJson(Map<String, dynamic> json) {
    return QueryTaskVo(
      id: (json['id']?.toString()) ?? '0',
      phone: (json['phone'] as String?) ?? '',
      enterpriseName: (json['enterpriseName'] as String?) ?? '',
      enterpriseId: (json['enterpriseId']?.toString()) ?? '0',
      registerAddress: (json['registerAddress'] as String?) ?? '',
      queryStatus: (json['queryStatus']?.toString()) ?? '0',
      legalPerson: (json['legalPerson'] as String?) ?? '',
      createTime: (json['createTime'] as String?) ?? '',
      sendMsgStatus: json['sendMsgStatus'] as bool,
      province: json['province'] as String?,
    );
  }
}

/// 新的发送验证码请求实体
class AppSendSmsEntity {
  final String province;
  final String creditCode;
  final String uuid;
  final String account;

  AppSendSmsEntity({
    required this.province,
    required this.creditCode,
    required this.uuid,
    required this.account,
  });

  Map<String, dynamic> toJson() {
    return {
      'province': province,
      'creditCode': creditCode,
      'uuid': uuid,
      'account': account,
    };
  }
}

/// 验证短信验证码请求实体
class AppVerifySmsEntity {
  final String taskId;
  final String creditCode;
  final String uuid;
  final String account;
  final String code;

  AppVerifySmsEntity({
    required this.taskId,
    required this.creditCode,
    required this.uuid,
    required this.account,
    required this.code,
  });

  Map<String, dynamic> toJson() {
    return {
      'taskId': taskId,
      'creditCode': creditCode,
      'uuid': uuid,
      'account': account,
      'code': code,
    };
  }
}

/// 验证短信验证码响应数据模型
class AppVerifySmsResponse {
  final String code;
  final String msg;
  final String status;
  final String uuid;
  final String account;
  final bool needVerifyCode;
  final String queryTaskId;
  final QueryTaskVo? queryTaskVo;

  AppVerifySmsResponse({
    required this.code,
    required this.msg,
    required this.status,
    required this.uuid,
    required this.account,
    required this.needVerifyCode,
    required this.queryTaskId,
    this.queryTaskVo,
  });

  factory AppVerifySmsResponse.fromJson(Map<String, dynamic> json) {
    return AppVerifySmsResponse(
      code: (json['code']?.toString()) ?? '0',
      msg: (json['msg'] as String?) ?? '',
      status: (json['status'] as String?) ?? '',
      uuid: (json['uuid'] as String?) ?? '',
      account: (json['account'] as String?) ?? '',
      needVerifyCode: json['needVerifyCode'] as bool,
      queryTaskId: (json['queryTaskId']?.toString()) ?? '0',
      queryTaskVo: json['queryTaskVo'] != null
          ? QueryTaskVo.fromJson(json['queryTaskVo'] as Map<String, dynamic>)
          : null,
    );
  }
}

class SearchEnterpriseEntity {
  int pageNum;
  int pageSize;
  String keyword;

  SearchEnterpriseEntity(
    this.keyword,
    this.pageNum,
    this.pageSize,
  );

  Map<String, dynamic> toJson() {
    return {
      'keyword': keyword,
      'pageNum': pageNum,
      'pageSize': pageSize,
      'platformType': Constants.platform,
    };
  }
}

class SearchEnterprise {
  final String? companyName;
  final String? creditNo;
  final String? companyCode;
  final String? legalPerson;
  final String? companyStatus;
  final String? establishDate;

  SearchEnterprise({
    this.companyName,
    this.creditNo,
    this.companyCode,
    this.legalPerson,
    this.companyStatus,
    this.establishDate,
  });

  factory SearchEnterprise.fromJson(Map<String, dynamic> json) {
    return SearchEnterprise(
      companyName: json['companyName'] as String?,
      creditNo: json['creditNo'] as String?,
      companyCode: json['companyCode'] as String?,
      legalPerson: json['legalPerson'] as String?,
      companyStatus: json['companyStatus'] as String?,
      establishDate: json['establishDate'] as String?,
    );
  }
}

class EnterpriseCase {
  final String? id;
  final String? name;
  final String? reason;
  final String? amount;
  final String? judgeDate;
  final String? result;
  final String? caseType;
  final String? roleType;

  EnterpriseCase({
    this.id,
    this.name,
    this.reason,
    this.amount,
    this.judgeDate,
    this.result,
    this.caseType,
    this.roleType,
  });

  factory EnterpriseCase.fromJson(Map<String, dynamic> json) {
    return EnterpriseCase(
      id: json['id'] as String?,
      name: json['name'] as String?,
      reason: json['reason'] as String?,
      amount: json['amount'] as String?,
      judgeDate: json['judgeDate'] as String?,
      result: json['result'] as String?,
      caseType: json['caseType'] as String?,
      roleType: json['roleType'] as String?,
    );
  }
}

/// 工商司法信息
class EnterpriseCaseModel {
  final List<EnterpriseChange> qccEnterpriseChangeList;

  final List<EnterpriseCase> qccEnterpriseCaseList;

  final int? enterpriseDishonestSum;
  final int? executedPersonSum;
  final int? courtNoticeSum;
  final int? enterpriseCaseSum;
  final String? subjectAmount;
  final bool? changeStatus;

  EnterpriseCaseModel({
    required this.enterpriseDishonestSum,
    required this.executedPersonSum,
    required this.courtNoticeSum,
    required this.enterpriseCaseSum,
    required this.subjectAmount,
    required this.changeStatus,
    required this.qccEnterpriseChangeList,
    required this.qccEnterpriseCaseList,
  });

  factory EnterpriseCaseModel.fromJson(Map<String, dynamic> json) {
    return EnterpriseCaseModel(
      enterpriseDishonestSum: json['enterpriseDishonestSum'] as int?,
      executedPersonSum: json['executedPersonSum'] as int?,
      courtNoticeSum: json['courtNoticeSum'] as int?,
      enterpriseCaseSum: json['enterpriseCaseSum'] as int?,
      subjectAmount: json['subjectAmount'] as String?,
      changeStatus: json['changeStatus'] as bool?,
      qccEnterpriseChangeList: (json['enterpriseChangeList'] as List? ?? [])
          .map((e) => EnterpriseChange.fromJson(e as Map<String, dynamic>))
          .toList(),
      qccEnterpriseCaseList: (json['qccEnterpriseCaseList'] as List? ?? [])
          .map((e) => EnterpriseCase.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}
