// To parse this JSON data, do
//
//     final enterpriseInvoiceInfo = enterpriseInvoiceInfoFromJson(jsonString);

import 'dart:convert';

EnterpriseInvoiceInfo enterpriseInvoiceInfoFromJson(String str) => EnterpriseInvoiceInfo.fromJson(json.decode(str));

String enterpriseInvoiceInfoToJson(EnterpriseInvoiceInfo data) => json.encode(data.toJson());

class EnterpriseInvoiceInfo {
    bool? billingRecords;
    String? billingDaysSum;
    String? taxVoidRateMonth12;
    int? breakMonthsSum12;
    int? breakMonthsSum;
    String? taxAmountGrowthRateMonth12;
    String? taxOutputNotInvoicedMonth12;
    String? earliestTime;
    String? latestTime;
    String? recentBillingGapdays;
    List<InvoiceInfoDataList>? invoiceInfoDataList;
    List<EnterpriseMonthApplyList>? enterpriseMonthApplyList;

    EnterpriseInvoiceInfo({
        this.billingRecords,
        this.billingDaysSum,
        this.taxVoidRateMonth12,
        this.breakMonthsSum12,
        this.breakMonthsSum,
        this.taxAmountGrowthRateMonth12,
        this.taxOutputNotInvoicedMonth12,
        this.earliestTime,
        this.latestTime,
        this.recentBillingGapdays,
        this.invoiceInfoDataList,
        this.enterpriseMonthApplyList,
    });

    factory EnterpriseInvoiceInfo.fromJson(Map<String, dynamic> json) => EnterpriseInvoiceInfo(
        billingRecords: json["billingRecords"],
        billingDaysSum: json["billingDaysSum"],
        taxVoidRateMonth12: json["taxVoidRateMonth12"],
        breakMonthsSum12: json["breakMonthsSum12"],
        breakMonthsSum: json["breakMonthsSum"],
        taxAmountGrowthRateMonth12: json["taxAmountGrowthRateMonth12"],
        taxOutputNotInvoicedMonth12: json["taxOutputNotInvoicedMonth12"],
        earliestTime: json["earliestTime"],
        latestTime: json["latestTime"],
        recentBillingGapdays: json["recentBillingGapdays"],
        invoiceInfoDataList: json["invoiceInfoDataList"] == null ? [] : List<InvoiceInfoDataList>.from(json["invoiceInfoDataList"]!.map((x) => InvoiceInfoDataList.fromJson(x))),
        enterpriseMonthApplyList: json["enterpriseMonthApplyList"] == null ? [] : List<EnterpriseMonthApplyList>.from(json["enterpriseMonthApplyList"]!.map((x) => EnterpriseMonthApplyList.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "billingRecords": billingRecords,
        "billingDaysSum": billingDaysSum,
        "taxVoidRateMonth12": taxVoidRateMonth12,
        "breakMonthsSum12": breakMonthsSum12,
        "breakMonthsSum": breakMonthsSum,
        "taxAmountGrowthRateMonth12": taxAmountGrowthRateMonth12,
        "taxOutputNotInvoicedMonth12": taxOutputNotInvoicedMonth12,
        "earliestTime": earliestTime,
        "latestTime": latestTime,
        "recentBillingGapdays": recentBillingGapdays,
        "invoiceInfoDataList": invoiceInfoDataList == null ? [] : List<dynamic>.from(invoiceInfoDataList!.map((x) => x.toJson())),
        "enterpriseMonthApplyList": enterpriseMonthApplyList == null ? [] : List<dynamic>.from(enterpriseMonthApplyList!.map((x) => x.toJson())),
    };
}

class EnterpriseMonthApplyList {
    int? month;
    TaxEnterpriseApplyDate? taxEnterpriseApplyDate;

    EnterpriseMonthApplyList({
        this.month,
        this.taxEnterpriseApplyDate,
    });

    factory EnterpriseMonthApplyList.fromJson(Map<String, dynamic> json) => EnterpriseMonthApplyList(
        month: json["month"],
        taxEnterpriseApplyDate: json["taxEnterpriseApplyDate"] == null ? null : TaxEnterpriseApplyDate.fromJson(json["taxEnterpriseApplyDate"]),
    );

    Map<String, dynamic> toJson() => {
        "month": month,
        "taxEnterpriseApplyDate": taxEnterpriseApplyDate?.toJson(),
    };
}

class TaxEnterpriseApplyDate {
    String? invoiceAmount;
    String? invoiceGrowthRate;
    String? invoiceOverGrowthRate;
    int? invoiceCount;
    String? invoiceMonthsNum;
    String? breakMonthsSum;
    String? invoiceRedCountRate;
    String? invoiceRedAmountRate;
    int? customerUpNum;
    int? customerDownNum;

    TaxEnterpriseApplyDate({
        this.invoiceAmount,
        this.invoiceGrowthRate,
        this.invoiceOverGrowthRate,
        this.invoiceCount,
        this.invoiceMonthsNum,
        this.breakMonthsSum,
        this.invoiceRedCountRate,
        this.invoiceRedAmountRate,
        this.customerUpNum,
        this.customerDownNum,
    });

    factory TaxEnterpriseApplyDate.fromJson(Map<String, dynamic> json) => TaxEnterpriseApplyDate(
        invoiceAmount: json["invoiceAmount"],
        invoiceGrowthRate: json["invoiceGrowthRate"],
        invoiceOverGrowthRate: json["invoiceOverGrowthRate"],
        invoiceCount: json["invoiceCount"],
        invoiceMonthsNum: json["invoiceMonthsNum"],
        breakMonthsSum: json["breakMonthsSum"],
        invoiceRedCountRate: json["invoiceRedCountRate"],
        invoiceRedAmountRate: json["invoiceRedAmountRate"],
        customerUpNum: json["customerUpNum"],
        customerDownNum: json["customerDownNum"],
    );

    Map<String, dynamic> toJson() => {
        "invoiceAmount": invoiceAmount,
        "invoiceGrowthRate": invoiceGrowthRate,
        "invoiceOverGrowthRate": invoiceOverGrowthRate,
        "invoiceCount": invoiceCount,
        "invoiceMonthsNum": invoiceMonthsNum,
        "breakMonthsSum": breakMonthsSum,
        "invoiceRedCountRate": invoiceRedCountRate,
        "invoiceRedAmountRate": invoiceRedAmountRate,
        "customerUpNum": customerUpNum,
        "customerDownNum": customerDownNum,
    };
}

class InvoiceInfoDataList {
    String? total;
    int? year;
    List<MonthDatum>? monthData;

    InvoiceInfoDataList({
        this.total,
        this.year,
        this.monthData,
    });

    factory InvoiceInfoDataList.fromJson(Map<String, dynamic> json) => InvoiceInfoDataList(
        total: json["total"],
        year: json["year"],
        monthData: json["monthData"] == null ? [] : List<MonthDatum>.from(json["monthData"]!.map((x) => MonthDatum.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "total": total,
        "year": year,
        "monthData": monthData == null ? [] : List<dynamic>.from(monthData!.map((x) => x.toJson())),
    };
}

class MonthDatum {
    String? name;
    String? value;

    MonthDatum({
        this.name,
        this.value,
    });

    factory MonthDatum.fromJson(Map<String, dynamic> json) => MonthDatum(
        name: json["name"],
        value: json["value"],
    );

    Map<String, dynamic> toJson() => {
        "name": name,
        "value": value,
    };
}
