// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location_detail.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LocationDetail {
  /// 唯一标识符
  String get id;

  /// 经度
  double get longitude;

  /// 纬度
  double get latitude;

  /// 城市或地区名字
  String get cityName;

  /// 更详细的地址，小区，镇等
  String get townName;

  /// 地区码
  String get adCode;

  /// 创建时间
  DateTime? get createdAt;

  /// Create a copy of LocationDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LocationDetailCopyWith<LocationDetail> get copyWith =>
      _$LocationDetailCopyWithImpl<LocationDetail>(
          this as LocationDetail, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LocationDetail &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.cityName, cityName) ||
                other.cityName == cityName) &&
            (identical(other.townName, townName) ||
                other.townName == townName) &&
            (identical(other.adCode, adCode) || other.adCode == adCode) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, longitude, latitude,
      cityName, townName, adCode, createdAt);

  @override
  String toString() {
    return 'LocationDetail(id: $id, longitude: $longitude, latitude: $latitude, cityName: $cityName, townName: $townName, adCode: $adCode, createdAt: $createdAt)';
  }
}

/// @nodoc
abstract mixin class $LocationDetailCopyWith<$Res> {
  factory $LocationDetailCopyWith(
          LocationDetail value, $Res Function(LocationDetail) _then) =
      _$LocationDetailCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      double longitude,
      double latitude,
      String cityName,
      String townName,
      String adCode,
      DateTime? createdAt});
}

/// @nodoc
class _$LocationDetailCopyWithImpl<$Res>
    implements $LocationDetailCopyWith<$Res> {
  _$LocationDetailCopyWithImpl(this._self, this._then);

  final LocationDetail _self;
  final $Res Function(LocationDetail) _then;

  /// Create a copy of LocationDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? longitude = null,
    Object? latitude = null,
    Object? cityName = null,
    Object? townName = null,
    Object? adCode = null,
    Object? createdAt = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      longitude: null == longitude
          ? _self.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      latitude: null == latitude
          ? _self.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      cityName: null == cityName
          ? _self.cityName
          : cityName // ignore: cast_nullable_to_non_nullable
              as String,
      townName: null == townName
          ? _self.townName
          : townName // ignore: cast_nullable_to_non_nullable
              as String,
      adCode: null == adCode
          ? _self.adCode
          : adCode // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: freezed == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

class _LocationDetail implements LocationDetail {
  const _LocationDetail(
      {this.id = '',
      required this.longitude,
      required this.latitude,
      this.cityName = '',
      this.townName = '',
      this.adCode = '',
      this.createdAt = null});

  /// 唯一标识符
  @override
  @JsonKey()
  final String id;

  /// 经度
  @override
  final double longitude;

  /// 纬度
  @override
  final double latitude;

  /// 城市或地区名字
  @override
  @JsonKey()
  final String cityName;

  /// 更详细的地址，小区，镇等
  @override
  @JsonKey()
  final String townName;

  /// 地区码
  @override
  @JsonKey()
  final String adCode;

  /// 创建时间
  @override
  @JsonKey()
  final DateTime? createdAt;

  /// Create a copy of LocationDetail
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LocationDetailCopyWith<_LocationDetail> get copyWith =>
      __$LocationDetailCopyWithImpl<_LocationDetail>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LocationDetail &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.cityName, cityName) ||
                other.cityName == cityName) &&
            (identical(other.townName, townName) ||
                other.townName == townName) &&
            (identical(other.adCode, adCode) || other.adCode == adCode) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, longitude, latitude,
      cityName, townName, adCode, createdAt);

  @override
  String toString() {
    return 'LocationDetail(id: $id, longitude: $longitude, latitude: $latitude, cityName: $cityName, townName: $townName, adCode: $adCode, createdAt: $createdAt)';
  }
}

/// @nodoc
abstract mixin class _$LocationDetailCopyWith<$Res>
    implements $LocationDetailCopyWith<$Res> {
  factory _$LocationDetailCopyWith(
          _LocationDetail value, $Res Function(_LocationDetail) _then) =
      __$LocationDetailCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      double longitude,
      double latitude,
      String cityName,
      String townName,
      String adCode,
      DateTime? createdAt});
}

/// @nodoc
class __$LocationDetailCopyWithImpl<$Res>
    implements _$LocationDetailCopyWith<$Res> {
  __$LocationDetailCopyWithImpl(this._self, this._then);

  final _LocationDetail _self;
  final $Res Function(_LocationDetail) _then;

  /// Create a copy of LocationDetail
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? longitude = null,
    Object? latitude = null,
    Object? cityName = null,
    Object? townName = null,
    Object? adCode = null,
    Object? createdAt = freezed,
  }) {
    return _then(_LocationDetail(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      longitude: null == longitude
          ? _self.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      latitude: null == latitude
          ? _self.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      cityName: null == cityName
          ? _self.cityName
          : cityName // ignore: cast_nullable_to_non_nullable
              as String,
      townName: null == townName
          ? _self.townName
          : townName // ignore: cast_nullable_to_non_nullable
              as String,
      adCode: null == adCode
          ? _self.adCode
          : adCode // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: freezed == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

// dart format on
