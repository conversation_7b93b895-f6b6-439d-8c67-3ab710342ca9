import 'package:freezed_annotation/freezed_annotation.dart';

part 'location_detail.freezed.dart';

@freezed
abstract class LocationDetail with _$LocationDetail {
  const factory LocationDetail({
    /// 唯一标识符
    @Default('') String id,

    /// 经度
    required double longitude,

    /// 纬度
    required double latitude,

    /// 城市或地区名字
    @Default('') String cityName,

    /// 更详细的地址，小区，镇等
    @Default('') String townName,

    /// 地区码
    @Default('') String adCode,

    /// 创建时间
    @Default(null) DateTime? createdAt,
  }) = _LocationDetail;
}
