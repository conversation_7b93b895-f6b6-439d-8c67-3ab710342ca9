import 'package:zrreport/common/index.dart';

import 'index.dart';

// /// 用户详情响应数据中的会员信息
// class UserProfile {
//   /// 用户ID
//   final String id;

//   /// 管理员ID
//   final String? adminId;

//   /// 部门ID
//   final String? deptId;

//   /// 会员ID
//   final String? memberId;

//   /// 社交ID
//   final String? socialId;

//   /// 用户类型
//   final int? userType;

//   /// 用户名
//   final String? username;

//   /// 昵称
//   final String? nickname;

//   /// 真实姓名
//   final String? realname;

//   /// 手机号
//   final String? phone;

//   /// 身份证号
//   final String? cardId;

//   /// 邀请码
//   final String? inviteCode;

//   /// 状态
//   final int? status;

//   /// 头像
//   final String? icon;

//   /// 性别
//   final int? gender;

//   /// 生日
//   final String? birthday;

//   /// 省份
//   final String? province;

//   /// 城市
//   final String? city;

//   /// 职业
//   final String? job;

//   /// 个性签名
//   final String? personalizedSignature;

//   /// 来源类型
//   final int? sourceType;

//   /// 最后登录IP
//   final String? lastIp;

//   /// 最后登录地点
//   final String? lastLocation;

//   /// 创建时间
//   final String? createTime;

//   /// 最后登录时间
//   final String? lastLoginTime;

//   /// 推广用户ID
//   final String? spreadUid;

//   /// 推广数量
//   final int? spreadCount;

//   /// 推广时间
//   final String? spreadTime;

//   /// 官方认证名称
//   final String? officialAuthnName;

//   /// 类型
//   final int? type;

//   /// 人脸认证状态
//   final int? faceAuthnStatus;

//   /// 关注状态
//   final bool followStatus;

//   /// 背景图片URL
//   final String? backdropUrl;

//   UserProfile({
//     required this.id,
//     this.adminId,
//     this.deptId,
//     this.memberId,
//     this.socialId,
//     this.userType,
//     this.username,
//     this.nickname,
//     this.realname,
//     this.phone,
//     this.cardId,
//     this.inviteCode,
//     this.status,
//     this.icon,
//     this.gender,
//     this.birthday,
//     this.province,
//     this.city,
//     this.job,
//     this.personalizedSignature,
//     this.sourceType,
//     this.lastIp,
//     this.lastLocation,
//     this.createTime,
//     this.lastLoginTime,
//     this.spreadUid,
//     this.spreadCount,
//     this.spreadTime,
//     this.officialAuthnName,
//     this.type,
//     this.faceAuthnStatus,
//     required this.followStatus,
//     this.backdropUrl,
//   });

//   factory UserProfile.fromJson(Map<String, dynamic> json) {
//     return UserProfile(
//       id: json['id'] as String,
//       adminId: json['adminId'] as String?,
//       deptId: json['deptId'] as String?,
//       socialId: json['socialId'] as String?,
//       memberId: json['memberId'] as String?,
//       userType: json['userType'] as int?,
//       username: json['username'] as String?,
//       nickname: json['nickname'] as String?,
//       realname: json['realname'] as String?,
//       phone: json['phone'] as String?,
//       cardId: json['cardId'] as String?,
//       inviteCode: json['inviteCode'] as String?,
//       status: json['status'] as int?,
//       icon: json['icon'] as String?,
//       gender: json['gender'] as int?,
//       birthday: json['birthday'] as String?,
//       province: json['province'] as String?,
//       city: json['city'] as String?,
//       job: json['job'] as String?,
//       personalizedSignature: json['personalizedSignature'] as String?,
//       sourceType: json['sourceType'] as int?,
//       lastIp: json['lastIp'] as String?,
//       lastLocation: json['lastLocation'] as String?,
//       createTime: json['createTime'] as String?,
//       lastLoginTime: json['lastLoginTime'] as String?,
//       spreadUid: json['spreadUid'] as String?,
//       spreadCount: json['spreadCount'] as int?,
//       spreadTime: json['spreadTime'] as String?,
//       officialAuthnName: json['officialAuthnName'] as String?,
//       type: json['type'] as int?,
//       faceAuthnStatus: json['faceAuthnStatus'] as int?,
//       followStatus: json['followStatus'] as bool,
//       backdropUrl: json['backdropUrl'] as String?,
//     );
//   }
// }

/// 用户详情响应数据
class UserDetailModel {
  /// 收藏点赞总数
  final String? collectLikeTotal;

  /// 点赞数
  final String? likeCount;

  /// 收藏数
  final String? collectCount;

  /// 粉丝数
  final String? fansCount;

  /// 关注数
  final String? followCount;

  /// 会员信息
  final UserProfile umsMember;

  /// 问答总数
  final String? askCount;

  /// 视频总数
  final String? videoCount;

  /// 作品总数
  final String? worksCount;

  const UserDetailModel({
    this.collectLikeTotal,
    this.likeCount,
    this.collectCount,
    this.fansCount,
    this.followCount,
    this.askCount,
    this.videoCount,
    this.worksCount,
    required this.umsMember,
  });

  factory UserDetailModel.fromJson(Map<String, dynamic> json) {
    return UserDetailModel(
      collectLikeTotal: json['collectLikeTotal'] as String?,
      likeCount: json['likeCount'] as String?,
      collectCount: json['collectCount'] as String?,
      fansCount: json['fansCount'] as String?,
      followCount: json['followCount'] as String?,
      askCount: json['askCount'] as String?,
      videoCount: json['videoCount'] as String?,
      worksCount: json['worksCount'] as String?,
      umsMember:
          UserProfile.fromJson(json['umsMember'] as Map<String, dynamic>),
    );
  }

  String get avatar => umsMember.icon ?? "";
  String get personalizedSignature => umsMember.personalizedSignature ?? "";
}

/// 用户详情响应类型
typedef UserDetailResponse = BaseResponse<UserDetailModel>;

/// 创建用户详情响应的工厂方法
UserDetailResponse createUserDetailResponse(dynamic json) {
  return BaseResponse.fromJson(
    json,
    (data) => UserDetailModel.fromJson(data as Map<String, dynamic>),
  );
}

typedef UserProfileResponse = BaseResponse<UserProfile>;

UserProfileResponse createUserProfileResponse(dynamic json) {
  return BaseResponse.fromJson(
    json,
    (data) => UserProfile.fromJson(data as Map<String, dynamic>),
  );
}


/// 上传图片响应数据模型
class UploadImageData {
  /// 原始文件名
  final String origFileName;

  /// 文件名
  final String fileName;

  /// 扩展名
  final String extName;

  /// 文件大小
  final double fileSize;

  /// 文件URL
  final String url;

  /// 文件类型
  final String type;

  const UploadImageData({
    required this.origFileName,
    required this.fileName,
    required this.extName,
    required this.fileSize,
    required this.url,
    required this.type,
  });

  factory UploadImageData.fromJson(Map<String, dynamic> json) {
    return UploadImageData(
      origFileName: json['origFileName'] as String,
      fileName: json['fileName'] as String,
      extName: json['extName'] as String,
      fileSize: json['fileSize'] as double,
      url: json['url'] as String,
      type: json['type'] as String,
    );
  }
}

/// 上传图片响应类型
typedef UploadImageResp = BaseResponse<UploadImageData>;

/// 创建上传图片响应的工厂方法
UploadImageResp createUploadImageResp(dynamic json) {
  return BaseResponse.fromJson(
    json,
    (data) => UploadImageData.fromJson(data as Map<String, dynamic>),
  );
}

class UpdateProfileRequestEntity {
  final String? nickname;
  final int? gender; //1->男；2->女
  final String? icon;
  final String? birthday;
  final String? personalizedSignature;
  final int? collectionPublic; //收藏是否公开：0->公开；1->隐藏

  const UpdateProfileRequestEntity(
      {this.nickname,
      this.gender,
      this.icon,
      this.birthday,
      this.personalizedSignature,
      this.collectionPublic});

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (nickname != null) json['nickname'] = nickname;
    if (gender != null) json['gender'] = gender;
    if (birthday != null) json['birthday'] = birthday;
    if (personalizedSignature != null) {
      json['personalizedSignature'] = personalizedSignature;
    }
    if (icon != null) {
      json['icon'] = icon;
      json['iconType'] = 1;
    }
    if (collectionPublic != null) {
      json['collectionPublic'] = collectionPublic;
    }
    return json;
  }
}


class FeedbackEntity {
  final String content;
  final String pics;
  final String deviceBrand;
  final String deviceModel;
  final String deviceSystem;
  final String devicePlatform;

  const FeedbackEntity({
    required this.content,
    required this.pics,
    required this.deviceBrand,
    required this.deviceModel,
    required this.deviceSystem,
    required this.devicePlatform,
  });

  Map<String, dynamic> toJson() {
    return {
      'content': content,
      'pics': pics,
      'deviceBrand': deviceBrand,
      'deviceModel': deviceModel,
      'deviceSystem': deviceSystem,
      'devicePlatform': devicePlatform,
    };
  }
}
