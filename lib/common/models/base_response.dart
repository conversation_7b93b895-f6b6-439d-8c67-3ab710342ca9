import 'dart:ffi';

/// 基础响应实体
class BaseResponse<T> {
  /// 追踪ID
  final String? traceId;

  /// 响应代码
  final int code;

  /// 响应消息
  final String message;

  /// 响应数据
  final T? data;

  const BaseResponse({
    required this.traceId,
    required this.code,
    required this.message,
    required this.data,
  });

  factory BaseResponse.fromJson(
    dynamic json,
    T Function(dynamic json) fromJsonT,
  ) {
    final dynamic responseData = json['data'];
    return BaseResponse<T>(
      traceId: json['traceId'] as String?,
      code: json['code'] as int,
      message: json['message'] as String,
      data: responseData != null ? fromJsonT(responseData) : null,
    );
  }

  static BaseResponse<List<T>> fromJsonList<T>(
    dynamic json,
    T Function(dynamic json) fromJsonT,
  ) {
    final dynamic responseData = json['data'];

    List<T>? list;
    if (responseData is List) {
      list = responseData.map((item) {
        return fromJsonT(item);
      }).toList();
    }

    return BaseResponse<List<T>>(
      traceId: json['traceId'] as String?,
      code: json['code'] as int,
      message: json['message'] as String,
      data: list ?? [],
    );
  }
}

BaseResponse createBaseResponse(dynamic json) {
  return BaseResponse.fromJson(json, (data) => null);
}

BaseResponse<bool?> createBoolBaseResponse(dynamic json) {
  return BaseResponse.fromJson(json, (data) => data as bool?);
}

BaseResponse<int?> createIntBaseResponse(dynamic json) {
  return BaseResponse.fromJson(json, (data) {
    if (data is num) {
      return data.toInt();
    } else if (data is String) {
      return num.tryParse(data as String? ?? '')?.toInt();
    } else {
      return null;
    }
  });
}
