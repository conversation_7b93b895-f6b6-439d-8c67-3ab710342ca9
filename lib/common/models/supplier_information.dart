// To parse this JSON data, do
//
//     final supplierInformation = supplierInformationFromJson(jsonString);

import 'dart:convert';

SupplierInformation supplierInformationFromJson(String str) => SupplierInformation.fromJson(json.decode(str));

String supplierInformationToJson(SupplierInformation data) => json.encode(data.toJson());

class SupplierInformation {
    List<DataList>? paidTaxesDataList;
    List<DataList>? redInvoiceInfoDataList;

    SupplierInformation({
        this.paidTaxesDataList,
        this.redInvoiceInfoDataList,
    });

    factory SupplierInformation.fromJson(Map<String, dynamic> json) => SupplierInformation(
        paidTaxesDataList: json["paidTaxesDataList"] == null ? [] : List<DataList>.from(json["paidTaxesDataList"]!.map((x) => DataList.fromJson(x))),
        redInvoiceInfoDataList: json["redInvoiceInfoDataList"] == null ? [] : List<DataList>.from(json["redInvoiceInfoDataList"]!.map((x) => DataList.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "paidTaxesDataList": paidTaxesDataList == null ? [] : List<dynamic>.from(paidTaxesDataList!.map((x) => x.toJson())),
        "redInvoiceInfoDataList": redInvoiceInfoDataList == null ? [] : List<dynamic>.from(redInvoiceInfoDataList!.map((x) => x.toJson())),
    };
}

class DataList {
    int? sort;
    String? queryType;
    String? buySaleName;
    String? sumAmount;
    String? amountRate;
    bool? relatedStatus;
    String? buySaleCode;
    String? provinceName;

    DataList({
        this.sort,
        this.queryType,
        this.buySaleName,
        this.sumAmount,
        this.amountRate,
        this.relatedStatus,
        this.buySaleCode,
        this.provinceName,
    });

    factory DataList.fromJson(Map<String, dynamic> json) => DataList(
        sort: json["sort"],
        queryType: json["queryType"],
        buySaleName: json["buySaleName"],
        sumAmount: json["sumAmount"],
        amountRate: json["amountRate"],
        relatedStatus: json["relatedStatus"],
        buySaleCode: json["buySaleCode"],
        provinceName: json["provinceName"],
    );

    Map<String, dynamic> toJson() => {
        "sort": sort,
        "queryType": queryType,
        "buySaleName": buySaleName,
        "sumAmount": sumAmount,
        "amountRate": amountRate,
        "relatedStatus": relatedStatus,
        "buySaleCode": buySaleCode,
        "provinceName": provinceName,
    };
}
