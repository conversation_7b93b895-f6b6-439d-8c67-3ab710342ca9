import 'index.dart';

/**
 * 本文件中定义登录相关的模型
 */

/// 登录请求实体
class LoginByMobileRequestEntity {
  final String phone;
  final String code;
  final int sourceType;
  final String deviceId;
  final String deviceType;
  final int spreadUid;
  final int businessNo;

  const LoginByMobileRequestEntity({
    required this.phone,
    required this.code,
    this.sourceType = 10,
    this.deviceId = '',
    this.deviceType = '',
    this.spreadUid = 0,
    this.businessNo = 0,
  });

  Map<String, dynamic> toJson() => {
        'phone': phone,
        'code': code,
        'sourceType': sourceType,
        'deviceId': deviceId,
        'deviceType': deviceType,
        'spreadUid': spreadUid,
        'businessNo': businessNo,
      };
}

class LoginByPasswordRequestEntity {
  final String account;
  final String password;
  final int sourceType;
  final String deviceId;
  final String deviceType;
  final int spreadUid;
  final int businessNo;

  const LoginByPasswordRequestEntity({
    required this.account,
    required this.password,
    this.sourceType = 10,
    this.deviceId = '',
    this.deviceType = '',
    this.spreadUid = 0,
    this.businessNo = 0,
  });

  Map<String, dynamic> toJson() => {
        'account': account,
        'password': password,
        'sourceType': sourceType,
        'deviceId': deviceId,
        'deviceType': deviceType,
        'spreadUid': spreadUid,
        'businessNo': businessNo,
      };
}

/// 注册请求实体
class RegisterRequestEntity {
  // final String username;
  final String phone;
  final String password;
  final String authCode;
  final int sourceType; //来源类型：10->顺顺助融，11->助融报告，12->抖音小程序，13->官方网站，14->APP

  const RegisterRequestEntity({
    // required this.username,
    required this.phone,
    required this.password,
    required this.authCode,
    this.sourceType = 11,
  });

  Map<String, dynamic> toJson() => {
        // 'username': username,
        'phone': phone,
        'password': password,
        'authCode': authCode,
        'sourceType': sourceType,
      };
}

/// 登录响应数据,仅包含Response中的data数据
class LoginResponseModel {
  /// token前缀
  final String tokenHead;

  /// token过期时间
  final String expiresIn;

  /// token内容
  final String token;

  const LoginResponseModel({
    required this.tokenHead,
    required this.expiresIn,
    required this.token,
  });

  factory LoginResponseModel.fromJson(Map<String, dynamic> json) {
    return LoginResponseModel(
      tokenHead: json['tokenHead'] as String,
      expiresIn: json['expiresIn'] as String,
      token: json['token'] as String,
    );
  }
}

/// 登录响应类型
typedef LoginResponse = BaseResponse<LoginResponseModel>;

/// 创建登录响应的工厂方法
LoginResponse createLoginResponse(dynamic json) {
  return BaseResponse.fromJson(
    json,
    (data) => LoginResponseModel.fromJson(data as Map<String, dynamic>),
  );
}

/// 短信验证码响应类型
typedef GetSmsCodeResponse = BaseResponse<bool>;

/// 创建短信验证码响应的工厂方法
GetSmsCodeResponse createGetSmsCodeResponse(Map<String, dynamic> json) {
  return BaseResponse.fromJson(
    json,
    (data) => data as bool,
  );
}
