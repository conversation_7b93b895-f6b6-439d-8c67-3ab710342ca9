/// 指定访问用户信息模型
class SpecifyAccessUser {
  final int id;
  final int queryTaskId;
  final int queryShareId;
  final String queryShareCode;
  final String phone;
  final String createTime;

  SpecifyAccessUser({
    required this.id,
    required this.queryTaskId,
    required this.queryShareId,
    required this.queryShareCode,
    required this.phone,
    required this.createTime,
  });

  factory SpecifyAccessUser.fromJson(Map<String, dynamic> json) {
    return SpecifyAccessUser(
      id: _parseToInt(json['id']),
      queryTaskId: _parseToInt(json['queryTaskId']),
      queryShareId: _parseToInt(json['queryShareId']),
      queryShareCode: json['queryShareCode'] as String,
      phone: json['phone'] as String,
      createTime: json['createTime'] as String,
    );
  }

  /// 安全地将动态类型转换为int
  static int _parseToInt(dynamic value) {
    if (value is int) {
      return value;
    } else if (value is String) {
      return int.parse(value);
    } else {
      throw ArgumentError('Cannot convert $value (${value.runtimeType}) to int');
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'queryTaskId': queryTaskId,
      'queryShareId': queryShareId,
      'queryShareCode': queryShareCode,
      'phone': phone,
      'createTime': createTime,
    };
  }
} 