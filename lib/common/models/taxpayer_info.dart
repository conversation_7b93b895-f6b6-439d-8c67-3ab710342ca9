import 'package:freezed_annotation/freezed_annotation.dart';

part 'taxpayer_info.freezed.dart';
part 'taxpayer_info.g.dart';

/// 纳税人信息模型
@freezed
abstract class TaxpayerInfo with _$TaxpayerInfo {
  const factory TaxpayerInfo({
    /// 唯一标识符
    @Default('') String id,

    /// 会员ID
    @Default('') String memberId,

    /// 省份ID
    @Default('') String provinceId,

    /// 统一社会信用代码
    @Default('') String creditCode,

    /// 纳税人名称
    @Default('') String taxpayerName,

    /// 创建时间
    @Default('') String createTime,

    /// 更新时间
    @Default('') String updateTime,
  }) = _TaxpayerInfo;

  factory TaxpayerInfo.fromJson(Map<String, dynamic> json) =>
      _$TaxpayerInfoFromJson(json);
}
