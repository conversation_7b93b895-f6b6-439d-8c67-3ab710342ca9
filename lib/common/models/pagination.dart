/// 分页数据头部
class Pagination<T> {
  /// 当前页码
  final int pageNum;

  /// 每页大小
  final int pageSize;

  /// 总页数
  final int totalPage;

  /// 总记录数
  final int total;

  /// 收藏列表
  final List<T> list;

  const Pagination({
    required this.pageNum,
    required this.pageSize,
    required this.totalPage,
    required this.total,
    required this.list,
  });

  factory Pagination.empty() {
    return Pagination(
        pageNum: 0, pageSize: 0, totalPage: 0, total: 0, list: []);
  }

  bool get hasMorePage => pageNum < totalPage && list.length < total;

  factory Pagination.fromJson(
      Map<String, dynamic> json, T Function(Map<String, dynamic>) fromJson) {
    return Pagination(
      pageNum: json['pageNum'] as int,
      pageSize: json['pageSize'] as int,
      totalPage: json['totalPage'] as int,
      total: json['total'] as int,
      list: (json['list'] as List<dynamic>)
          .map((e) => fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  /// 创建一个包含更新值的新 Pagination 实例
  Pagination<T> copyWith({
    int? pageNum,
    int? pageSize,
    int? totalPage,
    int? total,
    List<T>? list,
  }) {
    return Pagination<T>(
      pageNum: pageNum ?? this.pageNum,
      pageSize: pageSize ?? this.pageSize,
      totalPage: totalPage ?? this.totalPage,
      total: total ?? this.total,
      list: list ?? this.list,
    );
  }
}
