// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'taxpayer_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TaxpayerInfo {
  /// 唯一标识符
  String get id;

  /// 会员ID
  String get memberId;

  /// 省份ID
  String get provinceId;

  /// 统一社会信用代码
  String get creditCode;

  /// 纳税人名称
  String get taxpayerName;

  /// 创建时间
  String get createTime;

  /// 更新时间
  String get updateTime;

  /// Create a copy of TaxpayerInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TaxpayerInfoCopyWith<TaxpayerInfo> get copyWith =>
      _$TaxpayerInfoCopyWithImpl<TaxpayerInfo>(
          this as TaxpayerInfo, _$identity);

  /// Serializes this TaxpayerInfo to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TaxpayerInfo &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.memberId, memberId) ||
                other.memberId == memberId) &&
            (identical(other.provinceId, provinceId) ||
                other.provinceId == provinceId) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.taxpayerName, taxpayerName) ||
                other.taxpayerName == taxpayerName) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, memberId, provinceId,
      creditCode, taxpayerName, createTime, updateTime);

  @override
  String toString() {
    return 'TaxpayerInfo(id: $id, memberId: $memberId, provinceId: $provinceId, creditCode: $creditCode, taxpayerName: $taxpayerName, createTime: $createTime, updateTime: $updateTime)';
  }
}

/// @nodoc
abstract mixin class $TaxpayerInfoCopyWith<$Res> {
  factory $TaxpayerInfoCopyWith(
          TaxpayerInfo value, $Res Function(TaxpayerInfo) _then) =
      _$TaxpayerInfoCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String memberId,
      String provinceId,
      String creditCode,
      String taxpayerName,
      String createTime,
      String updateTime});
}

/// @nodoc
class _$TaxpayerInfoCopyWithImpl<$Res> implements $TaxpayerInfoCopyWith<$Res> {
  _$TaxpayerInfoCopyWithImpl(this._self, this._then);

  final TaxpayerInfo _self;
  final $Res Function(TaxpayerInfo) _then;

  /// Create a copy of TaxpayerInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? memberId = null,
    Object? provinceId = null,
    Object? creditCode = null,
    Object? taxpayerName = null,
    Object? createTime = null,
    Object? updateTime = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      memberId: null == memberId
          ? _self.memberId
          : memberId // ignore: cast_nullable_to_non_nullable
              as String,
      provinceId: null == provinceId
          ? _self.provinceId
          : provinceId // ignore: cast_nullable_to_non_nullable
              as String,
      creditCode: null == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String,
      taxpayerName: null == taxpayerName
          ? _self.taxpayerName
          : taxpayerName // ignore: cast_nullable_to_non_nullable
              as String,
      createTime: null == createTime
          ? _self.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String,
      updateTime: null == updateTime
          ? _self.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _TaxpayerInfo implements TaxpayerInfo {
  const _TaxpayerInfo(
      {this.id = '',
      this.memberId = '',
      this.provinceId = '',
      this.creditCode = '',
      this.taxpayerName = '',
      this.createTime = '',
      this.updateTime = ''});
  factory _TaxpayerInfo.fromJson(Map<String, dynamic> json) =>
      _$TaxpayerInfoFromJson(json);

  /// 唯一标识符
  @override
  @JsonKey()
  final String id;

  /// 会员ID
  @override
  @JsonKey()
  final String memberId;

  /// 省份ID
  @override
  @JsonKey()
  final String provinceId;

  /// 统一社会信用代码
  @override
  @JsonKey()
  final String creditCode;

  /// 纳税人名称
  @override
  @JsonKey()
  final String taxpayerName;

  /// 创建时间
  @override
  @JsonKey()
  final String createTime;

  /// 更新时间
  @override
  @JsonKey()
  final String updateTime;

  /// Create a copy of TaxpayerInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TaxpayerInfoCopyWith<_TaxpayerInfo> get copyWith =>
      __$TaxpayerInfoCopyWithImpl<_TaxpayerInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TaxpayerInfoToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TaxpayerInfo &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.memberId, memberId) ||
                other.memberId == memberId) &&
            (identical(other.provinceId, provinceId) ||
                other.provinceId == provinceId) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.taxpayerName, taxpayerName) ||
                other.taxpayerName == taxpayerName) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, memberId, provinceId,
      creditCode, taxpayerName, createTime, updateTime);

  @override
  String toString() {
    return 'TaxpayerInfo(id: $id, memberId: $memberId, provinceId: $provinceId, creditCode: $creditCode, taxpayerName: $taxpayerName, createTime: $createTime, updateTime: $updateTime)';
  }
}

/// @nodoc
abstract mixin class _$TaxpayerInfoCopyWith<$Res>
    implements $TaxpayerInfoCopyWith<$Res> {
  factory _$TaxpayerInfoCopyWith(
          _TaxpayerInfo value, $Res Function(_TaxpayerInfo) _then) =
      __$TaxpayerInfoCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String memberId,
      String provinceId,
      String creditCode,
      String taxpayerName,
      String createTime,
      String updateTime});
}

/// @nodoc
class __$TaxpayerInfoCopyWithImpl<$Res>
    implements _$TaxpayerInfoCopyWith<$Res> {
  __$TaxpayerInfoCopyWithImpl(this._self, this._then);

  final _TaxpayerInfo _self;
  final $Res Function(_TaxpayerInfo) _then;

  /// Create a copy of TaxpayerInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? memberId = null,
    Object? provinceId = null,
    Object? creditCode = null,
    Object? taxpayerName = null,
    Object? createTime = null,
    Object? updateTime = null,
  }) {
    return _then(_TaxpayerInfo(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      memberId: null == memberId
          ? _self.memberId
          : memberId // ignore: cast_nullable_to_non_nullable
              as String,
      provinceId: null == provinceId
          ? _self.provinceId
          : provinceId // ignore: cast_nullable_to_non_nullable
              as String,
      creditCode: null == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String,
      taxpayerName: null == taxpayerName
          ? _self.taxpayerName
          : taxpayerName // ignore: cast_nullable_to_non_nullable
              as String,
      createTime: null == createTime
          ? _self.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String,
      updateTime: null == updateTime
          ? _self.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
