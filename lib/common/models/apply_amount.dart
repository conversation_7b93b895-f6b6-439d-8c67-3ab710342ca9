// To parse this JSON data, do
//
//     final enterpriseApplyAmount = enterpriseApplyAmountFromJson(jsonString);

import 'dart:convert';

import 'package:zrreport/common/models/invoice_info.dart';

EnterpriseApplyAmount enterpriseApplyAmountFromJson(String str) => EnterpriseApplyAmount.fromJson(json.decode(str));

String enterpriseApplyAmountToJson(EnterpriseApplyAmount data) => json.encode(data.toJson());

class EnterpriseApplyAmount {
    bool? applyRecords;
    String? applyAmountMonths1;
    String? applySum12;
    List<ApplyDataList>? applyInfoDataList;
    List<ApplyDataList>? applySalesDataList;
    List<EnterpriseMonthApplyList1>? enterpriseMonthApplyList;

    EnterpriseApplyAmount({
        this.applyRecords,
        this.applyAmountMonths1,
        this.applySum12,
        this.applyInfoDataList,
        this.applySalesDataList,
        this.enterpriseMonthApplyList,
    });

    factory EnterpriseApplyAmount.fromJson(Map<String, dynamic> json) => EnterpriseApplyAmount(
        applyRecords: json["applyRecords"],
        applyAmountMonths1: json["applyAmountMonths1"],
        applySum12: json["applySum12"],
        applyInfoDataList: json["applyInfoDataList"] == null ? [] : List<ApplyDataList>.from(json["applyInfoDataList"]!.map((x) => ApplyDataList.fromJson(x))),
        applySalesDataList: json["applySalesDataList"] == null ? [] : List<ApplyDataList>.from(json["applySalesDataList"]!.map((x) => ApplyDataList.fromJson(x))),
        enterpriseMonthApplyList: json["enterpriseMonthApplyList"] == null ? [] : List<EnterpriseMonthApplyList1>.from(json["enterpriseMonthApplyList"]!.map((x) => EnterpriseMonthApplyList1.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "applyRecords": applyRecords,
        "applyAmountMonths1": applyAmountMonths1,
        "applySum12": applySum12,
        "applyInfoDataList": applyInfoDataList == null ? [] : List<dynamic>.from(applyInfoDataList!.map((x) => x.toJson())),
        "applySalesDataList": applySalesDataList == null ? [] : List<dynamic>.from(applySalesDataList!.map((x) => x.toJson())),
        "enterpriseMonthApplyList": enterpriseMonthApplyList == null ? [] : List<dynamic>.from(enterpriseMonthApplyList!.map((x) => x.toJson())),
    };
}

class ApplyDataList {
    String? total;
    int? year;
    List<MonthDatum>? monthData;

    ApplyDataList({
        this.total,
        this.year,
        this.monthData,
    });

    factory ApplyDataList.fromJson(Map<String, dynamic> json) => ApplyDataList(
        total: json["total"],
        year: json["year"],
        monthData: json["monthData"] == null ? [] : List<MonthDatum>.from(json["monthData"]!.map((x) => MonthDatum.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "total": total,
        "year": year,
        "monthData": monthData == null ? [] : List<dynamic>.from(monthData!.map((x) => x.toJson())),
    };
}

class EnterpriseMonthApplyList1 {
    int? month;
    TaxEnterpriseApplyDate1? taxEnterpriseApplyDate;

    EnterpriseMonthApplyList1({
        this.month,
        this.taxEnterpriseApplyDate,
    });

    factory EnterpriseMonthApplyList1.fromJson(Map<String, dynamic> json) => EnterpriseMonthApplyList1(
        month: json["month"],
        taxEnterpriseApplyDate: json["taxEnterpriseApplyDate"] == null ? null : TaxEnterpriseApplyDate1.fromJson(json["taxEnterpriseApplyDate"]),
    );

    Map<String, dynamic> toJson() => {
        "month": month,
        "taxEnterpriseApplyDate": taxEnterpriseApplyDate?.toJson(),
    };
}

class TaxEnterpriseApplyDate1 {
    String? applyAmount;
    String? applyGrowthRate;
    String? applyOverGrowthRate;

    TaxEnterpriseApplyDate1({
        this.applyAmount,
        this.applyGrowthRate,
        this.applyOverGrowthRate,
    });

    factory TaxEnterpriseApplyDate1.fromJson(Map<String, dynamic> json) => TaxEnterpriseApplyDate1(
        applyAmount: json["applyAmount"],
        applyGrowthRate: json["applyGrowthRate"],
        applyOverGrowthRate: json["applyOverGrowthRate"],
    );

    Map<String, dynamic> toJson() => {
        "applyAmount": applyAmount,
        "applyGrowthRate": applyGrowthRate,
        "applyOverGrowthRate": applyOverGrowthRate,
    };
}
