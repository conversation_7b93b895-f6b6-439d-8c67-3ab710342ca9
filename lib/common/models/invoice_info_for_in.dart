// To parse this JSON data, do
//
//     final enterpriseInvoiceInfoForIn = enterpriseInvoiceInfoForInFromJson(jsonString);

import 'dart:convert';

import 'package:zrreport/common/models/invoice_info.dart';

EnterpriseInvoiceInfoForIn enterpriseInvoiceInfoForInFromJson(String str) =>
    EnterpriseInvoiceInfoForIn.fromJson(json.decode(str));

String enterpriseInvoiceInfoForInToJson(EnterpriseInvoiceInfoForIn data) =>
    json.encode(data.toJson());

class EnterpriseInvoiceInfoForIn {
  bool? billingRecords;
  String? billingDaysSum;
  String? taxVoidRateMonth12;
  int? breakMonthsSum12;
  int? breakMonthsSum;
  String? taxAmountGrowthRateMonth12;
  String? taxOutputNotInvoicedMonth12;
  String? earliestTime;
  String? latestTime;
  String? recentBillingGapdays;
  List<InvoiceForInDataList>? invoiceForInDataList;
  List<TaxEnterpriseMonthInvoiceForInVoList>?
      taxEnterpriseMonthInvoiceForInVoList;

  EnterpriseInvoiceInfoForIn({
    this.billingRecords,
    this.billingDaysSum,
    this.taxVoidRateMonth12,
    this.breakMonthsSum12,
    this.breakMonthsSum,
    this.taxAmountGrowthRateMonth12,
    this.taxOutputNotInvoicedMonth12,
    this.earliestTime,
    this.latestTime,
    this.recentBillingGapdays,
    this.invoiceForInDataList,
    this.taxEnterpriseMonthInvoiceForInVoList,
  });

  factory EnterpriseInvoiceInfoForIn.fromJson(Map<String, dynamic> json) =>
      EnterpriseInvoiceInfoForIn(
        billingRecords: json["billingRecords"],
        billingDaysSum: json["billingDaysSum"],
        taxVoidRateMonth12: json["taxVoidRateMonth12"],
        breakMonthsSum12: json["breakMonthsSum12"],
        breakMonthsSum: json["breakMonthsSum"],
        taxAmountGrowthRateMonth12: json["taxAmountGrowthRateMonth12"],
        taxOutputNotInvoicedMonth12: json["taxOutputNotInvoicedMonth12"],
        earliestTime: json["earliestTime"],
        latestTime: json["latestTime"],
        recentBillingGapdays: json["recentBillingGapdays"],
        invoiceForInDataList: json["invoiceForInDataList"] == null
            ? []
            : List<InvoiceForInDataList>.from(json["invoiceForInDataList"]!
                .map((x) => InvoiceForInDataList.fromJson(x))),
        taxEnterpriseMonthInvoiceForInVoList:
            json["taxEnterpriseMonthInvoiceForInVoList"] == null
                ? []
                : List<TaxEnterpriseMonthInvoiceForInVoList>.from(
                    json["taxEnterpriseMonthInvoiceForInVoList"]!.map((x) =>
                        TaxEnterpriseMonthInvoiceForInVoList.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "billingRecords": billingRecords,
        "billingDaysSum": billingDaysSum,
        "taxVoidRateMonth12": taxVoidRateMonth12,
        "breakMonthsSum12": breakMonthsSum12,
        "breakMonthsSum": breakMonthsSum,
        "taxAmountGrowthRateMonth12": taxAmountGrowthRateMonth12,
        "taxOutputNotInvoicedMonth12": taxOutputNotInvoicedMonth12,
        "earliestTime": earliestTime,
        "latestTime": latestTime,
        "recentBillingGapdays": recentBillingGapdays,
        "invoiceForInDataList": invoiceForInDataList == null
            ? []
            : List<dynamic>.from(invoiceForInDataList!.map((x) => x.toJson())),
        "taxEnterpriseMonthInvoiceForInVoList":
            taxEnterpriseMonthInvoiceForInVoList == null
                ? []
                : List<dynamic>.from(taxEnterpriseMonthInvoiceForInVoList!
                    .map((x) => x.toJson())),
      };
}

class InvoiceForInDataList {
  String? total;
  int? year;
  List<MonthDatum>? monthData;

  InvoiceForInDataList({
    this.total,
    this.year,
    this.monthData,
  });

  factory InvoiceForInDataList.fromJson(Map<String, dynamic> json) =>
      InvoiceForInDataList(
        total: json["total"],
        year: json["year"],
        monthData: json["monthData"] == null
            ? []
            : List<MonthDatum>.from(
                json["monthData"]!.map((x) => MonthDatum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "total": total,
        "year": year,
        "monthData": monthData == null
            ? []
            : List<dynamic>.from(monthData!.map((x) => x.toJson())),
      };
}

class TaxEnterpriseMonthInvoiceForInVoList {
  int? month;
  TaxEnterpriseInvoiceDateForInVo? taxEnterpriseInvoiceDateForInVo;

  TaxEnterpriseMonthInvoiceForInVoList({
    this.month,
    this.taxEnterpriseInvoiceDateForInVo,
  });

  factory TaxEnterpriseMonthInvoiceForInVoList.fromJson(
          Map<String, dynamic> json) =>
      TaxEnterpriseMonthInvoiceForInVoList(
        month: json["month"],
        taxEnterpriseInvoiceDateForInVo:
            json["taxEnterpriseInvoiceDateForInVo"] == null
                ? null
                : TaxEnterpriseInvoiceDateForInVo.fromJson(
                    json["taxEnterpriseInvoiceDateForInVo"]),
      );

  Map<String, dynamic> toJson() => {
        "month": month,
        "taxEnterpriseInvoiceDateForInVo":
            taxEnterpriseInvoiceDateForInVo?.toJson(),
      };
}

class TaxEnterpriseInvoiceDateForInVo {
  String? invoiceAmount;
  String? invoiceGrowthRate;
  String? invoiceOverGrowthRate;
  int? invoiceCount;
  String? invoiceMonthsNum;
  String? breakMonthsSum;
  String? invoiceRedCountRate;
  String? invoiceRedAmountRate;
  String? customerUpNum;
  String? customerDownNum;

  TaxEnterpriseInvoiceDateForInVo({
    this.invoiceAmount,
    this.invoiceGrowthRate,
    this.invoiceOverGrowthRate,
    this.invoiceCount,
    this.invoiceMonthsNum,
    this.breakMonthsSum,
    this.invoiceRedCountRate,
    this.invoiceRedAmountRate,
    this.customerUpNum,
    this.customerDownNum,
  });

  factory TaxEnterpriseInvoiceDateForInVo.fromJson(Map<String, dynamic> json) =>
      TaxEnterpriseInvoiceDateForInVo(
        invoiceAmount: json["invoiceAmount"],
        invoiceGrowthRate: json["invoiceGrowthRate"],
        invoiceOverGrowthRate: json["invoiceOverGrowthRate"],
        invoiceCount: json["invoiceCount"],
        invoiceMonthsNum: json["invoiceMonthsNum"],
        breakMonthsSum: json["breakMonthsSum"],
        invoiceRedCountRate: json["invoiceRedCountRate"],
        invoiceRedAmountRate: json["invoiceRedAmountRate"],
        customerUpNum: json["customerUpNum"],
        customerDownNum: json["customerDownNum"],
      );

  Map<String, dynamic> toJson() => {
        "invoiceAmount": invoiceAmount,
        "invoiceGrowthRate": invoiceGrowthRate,
        "invoiceOverGrowthRate": invoiceOverGrowthRate,
        "invoiceCount": invoiceCount,
        "invoiceMonthsNum": invoiceMonthsNum,
        "breakMonthsSum": breakMonthsSum,
        "invoiceRedCountRate": invoiceRedCountRate,
        "invoiceRedAmountRate": invoiceRedAmountRate,
        "customerUpNum": customerUpNum,
        "customerDownNum": customerDownNum,
      };
}
