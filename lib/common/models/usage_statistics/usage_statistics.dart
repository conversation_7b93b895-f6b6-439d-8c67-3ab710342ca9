import 'package:freezed_annotation/freezed_annotation.dart';

part 'usage_statistics.freezed.dart';
part 'usage_statistics.g.dart';

@freezed
abstract class UsageStatisticsModel with _$UsageStatisticsModel {
  const factory UsageStatisticsModel({
    required String freeQueryNum,
    required String availQueryNum,
    required String availNowTempNum,
    required String availPermanentNum,
    required String usedQueryNum,
    required String freeSignNum,
    required String usedSignNum,
    required String availSignNum,
    required String spreadCount,
    required String freeSpreadNum,
    required String usedSpreadNum,
    required String availSpreadNum,
    required String freeTempNum,
    required String usedNowTempNum,
    required String usedTempNum,
    required bool enableWhiteList,
    required bool enableUnlimited,
    required String usedUnlimitedNum,
    required String usedWhiteNum,
  }) = _UsageStatisticsModel;

  factory UsageStatisticsModel.fromJson(Map<String, dynamic> json) =>
      _$UsageStatisticsModelFromJson(json);
}
