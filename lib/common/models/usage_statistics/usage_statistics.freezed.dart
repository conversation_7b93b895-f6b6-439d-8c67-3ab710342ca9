// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'usage_statistics.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UsageStatisticsModel {
  String get freeQueryNum;
  String get availQueryNum;
  String get availNowTempNum;
  String get availPermanentNum;
  String get usedQueryNum;
  String get freeSignNum;
  String get usedSignNum;
  String get availSignNum;
  String get spreadCount;
  String get freeSpreadNum;
  String get usedSpreadNum;
  String get availSpreadNum;
  String get freeTempNum;
  String get usedNowTempNum;
  String get usedTempNum;
  bool get enableWhiteList;
  bool get enableUnlimited;
  String get usedUnlimitedNum;
  String get usedWhiteNum;

  /// Create a copy of UsageStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UsageStatisticsModelCopyWith<UsageStatisticsModel> get copyWith =>
      _$UsageStatisticsModelCopyWithImpl<UsageStatisticsModel>(
          this as UsageStatisticsModel, _$identity);

  /// Serializes this UsageStatisticsModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UsageStatisticsModel &&
            (identical(other.freeQueryNum, freeQueryNum) ||
                other.freeQueryNum == freeQueryNum) &&
            (identical(other.availQueryNum, availQueryNum) ||
                other.availQueryNum == availQueryNum) &&
            (identical(other.availNowTempNum, availNowTempNum) ||
                other.availNowTempNum == availNowTempNum) &&
            (identical(other.availPermanentNum, availPermanentNum) ||
                other.availPermanentNum == availPermanentNum) &&
            (identical(other.usedQueryNum, usedQueryNum) ||
                other.usedQueryNum == usedQueryNum) &&
            (identical(other.freeSignNum, freeSignNum) ||
                other.freeSignNum == freeSignNum) &&
            (identical(other.usedSignNum, usedSignNum) ||
                other.usedSignNum == usedSignNum) &&
            (identical(other.availSignNum, availSignNum) ||
                other.availSignNum == availSignNum) &&
            (identical(other.spreadCount, spreadCount) ||
                other.spreadCount == spreadCount) &&
            (identical(other.freeSpreadNum, freeSpreadNum) ||
                other.freeSpreadNum == freeSpreadNum) &&
            (identical(other.usedSpreadNum, usedSpreadNum) ||
                other.usedSpreadNum == usedSpreadNum) &&
            (identical(other.availSpreadNum, availSpreadNum) ||
                other.availSpreadNum == availSpreadNum) &&
            (identical(other.freeTempNum, freeTempNum) ||
                other.freeTempNum == freeTempNum) &&
            (identical(other.usedNowTempNum, usedNowTempNum) ||
                other.usedNowTempNum == usedNowTempNum) &&
            (identical(other.usedTempNum, usedTempNum) ||
                other.usedTempNum == usedTempNum) &&
            (identical(other.enableWhiteList, enableWhiteList) ||
                other.enableWhiteList == enableWhiteList) &&
            (identical(other.enableUnlimited, enableUnlimited) ||
                other.enableUnlimited == enableUnlimited) &&
            (identical(other.usedUnlimitedNum, usedUnlimitedNum) ||
                other.usedUnlimitedNum == usedUnlimitedNum) &&
            (identical(other.usedWhiteNum, usedWhiteNum) ||
                other.usedWhiteNum == usedWhiteNum));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        freeQueryNum,
        availQueryNum,
        availNowTempNum,
        availPermanentNum,
        usedQueryNum,
        freeSignNum,
        usedSignNum,
        availSignNum,
        spreadCount,
        freeSpreadNum,
        usedSpreadNum,
        availSpreadNum,
        freeTempNum,
        usedNowTempNum,
        usedTempNum,
        enableWhiteList,
        enableUnlimited,
        usedUnlimitedNum,
        usedWhiteNum
      ]);

  @override
  String toString() {
    return 'UsageStatisticsModel(freeQueryNum: $freeQueryNum, availQueryNum: $availQueryNum, availNowTempNum: $availNowTempNum, availPermanentNum: $availPermanentNum, usedQueryNum: $usedQueryNum, freeSignNum: $freeSignNum, usedSignNum: $usedSignNum, availSignNum: $availSignNum, spreadCount: $spreadCount, freeSpreadNum: $freeSpreadNum, usedSpreadNum: $usedSpreadNum, availSpreadNum: $availSpreadNum, freeTempNum: $freeTempNum, usedNowTempNum: $usedNowTempNum, usedTempNum: $usedTempNum, enableWhiteList: $enableWhiteList, enableUnlimited: $enableUnlimited, usedUnlimitedNum: $usedUnlimitedNum, usedWhiteNum: $usedWhiteNum)';
  }
}

/// @nodoc
abstract mixin class $UsageStatisticsModelCopyWith<$Res> {
  factory $UsageStatisticsModelCopyWith(UsageStatisticsModel value,
          $Res Function(UsageStatisticsModel) _then) =
      _$UsageStatisticsModelCopyWithImpl;
  @useResult
  $Res call(
      {String freeQueryNum,
      String availQueryNum,
      String availNowTempNum,
      String availPermanentNum,
      String usedQueryNum,
      String freeSignNum,
      String usedSignNum,
      String availSignNum,
      String spreadCount,
      String freeSpreadNum,
      String usedSpreadNum,
      String availSpreadNum,
      String freeTempNum,
      String usedNowTempNum,
      String usedTempNum,
      bool enableWhiteList,
      bool enableUnlimited,
      String usedUnlimitedNum,
      String usedWhiteNum});
}

/// @nodoc
class _$UsageStatisticsModelCopyWithImpl<$Res>
    implements $UsageStatisticsModelCopyWith<$Res> {
  _$UsageStatisticsModelCopyWithImpl(this._self, this._then);

  final UsageStatisticsModel _self;
  final $Res Function(UsageStatisticsModel) _then;

  /// Create a copy of UsageStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? freeQueryNum = null,
    Object? availQueryNum = null,
    Object? availNowTempNum = null,
    Object? availPermanentNum = null,
    Object? usedQueryNum = null,
    Object? freeSignNum = null,
    Object? usedSignNum = null,
    Object? availSignNum = null,
    Object? spreadCount = null,
    Object? freeSpreadNum = null,
    Object? usedSpreadNum = null,
    Object? availSpreadNum = null,
    Object? freeTempNum = null,
    Object? usedNowTempNum = null,
    Object? usedTempNum = null,
    Object? enableWhiteList = null,
    Object? enableUnlimited = null,
    Object? usedUnlimitedNum = null,
    Object? usedWhiteNum = null,
  }) {
    return _then(_self.copyWith(
      freeQueryNum: null == freeQueryNum
          ? _self.freeQueryNum
          : freeQueryNum // ignore: cast_nullable_to_non_nullable
              as String,
      availQueryNum: null == availQueryNum
          ? _self.availQueryNum
          : availQueryNum // ignore: cast_nullable_to_non_nullable
              as String,
      availNowTempNum: null == availNowTempNum
          ? _self.availNowTempNum
          : availNowTempNum // ignore: cast_nullable_to_non_nullable
              as String,
      availPermanentNum: null == availPermanentNum
          ? _self.availPermanentNum
          : availPermanentNum // ignore: cast_nullable_to_non_nullable
              as String,
      usedQueryNum: null == usedQueryNum
          ? _self.usedQueryNum
          : usedQueryNum // ignore: cast_nullable_to_non_nullable
              as String,
      freeSignNum: null == freeSignNum
          ? _self.freeSignNum
          : freeSignNum // ignore: cast_nullable_to_non_nullable
              as String,
      usedSignNum: null == usedSignNum
          ? _self.usedSignNum
          : usedSignNum // ignore: cast_nullable_to_non_nullable
              as String,
      availSignNum: null == availSignNum
          ? _self.availSignNum
          : availSignNum // ignore: cast_nullable_to_non_nullable
              as String,
      spreadCount: null == spreadCount
          ? _self.spreadCount
          : spreadCount // ignore: cast_nullable_to_non_nullable
              as String,
      freeSpreadNum: null == freeSpreadNum
          ? _self.freeSpreadNum
          : freeSpreadNum // ignore: cast_nullable_to_non_nullable
              as String,
      usedSpreadNum: null == usedSpreadNum
          ? _self.usedSpreadNum
          : usedSpreadNum // ignore: cast_nullable_to_non_nullable
              as String,
      availSpreadNum: null == availSpreadNum
          ? _self.availSpreadNum
          : availSpreadNum // ignore: cast_nullable_to_non_nullable
              as String,
      freeTempNum: null == freeTempNum
          ? _self.freeTempNum
          : freeTempNum // ignore: cast_nullable_to_non_nullable
              as String,
      usedNowTempNum: null == usedNowTempNum
          ? _self.usedNowTempNum
          : usedNowTempNum // ignore: cast_nullable_to_non_nullable
              as String,
      usedTempNum: null == usedTempNum
          ? _self.usedTempNum
          : usedTempNum // ignore: cast_nullable_to_non_nullable
              as String,
      enableWhiteList: null == enableWhiteList
          ? _self.enableWhiteList
          : enableWhiteList // ignore: cast_nullable_to_non_nullable
              as bool,
      enableUnlimited: null == enableUnlimited
          ? _self.enableUnlimited
          : enableUnlimited // ignore: cast_nullable_to_non_nullable
              as bool,
      usedUnlimitedNum: null == usedUnlimitedNum
          ? _self.usedUnlimitedNum
          : usedUnlimitedNum // ignore: cast_nullable_to_non_nullable
              as String,
      usedWhiteNum: null == usedWhiteNum
          ? _self.usedWhiteNum
          : usedWhiteNum // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _UsageStatisticsModel implements UsageStatisticsModel {
  const _UsageStatisticsModel(
      {required this.freeQueryNum,
      required this.availQueryNum,
      required this.availNowTempNum,
      required this.availPermanentNum,
      required this.usedQueryNum,
      required this.freeSignNum,
      required this.usedSignNum,
      required this.availSignNum,
      required this.spreadCount,
      required this.freeSpreadNum,
      required this.usedSpreadNum,
      required this.availSpreadNum,
      required this.freeTempNum,
      required this.usedNowTempNum,
      required this.usedTempNum,
      required this.enableWhiteList,
      required this.enableUnlimited,
      required this.usedUnlimitedNum,
      required this.usedWhiteNum});
  factory _UsageStatisticsModel.fromJson(Map<String, dynamic> json) =>
      _$UsageStatisticsModelFromJson(json);

  @override
  final String freeQueryNum;
  @override
  final String availQueryNum;
  @override
  final String availNowTempNum;
  @override
  final String availPermanentNum;
  @override
  final String usedQueryNum;
  @override
  final String freeSignNum;
  @override
  final String usedSignNum;
  @override
  final String availSignNum;
  @override
  final String spreadCount;
  @override
  final String freeSpreadNum;
  @override
  final String usedSpreadNum;
  @override
  final String availSpreadNum;
  @override
  final String freeTempNum;
  @override
  final String usedNowTempNum;
  @override
  final String usedTempNum;
  @override
  final bool enableWhiteList;
  @override
  final bool enableUnlimited;
  @override
  final String usedUnlimitedNum;
  @override
  final String usedWhiteNum;

  /// Create a copy of UsageStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UsageStatisticsModelCopyWith<_UsageStatisticsModel> get copyWith =>
      __$UsageStatisticsModelCopyWithImpl<_UsageStatisticsModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UsageStatisticsModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UsageStatisticsModel &&
            (identical(other.freeQueryNum, freeQueryNum) ||
                other.freeQueryNum == freeQueryNum) &&
            (identical(other.availQueryNum, availQueryNum) ||
                other.availQueryNum == availQueryNum) &&
            (identical(other.availNowTempNum, availNowTempNum) ||
                other.availNowTempNum == availNowTempNum) &&
            (identical(other.availPermanentNum, availPermanentNum) ||
                other.availPermanentNum == availPermanentNum) &&
            (identical(other.usedQueryNum, usedQueryNum) ||
                other.usedQueryNum == usedQueryNum) &&
            (identical(other.freeSignNum, freeSignNum) ||
                other.freeSignNum == freeSignNum) &&
            (identical(other.usedSignNum, usedSignNum) ||
                other.usedSignNum == usedSignNum) &&
            (identical(other.availSignNum, availSignNum) ||
                other.availSignNum == availSignNum) &&
            (identical(other.spreadCount, spreadCount) ||
                other.spreadCount == spreadCount) &&
            (identical(other.freeSpreadNum, freeSpreadNum) ||
                other.freeSpreadNum == freeSpreadNum) &&
            (identical(other.usedSpreadNum, usedSpreadNum) ||
                other.usedSpreadNum == usedSpreadNum) &&
            (identical(other.availSpreadNum, availSpreadNum) ||
                other.availSpreadNum == availSpreadNum) &&
            (identical(other.freeTempNum, freeTempNum) ||
                other.freeTempNum == freeTempNum) &&
            (identical(other.usedNowTempNum, usedNowTempNum) ||
                other.usedNowTempNum == usedNowTempNum) &&
            (identical(other.usedTempNum, usedTempNum) ||
                other.usedTempNum == usedTempNum) &&
            (identical(other.enableWhiteList, enableWhiteList) ||
                other.enableWhiteList == enableWhiteList) &&
            (identical(other.enableUnlimited, enableUnlimited) ||
                other.enableUnlimited == enableUnlimited) &&
            (identical(other.usedUnlimitedNum, usedUnlimitedNum) ||
                other.usedUnlimitedNum == usedUnlimitedNum) &&
            (identical(other.usedWhiteNum, usedWhiteNum) ||
                other.usedWhiteNum == usedWhiteNum));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        freeQueryNum,
        availQueryNum,
        availNowTempNum,
        availPermanentNum,
        usedQueryNum,
        freeSignNum,
        usedSignNum,
        availSignNum,
        spreadCount,
        freeSpreadNum,
        usedSpreadNum,
        availSpreadNum,
        freeTempNum,
        usedNowTempNum,
        usedTempNum,
        enableWhiteList,
        enableUnlimited,
        usedUnlimitedNum,
        usedWhiteNum
      ]);

  @override
  String toString() {
    return 'UsageStatisticsModel(freeQueryNum: $freeQueryNum, availQueryNum: $availQueryNum, availNowTempNum: $availNowTempNum, availPermanentNum: $availPermanentNum, usedQueryNum: $usedQueryNum, freeSignNum: $freeSignNum, usedSignNum: $usedSignNum, availSignNum: $availSignNum, spreadCount: $spreadCount, freeSpreadNum: $freeSpreadNum, usedSpreadNum: $usedSpreadNum, availSpreadNum: $availSpreadNum, freeTempNum: $freeTempNum, usedNowTempNum: $usedNowTempNum, usedTempNum: $usedTempNum, enableWhiteList: $enableWhiteList, enableUnlimited: $enableUnlimited, usedUnlimitedNum: $usedUnlimitedNum, usedWhiteNum: $usedWhiteNum)';
  }
}

/// @nodoc
abstract mixin class _$UsageStatisticsModelCopyWith<$Res>
    implements $UsageStatisticsModelCopyWith<$Res> {
  factory _$UsageStatisticsModelCopyWith(_UsageStatisticsModel value,
          $Res Function(_UsageStatisticsModel) _then) =
      __$UsageStatisticsModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String freeQueryNum,
      String availQueryNum,
      String availNowTempNum,
      String availPermanentNum,
      String usedQueryNum,
      String freeSignNum,
      String usedSignNum,
      String availSignNum,
      String spreadCount,
      String freeSpreadNum,
      String usedSpreadNum,
      String availSpreadNum,
      String freeTempNum,
      String usedNowTempNum,
      String usedTempNum,
      bool enableWhiteList,
      bool enableUnlimited,
      String usedUnlimitedNum,
      String usedWhiteNum});
}

/// @nodoc
class __$UsageStatisticsModelCopyWithImpl<$Res>
    implements _$UsageStatisticsModelCopyWith<$Res> {
  __$UsageStatisticsModelCopyWithImpl(this._self, this._then);

  final _UsageStatisticsModel _self;
  final $Res Function(_UsageStatisticsModel) _then;

  /// Create a copy of UsageStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? freeQueryNum = null,
    Object? availQueryNum = null,
    Object? availNowTempNum = null,
    Object? availPermanentNum = null,
    Object? usedQueryNum = null,
    Object? freeSignNum = null,
    Object? usedSignNum = null,
    Object? availSignNum = null,
    Object? spreadCount = null,
    Object? freeSpreadNum = null,
    Object? usedSpreadNum = null,
    Object? availSpreadNum = null,
    Object? freeTempNum = null,
    Object? usedNowTempNum = null,
    Object? usedTempNum = null,
    Object? enableWhiteList = null,
    Object? enableUnlimited = null,
    Object? usedUnlimitedNum = null,
    Object? usedWhiteNum = null,
  }) {
    return _then(_UsageStatisticsModel(
      freeQueryNum: null == freeQueryNum
          ? _self.freeQueryNum
          : freeQueryNum // ignore: cast_nullable_to_non_nullable
              as String,
      availQueryNum: null == availQueryNum
          ? _self.availQueryNum
          : availQueryNum // ignore: cast_nullable_to_non_nullable
              as String,
      availNowTempNum: null == availNowTempNum
          ? _self.availNowTempNum
          : availNowTempNum // ignore: cast_nullable_to_non_nullable
              as String,
      availPermanentNum: null == availPermanentNum
          ? _self.availPermanentNum
          : availPermanentNum // ignore: cast_nullable_to_non_nullable
              as String,
      usedQueryNum: null == usedQueryNum
          ? _self.usedQueryNum
          : usedQueryNum // ignore: cast_nullable_to_non_nullable
              as String,
      freeSignNum: null == freeSignNum
          ? _self.freeSignNum
          : freeSignNum // ignore: cast_nullable_to_non_nullable
              as String,
      usedSignNum: null == usedSignNum
          ? _self.usedSignNum
          : usedSignNum // ignore: cast_nullable_to_non_nullable
              as String,
      availSignNum: null == availSignNum
          ? _self.availSignNum
          : availSignNum // ignore: cast_nullable_to_non_nullable
              as String,
      spreadCount: null == spreadCount
          ? _self.spreadCount
          : spreadCount // ignore: cast_nullable_to_non_nullable
              as String,
      freeSpreadNum: null == freeSpreadNum
          ? _self.freeSpreadNum
          : freeSpreadNum // ignore: cast_nullable_to_non_nullable
              as String,
      usedSpreadNum: null == usedSpreadNum
          ? _self.usedSpreadNum
          : usedSpreadNum // ignore: cast_nullable_to_non_nullable
              as String,
      availSpreadNum: null == availSpreadNum
          ? _self.availSpreadNum
          : availSpreadNum // ignore: cast_nullable_to_non_nullable
              as String,
      freeTempNum: null == freeTempNum
          ? _self.freeTempNum
          : freeTempNum // ignore: cast_nullable_to_non_nullable
              as String,
      usedNowTempNum: null == usedNowTempNum
          ? _self.usedNowTempNum
          : usedNowTempNum // ignore: cast_nullable_to_non_nullable
              as String,
      usedTempNum: null == usedTempNum
          ? _self.usedTempNum
          : usedTempNum // ignore: cast_nullable_to_non_nullable
              as String,
      enableWhiteList: null == enableWhiteList
          ? _self.enableWhiteList
          : enableWhiteList // ignore: cast_nullable_to_non_nullable
              as bool,
      enableUnlimited: null == enableUnlimited
          ? _self.enableUnlimited
          : enableUnlimited // ignore: cast_nullable_to_non_nullable
              as bool,
      usedUnlimitedNum: null == usedUnlimitedNum
          ? _self.usedUnlimitedNum
          : usedUnlimitedNum // ignore: cast_nullable_to_non_nullable
              as String,
      usedWhiteNum: null == usedWhiteNum
          ? _self.usedWhiteNum
          : usedWhiteNum // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
