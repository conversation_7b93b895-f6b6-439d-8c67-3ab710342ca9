// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'usage_statistics.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UsageStatisticsModel _$UsageStatisticsModelFromJson(
        Map<String, dynamic> json) =>
    _UsageStatisticsModel(
      freeQueryNum: json['freeQueryNum'] as String,
      availQueryNum: json['availQueryNum'] as String,
      availNowTempNum: json['availNowTempNum'] as String,
      availPermanentNum: json['availPermanentNum'] as String,
      usedQueryNum: json['usedQueryNum'] as String,
      freeSignNum: json['freeSignNum'] as String,
      usedSignNum: json['usedSignNum'] as String,
      availSignNum: json['availSignNum'] as String,
      spreadCount: json['spreadCount'] as String,
      freeSpreadNum: json['freeSpreadNum'] as String,
      usedSpreadNum: json['usedSpreadNum'] as String,
      availSpreadNum: json['availSpreadNum'] as String,
      freeTempNum: json['freeTempNum'] as String,
      usedNowTempNum: json['usedNowTempNum'] as String,
      usedTempNum: json['usedTempNum'] as String,
      enableWhiteList: json['enableWhiteList'] as bool,
      enableUnlimited: json['enableUnlimited'] as bool,
      usedUnlimitedNum: json['usedUnlimitedNum'] as String,
      usedWhiteNum: json['usedWhiteNum'] as String,
    );

Map<String, dynamic> _$UsageStatisticsModelToJson(
        _UsageStatisticsModel instance) =>
    <String, dynamic>{
      'freeQueryNum': instance.freeQueryNum,
      'availQueryNum': instance.availQueryNum,
      'availNowTempNum': instance.availNowTempNum,
      'availPermanentNum': instance.availPermanentNum,
      'usedQueryNum': instance.usedQueryNum,
      'freeSignNum': instance.freeSignNum,
      'usedSignNum': instance.usedSignNum,
      'availSignNum': instance.availSignNum,
      'spreadCount': instance.spreadCount,
      'freeSpreadNum': instance.freeSpreadNum,
      'usedSpreadNum': instance.usedSpreadNum,
      'availSpreadNum': instance.availSpreadNum,
      'freeTempNum': instance.freeTempNum,
      'usedNowTempNum': instance.usedNowTempNum,
      'usedTempNum': instance.usedTempNum,
      'enableWhiteList': instance.enableWhiteList,
      'enableUnlimited': instance.enableUnlimited,
      'usedUnlimitedNum': instance.usedUnlimitedNum,
      'usedWhiteNum': instance.usedWhiteNum,
    };
