import 'dart:convert';

import 'package:zrreport/common/index.dart';

class ArticleTag {
  String? id;
  String? name;

  ArticleTag({this.id, this.name});

  factory ArticleTag.fromJson(Map<String, dynamic> json) {
    return ArticleTag(id: json['id'], name: json['name']);
  }
} 


/// 收藏项目
class Article {
  /// 会员ID
  final String? memberId;

  /// 用户ID
  final String? userId;

  /// 收藏ID
  final String? id;

  /// 平台类型
  final String? platformType;

  /// 会员头像
  final String? memberIcon;

  /// 标题
  final String? title;

  /// 摘要
  final String? summary;

  /// 类型
  // final ContentType? type;

  /// 用户类型
  final int? userType;

  /// 分类名称
  final String? cateName;

  /// 昵称
  final String? nickname;

  /// 内容
  final String? content;

  /// 图片URL
  final String? imageUrl;

  /// 链接URL
  final String? linkUrl;

  /// 访问次数
  final int? visitCount;

  final String? videoId;

  /// 分类ID
  final String? cateId;

  /// 点赞数
  int? likeCount;

  /// 收藏数
  int? collectCount;

  /// 评论数
  int? commentCount;

  /// 创建时间
  final String? createTime;

  /// 收藏时间
  final String? collectTime;

  /// 创建时间字符串
  final String? createTimeStr;

  /// 关注状态
  bool? followStatus;

  /// 点赞状态
  bool? likeStatus;

  /// 收藏状态
  bool? collectStatus;

  /// 来源URL
  final String? sourceUrl;

  /// 评论状态
  final int? commentStatus;

  /// 产品列表
  final List<dynamic>? productList;

  /// 文章评论
  final List<dynamic>? articleCommentVo;

  final List<ArticleTag>? tagList;

  Article({
    this.memberId,
    this.userId,
    this.id,
    this.platformType,
    this.memberIcon,
    this.title,
    this.summary,
    // this.type,
    this.userType,
    this.cateName,
    this.nickname,
    this.content,
    this.imageUrl,
    this.linkUrl,
    this.visitCount,
    this.cateId,
    this.likeCount,
    this.collectCount,
    this.commentCount,
    this.createTime,
    this.collectTime,
    this.createTimeStr,
    this.followStatus,
    this.likeStatus,
    this.collectStatus,
    this.sourceUrl,
    this.commentStatus,
    this.productList,
    this.articleCommentVo,
    this.videoId,
    this.tagList,
  });

  static Article? fromJsonString(String jsonString) {
    try {
      var contentMap = json.decode(jsonString);
      final item = Article.fromJson(contentMap);
      return item;
    } catch (e) {
      return null;
    }
  }

  factory Article.fromJson(Map<String, dynamic> json) {
    return Article(
      memberId: json['memberId'] as String?,
      userId: json['userId'] as String?,
      id: json['id'] as String?,
      platformType: json['platformType'] as String?,
      memberIcon: json['memberIcon'] as String?,
      title: json['title'] as String?,
      summary: json['summary'] as String?,
      // type: ContentType.getType(json['type'] as int? ?? 0),
      userType: json['userType'] as int?,
      cateName: json['cateName'] as String?,
      nickname: json['nickname'] as String?,
      content: json['content'] as String?,
      imageUrl: json['imageUrl'] as String?,
      linkUrl: json['linkUrl'] as String?,
      visitCount: json['visitCount'] as int?,
      cateId: json['cateId'] as String?,
      likeCount: json['likeCount'] as int?,
      collectCount: json['collectCount'] as int?,
      commentCount: json['commentCount'] as int?,
      createTime: json['createTime'] as String?,
      collectTime: json['collectTime'] as String?,
      createTimeStr: json['createTimeStr'] as String?,
      followStatus: json['followStatus'] as bool?,
      likeStatus: json['likeStatus'] as bool?,
      collectStatus: json['collectStatus'] as bool?,
      sourceUrl: json['sourceUrl'] as String?,
      commentStatus: json['commentStatus'] as int?,
      productList: json['productList'] as List<dynamic>?,
      articleCommentVo: json['articleCommentVo'] as List<dynamic>?,
      videoId: json['videoId'] as String?,
      tagList: (json['tagList'] as List<dynamic>?)
          ?.map((e) => ArticleTag.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  String get thumbUrl {
    final images = (imageUrl ?? '').split(',');
    if (images.isNotEmpty) {
      return images.first;
    }
    return '';
  }
}

typedef UserCollectionListResp = BaseResponse<Pagination<Article>>;
UserCollectionListResp createUserCollectionListResp(dynamic json) {
  return BaseResponse.fromJson(
    json,
    (data) => Pagination.fromJson(data as Map<String, dynamic>, (data) {
      return Article.fromJson(data);
    }),
  );
}

class ListEntity {
  int pageNum;
  int pageSize;

  ListEntity(
    this.pageNum,
    this.pageSize,
  );

  Map<String, dynamic> toJson() {
    return {
      'pageNum': pageNum,
      'pageSize': pageSize,
      'platformType': Constants.platform
    };
  }
}


class ArticleDetail {
  final String? memberId;
  final String? userId;
  final String? id;
  final String? cateId;
  final String? tagIds;
  final String? title;
  final String? summary;
  final String? content;
  final String? cateName;
  final String? nickname;
  final String? memberIcon;
  final String? imageUrl;
  final int? userType;
  final int? publishStatus;
  final int? verifyStatus;
  final String? hotTopicId;
  final String? publishTime;
  final String? verifyTime;
  final int? visitCount;
  final int? likeCount;
  final int? collectCount;
  final int? commentCount;
  final String? relation;
  final int? tasksStatus;
  final int? type;
  final String? videoId;
  final String? linkUrl;
  final String? sourceType;
  final bool? followStatus;
  final String? createTime;
  final List<dynamic>? productList;
  final List<dynamic>? articleHotTopicList;
  final bool? likeStatus;
  final bool? collectStatus;
  final int? commentStatus;
  final int? choiceAnswers;
  final String? sourceUrl;
  final int? recommendStatus;
  final String? platformType;
  final List<ArticleTag>? tagList;

  ArticleDetail({
    this.memberId,
    this.userId,
    this.id,
    this.cateId,
    this.tagIds,
    this.title,
    this.summary,
    this.content,
    this.cateName,
    this.nickname,
    this.memberIcon,
    this.imageUrl,
    this.userType,
    this.publishStatus,
    this.verifyStatus,
    this.hotTopicId,
    this.publishTime,
    this.verifyTime,
    this.visitCount,
    this.likeCount,
    this.collectCount,
    this.commentCount,
    this.relation,
    this.tasksStatus,
    this.type,
    this.videoId,
    this.linkUrl,
    this.sourceType,
    this.followStatus,
    this.createTime,
    this.productList,
    this.articleHotTopicList,
    this.likeStatus,
    this.collectStatus,
    this.commentStatus,
    this.choiceAnswers,
    this.sourceUrl,
    this.recommendStatus,
    this.platformType,
    this.tagList,
  });

  factory ArticleDetail.fromJson(Map<String, dynamic> json) {
    return ArticleDetail(
      memberId: json['memberId'],
      userId: json['userId'],
      id: json['id'],
      cateId: json['cateId'],
      tagIds: json['tagIds'],
      title: json['title'],
      summary: json['summary'],
      content: json['content'],
      cateName: json['cateName'],
      nickname: json['nickname'],
      memberIcon: json['memberIcon'],
      imageUrl: json['imageUrl'],
      userType: json['userType'],
      publishStatus: json['publishStatus'],
      verifyStatus: json['verifyStatus'],
      hotTopicId: json['hotTopicId'],
      publishTime: json['publishTime'],
      verifyTime: json['verifyTime'],
      visitCount: json['visitCount'],
      likeCount: json['likeCount'],
      collectCount: json['collectCount'],
      commentCount: json['commentCount'],
      relation: json['relation'],
      tasksStatus: json['tasksStatus'],
      type: json['type'],
      videoId: json['videoId'],
      linkUrl: json['linkUrl'],
      sourceType: json['sourceType'],
      followStatus: json['followStatus'],
      createTime: json['createTime'],
      productList: json['productList'],
      articleHotTopicList: json['articleHotTopicList'],
      likeStatus: json['likeStatus'],
      collectStatus: json['collectStatus'],
      commentStatus: json['commentStatus'],
      choiceAnswers: json['choiceAnswers'],
      sourceUrl: json['sourceUrl'],
      recommendStatus: json['recommendStatus'],
      platformType: json['platformType'],
      tagList: (json['tagList'] as List<dynamic>?)
          ?.map((e) => ArticleTag.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}
