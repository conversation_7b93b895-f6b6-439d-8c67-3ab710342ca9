class Province {
  String? id;
  String? name;
  String? pinyin;
  String? areaId;
  String? code;
  int? status;
  int? deleteStatus;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? invoiceStatus; //发票信息 1 正常使用 0 维护中
  String? taxStatus; //税务信息状态 1 正常使用 0 维护中
  String? startTime;
  String? endTime;
  String? taxLoginUrl;


  Province({
    required this.id,
    required this.name,
    required this.pinyin,
    required this.areaId,
    this.code,
    required this.status,
    required this.deleteStatus,
    required this.createBy,
    required this.createTime,
    required this.updateBy,
    required this.updateTime,
    required this.invoiceStatus,
    required this.taxStatus,
    this.startTime,
    this.endTime,
    required this.taxLoginUrl,
  });

  factory Province.fromJson(Map<String, dynamic> json) {
    return Province(
      id: json['id'] as String?,
      name: json['name'] as String?,
      pinyin: json['pinyin'] as String?,
      areaId: json['areaId'] as String?,
      code: json['code'] as String?,
      status: json['status'] as int?,
      deleteStatus: json['deleteStatus'] as int?,
      createBy: json['createBy'] as String?,
      createTime: json['createTime'] as String?,
      updateBy: json['updateBy'] as String?,
      updateTime: json['updateTime'] as String?,
      invoiceStatus: json['invoiceStatus'] as String?,
      taxStatus: json['taxStatus'] as String?,
      startTime: json['startTime'] as String?,
      endTime: json['endTime'] as String?,
      taxLoginUrl: json['taxLoginUrl'] as String?,
    );
  }
}
