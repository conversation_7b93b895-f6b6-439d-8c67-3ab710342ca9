import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

EasyRefreshController useEasyRefreshController({
  bool controlFinishRefresh = false,
  bool controlFinishLoad = false,
}) {
  final controller = useRef(EasyRefreshController(
    controlFinishRefresh: true,
    controlFinishLoad: true,
  )).value;

  useEffect(() {
    return () {
      controller.dispose();
    };
  }, [controller]);

  return controller;
}
