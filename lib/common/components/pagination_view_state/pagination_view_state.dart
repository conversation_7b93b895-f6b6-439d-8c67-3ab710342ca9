// 定义 Loading 状态类
class PaginationLoading<T> {
  const PaginationLoading();
}

// 定义 Empty 状态类
class PaginationEmpty<T> {
  const PaginationEmpty();
}

// 定义 Ready 状态类
class PaginationReady<T> {
  final T data;
  const PaginationReady(this.data);
}

// 定义 Error 状态类
class PaginationError<T> {
  final String error;
  const PaginationError({required this.error});
}

///
// 定义 ListViewState 类
class PaginationViewState<T> {
  final dynamic _state;

  PaginationViewState.loading() : _state = PaginationLoading<T>();
  PaginationViewState.empty() : _state = PaginationEmpty<T>();
  PaginationViewState.ready(T data) : _state = PaginationReady<T>(data);
  PaginationViewState.error({required String error})
      : _state = PaginationError<T>(error: error);

  R when<R>({
    required R Function() empty,
    required R Function(T data) ready,
    required R Function(String error) error,
    required R Function() loading,
  }) {
    if (_state is PaginationLoading<T>) {
      return loading();
    } else if (_state is PaginationEmpty<T>) {
      return empty();
    } else if (_state is PaginationReady<T>) {
      return ready((_state).data);
    } else if (_state is PaginationError<T>) {
      return error((_state).error);
    }
    throw Exception('Unknown state');
  }
}
