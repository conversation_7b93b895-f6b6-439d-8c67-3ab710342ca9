import 'package:dio/dio.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';

import 'pagination_view_state.dart';

typedef ToastFunction = void Function(String error, {PaginationError? e});

class PaginationViewStateNotifier<T>
    extends StateNotifier<PaginationViewState<List<T>>> {
  PaginationViewStateNotifier({
    required this.fetchItems,
    this.pageSize = 10,
    this.pageIndex = 1,
  }) : super(PaginationViewState.loading()) {
    init();
  }
  final EasyRefreshController _refreshController = EasyRefreshController(
    controlFinishLoad: true,
    controlFinishRefresh: true,
  );

  EasyRefreshController get refreshController => _refreshController;

  final Future<Pagination<T>?> Function(int pageIndex, int pageSize,
      {CancelToken? cancelToken}) fetchItems;

  int pageSize = 10;
  int pageIndex = 1;
  List<T> _items = [];

  CancelToken? _cancelToken;

  void init() {
    firstLoadPage();
  }

  Future<void> firstLoadPage() async {
    try {
      _cancelToken = CancelToken();

      final pagination =
          await fetchItems(1, pageSize, cancelToken: _cancelToken);

      if (pagination != null) {
        pageIndex = pagination.pageNum;

        final list = pagination.list;
        if (list.isEmpty) {
          state = PaginationViewState.empty();
        } else {
          _items = list;
          state = PaginationViewState.ready(_items);
        }
        _refreshController.finishRefresh(IndicatorResult.success);

        if (list.length < pageSize) {
          _refreshController.finishLoad(IndicatorResult.noMore);
        }
      } else {
        assert(false);
      }
    } catch (e) {
      state = PaginationViewState.error(error: e.toString());
    } finally {
      _cancelToken = null;
    }
  }

  Future<void> loadMore() async {
    try {
      _cancelToken = CancelToken();
      final pagination =
          await fetchItems(pageIndex + 1, pageSize, cancelToken: _cancelToken);

      if (pagination != null) {
        pageIndex = pagination.pageNum;
        final list = pagination.list;
        if (list.isNotEmpty) {
          _items.addAll(list);
          state = PaginationViewState.ready(_items);
        } else {}

        _refreshController.finishLoad(list.length < pageSize
            ? IndicatorResult.noMore
            : IndicatorResult.success);
      } else {
        assert(false);
        _refreshController.finishLoad(IndicatorResult.fail);
      }
    } catch (e) {
      _refreshController.finishLoad(IndicatorResult.fail);
    } finally {
      _cancelToken = null;
    }
  }

  void onSearch() {
    _cancelToken?.cancel();
    reset();
    firstLoadPage();
  }

  reset() {
    pageIndex = 1;
    _items = [];
  }

  @override
  void dispose() {
    _cancelToken?.cancel();
    _refreshController.dispose();
    super.dispose();
  }
}
