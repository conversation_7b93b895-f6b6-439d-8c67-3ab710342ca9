import 'dart:async';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:get/get.dart';
import 'package:zrreport/common/index.dart';

/// 个人主页，加载收藏列表，我的作品列表
abstract class BasePaginationController<T> extends GetxController
    with StateMixin<List<T>> {
  // 收藏列表数据
  List<T> items = [];

  // 是否还有更多数据
  final hasMore = true.obs;

  // 当前页码
  int currentPage = 1;

  // 每页大小
  final int pageSize = 20;

  // 刷新控制器
  EasyRefreshController easyRefreshController = EasyRefreshController(
    controlFinishRefresh: true,
    controlFinishLoad: true,
  );

  @override
  void onInit() {
    super.onInit();
  }

  // 仅加载数据,不处理 _currentPage , hasMore,collectionList
  Future<Pagination<T>?> loadData({required int pageNum});

  // 刷新数据
  Future<IndicatorResult> onRefresh({bool isSilent = true}) async {
    if (items.isEmpty && !isSilent) {
      change(items, status: RxStatus.loading());
    }
    try {
      final data = await loadData(pageNum: 1);
      if (data != null) {
        items = data.list;
        currentPage = data.pageNum;
        hasMore.value = currentPage < data.totalPage;

        if (data.list.isNotEmpty) {
          change(items, status: RxStatus.success());
        } else {
          change(items, status: RxStatus.empty());
        }
        easyRefreshController.finishRefresh();
        return IndicatorResult.success;
      }
      change(items, status: RxStatus.error());
    } catch (e) {
      userLogger.error('$runtimeType onRefresh error: $e');
      if (items.isEmpty) {
        change(items, status: RxStatus.error(e.toString()));
      } else {
        Loading.toast('加载失败，$e');
      }
    }
    easyRefreshController.finishRefresh(IndicatorResult.fail);
    return IndicatorResult.fail;
  }

  // 加载更多
  Future<IndicatorResult> onLoadMore() async {
    try {
      final data = await loadData(pageNum: currentPage + 1);
      if (data != null) {
        hasMore.value = currentPage < data.totalPage;
        if (data.list.isEmpty) {
          easyRefreshController.finishLoad(IndicatorResult.noMore);
          return IndicatorResult.noMore;
        }
        items.addAll(data.list);
        currentPage = data.pageNum;

        /// 这里必须调用 change 方法，否则不会触发状态变化,加载更多数据不会显示
        change(items, status: RxStatus.success());
        if (hasMore.value) {
          easyRefreshController.finishLoad();
          return IndicatorResult.success;
        }
        easyRefreshController.finishLoad(IndicatorResult.noMore);
        return IndicatorResult.noMore;
      }
    } catch (e) {
      userLogger.error('$runtimeType onLoadMore error: $e');
    }
    easyRefreshController.finishLoad(IndicatorResult.fail);
    return IndicatorResult.fail;
  }
}
