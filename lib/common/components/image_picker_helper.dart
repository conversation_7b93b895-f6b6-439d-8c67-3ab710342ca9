import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:zrreport/common/index.dart';

/// @description: 从相册或照相机里选取图片的工具，带裁剪功能
class ImagePickerHelper {
  BuildContext buildContext;

  ImagePickerHelper(this.buildContext);

  Future<XFile?> pickImage2(ImageSource source) async {
    try {
      final file = await ImagePicker().pickImage(source: source);
      return file;
    } catch (error) {
      defaultLogger.debug("获取图片失败:$error");
    }
  }


  void pickImage(ImageSource source, ImagePickerCallback? callback) {
    ImagePicker().pickImage(source: source).then((image) {
      if (image == null) return;
      if (callback == null) return;
      callback.call(image);
    }).onError((error, stackTrace) {
      defaultLogger.debug("获取图片失败:$error");
    });
  }

  void cropImage(File originalImage,
      {double? aspectRatio, ImageCropCallback? callback}) {
    Future<CroppedFile?> future = ImageCropper().cropImage(
      sourcePath: originalImage.path,
      maxWidth: 10000,
      maxHeight: 10000,
      aspectRatio: aspectRatio != null
          ? CropAspectRatio(ratioX: 1, ratioY: aspectRatio)
          : null,
      uiSettings: [
        AndroidUiSettings(
            toolbarTitle: '',
            toolbarColor: Colors.white,
            statusBarColor: Colors.white,
            toolbarWidgetColor: Colors.green,
            initAspectRatio: CropAspectRatioPreset.square,
            lockAspectRatio: false),
        IOSUiSettings(
          title: '',
        ),
        WebUiSettings(
          context: buildContext,
        ),
      ],
    );
    future.then((value) {
      defaultLogger.debug("_cropImage path:${value == null ? "" : value.path}");
      if (value == null) return;
      if (callback == null) return;
      callback.call(value);
    }).onError((error, stackTrace) {
      defaultLogger.debug("裁剪图片失败:$error");
    });
  }

  void pickWithCropImage(ImageSource source,
      {double? aspectRatio, ImageCropCallback? callback}) {
    pickImage(source, (xFile) {
      cropImage(File(xFile.path), aspectRatio: aspectRatio, callback: callback);
    });
  }
}

typedef ImagePickerCallback = void Function(XFile xFile);
typedef ImageCropCallback = void Function(CroppedFile croppedFile);
