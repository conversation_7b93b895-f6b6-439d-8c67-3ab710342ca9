import 'dart:async';
import 'dart:io';
// ignore: depend_on_referenced_packages
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';

class CustomLogFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    return event.level.value > Level.all.value;
  }
}

class CustomLogger {
  final Logger _logger;
  final String _moduleName;
  final File? _logFile;

  CustomLogger._(this._logger, this._moduleName, this._logFile);

  // 工厂方法：创建不同模块的Logger
  factory CustomLogger.forModule(String moduleName, {bool saveToFile = true}) {
    final logger = Logger(
      filter: CustomLogFilter(),
      printer: _ModulePrinter(moduleName),
      output: MultiOutput([
        // ConsoleOutput(),
        MyConsoleOutput(),
        // if (saveToFile) _FileOutput(moduleName),
      ]),
    );
    return CustomLogger._(logger, moduleName, null);
  }

  // 日志方法（支持分级）
  void debug(String message) => _log(Level.debug, message);
  void info(String message) => _log(Level.info, message);
  void warning(String message) => _log(Level.warning, "⚠️⚠️⚠️ $message");
  void error(String message, [dynamic error]) =>
      _log(Level.error, "‼️‼️‼️ $message", error);

  void _log(Level level, String message, [dynamic error]) {
    _logger.log(level, message, error: error, stackTrace: StackTrace.current);
    _appendToFile(level, message);
  }

  // 异步写入文件（避免阻塞UI线程）
  Future<void> _appendToFile(Level level, String message) async {
    if (_logFile == null) return;
    final entry = '${DateTime.now()} [$level] $_moduleName: $message\n';
    await _logFile!.writeAsString(entry, mode: FileMode.append);
  }
}

class _ModulePrinter extends LogPrinter {
  final String _module;
  final _colors = {
    Level.debug: AnsiColor.fg(12), // 浅蓝
    Level.info: AnsiColor.fg(34), // 绿色
    Level.warning: AnsiColor.fg(214), // 橙色
    Level.error: AnsiColor.fg(196), // 红色
  };

  _ModulePrinter(this._module);

  @override
  List<String> log(LogEvent event) {
    final color = _colors[event.level]!;
    return [
      // color('SXApp-$_module ${event.message}'),
      'SXApp-$_module ${event.message}',
      if (event.error != null) 'Error: ${event.error}',
    ];
  }
}

class MyConsoleOutput extends LogOutput {
  @override
  void output(OutputEvent event) {
    for (var line in event.lines) {
      print(line);
    }
  }
}

class _FileOutput extends LogOutput {
  final String _module;
  File? _file;

  _FileOutput(this._module);

  @override
  Future<void> init() async {
    final dir = await getApplicationDocumentsDirectory();
    var path =
        '${dir.path}/logs/${_module}_${DateTime.now().toIso8601String()}.log';
    debugPrint('_FileOutput path: $path');
    _file = File(path);
    if (await _file!.exists()) {
      await _file!.create(recursive: true);
    }
  }

  @override
  void output(OutputEvent event) {
    event.lines.forEach(
        (line) => _file!.writeAsString('$line\n', mode: FileMode.append));
  }
}

final defaultLogger = CustomLogger.forModule('default');
final networkLogger = CustomLogger.forModule('network');
final userLogger = CustomLogger.forModule('user');
final chatLogger = CustomLogger.forModule('chat');
final webLogger = CustomLogger.forModule('web');
final liveLogger = CustomLogger.forModule('live_stream');
final videoLogger = CustomLogger.forModule('video');
// final imageLogger = CustomLogger.forModule('image');
