import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class InputTextPage extends StatefulWidget {
  final String title;
  final String hintText;
  int? maxLength;
  final String? text;

  InputTextPage({
    Key? key,
    required this.title,
    required this.hintText,
    this.maxLength,
    this.text,
  }) : super(key: key);

  @override
  _InputTextPageState createState() => _InputTextPageState();
}

class _InputTextPageState extends State<InputTextPage> {
  late TextEditingController _controller;
  int _currentLength = 0;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _controller.addListener(_updateLength);
    _controller.text = widget.text ?? '';
  }

  void _updateLength() {
    setState(() {
      _currentLength = _controller.text.length;
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleSubmit() {
    if (widget.maxLength != null) {
      if (_currentLength <= widget.maxLength!) {
        Navigator.pop(context, _controller.text);
      }
    } else {
      Navigator.pop(context, _controller.text);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextField(
              controller: _controller,
              maxLines: 15,
              minLines: 10,
              maxLength: widget.maxLength,
              maxLengthEnforcement: MaxLengthEnforcement.enforced,
              decoration: InputDecoration(
                hintText: widget.hintText,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.all(12),
              ),
            ).border(
                bottom: 0.5,
                top: 0.5,
                color: Get.context?.colors.divider ?? Colors.transparent),
            const SizedBox(height: 8),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed:
                    widget.maxLength != null
                    ? (_currentLength > widget.maxLength!
                        ? null
                        : _handleSubmit)
                    : _handleSubmit,
                child: const Padding(
                  padding: EdgeInsets.symmetric(vertical: 16),
                  child: Text(
                    '确定',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 17,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// // 使用示例：带返回值处理
// Navigator.push(
//   context,
//   MaterialPageRoute(
//     builder: (context) => ProfileEditPage(
//       title: '简介',
//       hintText: '介绍一下自己',
//       maxLength: 200,
//     ),
//   ),
// ).then((value) {
//   if (value != null) {
//     final result = value as String;
//     print('用户输入的简介内容: $result');
//     // 在这里处理返回的文本内容
//   }
// });
