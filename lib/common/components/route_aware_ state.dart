// ignore: file_names
import 'package:flutter/material.dart';

RouteObserver<PageRoute> routeObserver = RouteObserver<PageRoute>();

abstract class RouteAwareState<T extends StatefulWidget> extends State<T>
    with RouteAware {
  @override
  void didChangeDependencies() {
    print("[route_aware] didChangeDependencies $widget");
    routeObserver.subscribe(
        this, ModalRoute.of(context) as PageRoute); //Subscribe it here
    super.didChangeDependencies();
  }

  @override
  void didPush() {
    print('[route_aware]didPush $widget');
  }

  @override
  void didPopNext() {
    print('[route_aware]didPopNext $widget');
  }

  @override
  void didPop() {
    print('[route_aware]didPop $widget');
  }

  @override
  void didPushNext() {
    print('[route_aware]didPushNext $widget');
  }

  @override
  void dispose() {
    print("[route_aware]dispose $widget");
    routeObserver.unsubscribe(this);
    super.dispose();
  }
}
