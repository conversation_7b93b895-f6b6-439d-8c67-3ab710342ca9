import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

import 'controller.dart';

// ignore: must_be_immutable
class WebViewContainer extends StatefulWidget {
  WebViewContainer(
      {super.key,
      this.initialUrl,
      this.htmlContent,
      this.onWebControllerCreate});

  String? initialUrl;

  String? htmlContent;

  void Function(MyWebViewController)? onWebControllerCreate;

  @override
  State<WebViewContainer> createState() => _WebViewContainerState();
}

class _WebViewContainerState extends State<WebViewContainer>
    with AutomaticKeepAliveClientMixin {
  MyWebViewController controller = MyWebViewController();

  @override
  void initState() {
    super.initState();
    controller.initState();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    super.dispose();
    controller.dispose();
  }

  // 主视图
  Widget _buildView() {
    return InAppWebView(
      initialSettings: InAppWebViewSettings(
        useHybridComposition: true,
        transparentBackground: true
      ),

      onWebViewCreated: (webViewController) {
        controller.handleWebViewCreated(webViewController);
        widget.onWebControllerCreate?.call(controller);
      },
      initialUrlRequest: widget.initialUrl != null
          ? URLRequest(url: WebUri(widget.initialUrl!))
          : null,
      initialData: widget.htmlContent != null
          ? InAppWebViewInitialData(data: widget.htmlContent!)
          : null,
      onLoadStart: controller.handleLoadStart,
      onLoadStop: controller.handleLoadStop,
      onProgressChanged: controller.handleProgressChanged,
      onConsoleMessage: controller.handleConsoleMessage,
      onCreateWindow: controller.handleCreateWindow,
    );
  }

  @override
  Widget build(BuildContext context) {
    return _buildView();
    // return Scaffold(
    //   backgroundColor: Colors.white,
    //   body: _buildView(),
    // );
  }
}
