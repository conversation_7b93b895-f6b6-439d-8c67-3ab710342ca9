import 'package:get/get.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:zrreport/common/index.dart';

class MyWebViewController {
  MyWebViewController();

  InAppWebViewController? webViewController;

  // 加载状态
  final loading = false.obs;

  // 加载进度
  final progress = 0.0.obs;

  Worker? worker;

  initState() {
    worker = ever(UserService.to.isLoginRx, (value) async {
      if (value) {
        await _saveTokenForWeb();
      } else {
        try {
          await webViewController?.evaluateJavascript(
              source: 'deleteTokenFormFlutter()');
          webLogger.debug("flutter call deleteTokenFormFlutter success");
        } catch (e) {
          webLogger.error("flutter call deleteTokenFormFlutter failed， $e");
        }
      }
    });
  }

  Future<void> _saveTokenForWeb() async {
    var source = 'saveTokenFromFlutter({"token":"${UserService.to.token}"})';

    try {
      await webViewController?.evaluateJavascript(source: source);
      webLogger
          .debug("flutter call saveTokenFromFlutter success, source:$source");
    } catch (e) {
      webLogger.error("flutter call saveTokenFromFlutter failed， $e");
    }
  }

  dispose() {
    worker?.dispose();
    webViewController?.dispose();
  }

  Future<bool> canGoBack() async {
    return await webViewController?.canGoBack() ?? false;
  }

  Future<void> goBack() async {
    if (await webViewController?.canGoBack() ?? false) {
      await webViewController?.goBack();
    } else {
      Get.back();
    }
  }

  void handleWebViewCreated(InAppWebViewController webViewController) {
    this.webViewController = webViewController;

    webViewController.addJavaScriptHandler(
        handlerName: 'getToken',
        callback: (args) {
          var result = {"token": UserService.to.token};
          webLogger.info('js call getToken result: $result');
          return result;
        });

    webViewController.addJavaScriptHandler(
        handlerName: 'closePage',
        callback: (args) {
          webLogger.info('js call closePage');
          goBack();
        });

    webViewController.addJavaScriptHandler(
        handlerName: 'openNativePage',
        callback: (args) {
          webLogger.info('js call openNativePage args:$args');
          if (args.isEmpty || args[0] is! Map) {
            return false;
          }
          return false;
        });

    webViewController.addJavaScriptHandler(
        handlerName: 'callNativeLogin',
        callback: (args) async {
          webLogger.info('js call callNativeLogin');
          if (UserService.to.isLogin && UserService.to.token.isNotEmpty) {
            var result = {"token": UserService.to.token};
            webLogger.info('js call callNativeLogin result: $result');
            return result;
          } else {
            await Get.toNamed(RouteNames.systemLogin);
            var result = {"token": UserService.to.token};
            webLogger.info('js call callNativeLogin result: $result');
            return result;
          }
        });

    _saveTokenForWeb();
  }

  void handleLoadStart(InAppWebViewController webViewController, Uri? url) {
    loading.value = true;
  }

  Future<void> handleLoadStop(
      InAppWebViewController webViewController, Uri? url) async {
    loading.value = false;
  }

  void handleProgressChanged(
      InAppWebViewController webViewController, int progress) {
    this.progress.value = progress / 100;
  }

  void handleConsoleMessage(
      InAppWebViewController webViewController, ConsoleMessage consoleMessage) {
    // webLogger.debug("console message: ${consoleMessage.message}");
  }

  Future<bool> handleCreateWindow(InAppWebViewController webViewController,
      CreateWindowAction createWindowAction) async {
    return false;
  }
}
