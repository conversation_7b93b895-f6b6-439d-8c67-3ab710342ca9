import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';
import 'package:visibility_detector/visibility_detector.dart';

mixin VisibleMixin {
  var _isVisible = false;
  bool get isVisible => _isVisible;
  set isVisible(bool value) {
    if (!_isVisible && value) {
      didShowCount += 1;

      defaultLogger.debug("$this fromInVisibleToVisible count: $didShowCount");
      if (didShowCount == 1) {
        return;
      }

      fromInVisibleToVisible();
    }
    _isVisible = value;
  }

  fromInVisibleToVisible() {}

  void onVisibilityChanged(VisibilityInfo info) {
    isVisible = info.visibleFraction > 0;
  }

  int didShowCount = 0;
}

extension VisibleExtension on Widget {
  Widget visibilityContent(
      {required Key key, required VisibilityChangedCallback callback}) {
    return VisibilityDetector(
      key: key,
      onVisibilityChanged: callback,
      child: this,
    );
  }
}
