import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:zrreport/common/index.dart';

import 'model/amap_tipps.dart';

/// 高德地图API服务类
class AmapService {
  static const String _baseUrl = 'https://restapi.amap.com/v3';
  static const String _apiKey = '329b1e97a844a0cf2be6e3046a5632cc';

  /// 单例模式
  static final AmapService _instance = AmapService._internal();
  factory AmapService() => _instance;
  AmapService._internal();

  /// 通用GET请求方法
  Future<Map<String, dynamic>> _get(
      String endpoint, Map<String, dynamic> params) async {
    try {
      // 添加API密钥到参数中
      params['key'] = _apiKey;

      // 构建查询字符串
      final queryString = params.entries
          .map((e) =>
              '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value.toString())}')
          .join('&');

      final url = '$_baseUrl$endpoint?$queryString';
      defaultLogger.debug('AmapService请求URL: $url');

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        // defaultLogger.debug('AmapService响应: $jsonData');
        return jsonData;
      } else {
        defaultLogger.error('AmapService请求失败，状态码: ${response.statusCode}');
        throw Exception('HTTP请求失败: ${response.statusCode}');
      }
    } catch (e) {
      defaultLogger.error('AmapService请求异常: $e');
      rethrow;
    }
  }

  /// 逆地理编码 - 根据经纬度获取地址信息
  Future<AmapLocationResponse> regeocode({
    required double longitude,
    required double latitude,
  }) async {
    final params = {
      'location': '$longitude,$latitude',
    };
    final jsonData = await _get('/geocode/regeo', params);
    return AmapLocationResponse.fromJson(jsonData);
  }

  /// 输入提示
  Future<AmapTipsResponse> inputtips({
    required String city,
    required String keywords,
  }) async {
    final params = {
      // city=610100&children=1&offset=1&page=1&extensions=all&keywords=k
      'city': city,
      'keywords': keywords,
    };
    final jsonData = await _get('/assistant/inputtips', params);
    return AmapTipsResponse.fromJson(jsonData);
  }

  // /// 地理编码 - 根据地址获取经纬度
  // Future<Map<String, dynamic>> geocode({
  //   required String address,
  //   String? city,
  //   String? batch,
  //   String? output,
  // }) async {
  //   final params = {
  //     'address': address,
  //   };

  //   if (city != null) params['city'] = city;
  //   if (batch != null) params['batch'] = batch;
  //   if (output != null) params['output'] = output;

  //   return await _get('/geocode/geo', params);
  // }

  // /// 搜索POI（兴趣点）
  // Future<Map<String, dynamic>> searchPoi({
  //   required String keywords,
  //   String? types,
  //   String? city,
  //   String? citylimit,
  //   String? children,
  //   String? offset,
  //   String? page,
  //   String? extensions,
  // }) async {
  //   final params = {
  //     'keywords': keywords,
  //   };

  //   if (types != null) params['types'] = types;
  //   if (city != null) params['city'] = city;
  //   if (citylimit != null) params['citylimit'] = citylimit;
  //   if (children != null) params['children'] = children;
  //   if (offset != null) params['offset'] = offset;
  //   if (page != null) params['page'] = page;
  //   if (extensions != null) params['extensions'] = extensions;

  //   return await _get('/place/text', params);
  // }

  // /// 周边搜索POI
  // Future<Map<String, dynamic>> searchAround({
  //   required double longitude,
  //   required double latitude,
  //   String? keywords,
  //   String? types,
  //   String? radius,
  //   String? sortrule,
  //   String? offset,
  //   String? page,
  //   String? extensions,
  // }) async {
  //   final params = {
  //     'location': '$longitude,$latitude',
  //   };

  //   if (keywords != null) params['keywords'] = keywords;
  //   if (types != null) params['types'] = types;
  //   if (radius != null) params['radius'] = radius;
  //   if (sortrule != null) params['sortrule'] = sortrule;
  //   if (offset != null) params['offset'] = offset;
  //   if (page != null) params['page'] = page;
  //   if (extensions != null) params['extensions'] = extensions;

  //   return await _get('/place/around', params);
  // }

  // /// 获取IP定位
  // Future<Map<String, dynamic>> ipLocation({
  //   required String ip,
  // }) async {
  //   final params = {
  //     'ip': ip,
  //   };

  //   return await _get('/ip', params);
  // }

  // /// 获取天气信息
  // Future<Map<String, dynamic>> getWeather({
  //   required String city,
  //   String? extensions,
  //   String? output,
  // }) async {
  //   final params = {
  //     'city': city,
  //   };

  //   if (extensions != null) params['extensions'] = extensions;
  //   if (output != null) params['output'] = output;

  //   return await _get('/weather/weatherInfo', params);
  // }

  // /// 计算两点间距离
  // Future<Map<String, dynamic>> calculateDistance({
  //   required String origins,
  //   required String destination,
  //   String? type,
  //   String? strategy,
  //   String? avoidpolygons,
  //   String? avoidroad,
  // }) async {
  //   final params = {
  //     'origins': origins,
  //     'destination': destination,
  //   };

  //   if (type != null) params['type'] = type;
  //   if (strategy != null) params['strategy'] = strategy;
  //   if (avoidpolygons != null) params['avoidpolygons'] = avoidpolygons;
  //   if (avoidroad != null) params['avoidroad'] = avoidroad;

  //   return await _get('/distance', params);
  // }

  // /// 路径规划
  // Future<Map<String, dynamic>> routePlanning({
  //   required String origin,
  //   required String destination,
  //   String? strategy,
  //   String? waypoints,
  //   String? avoidpolygons,
  //   String? avoidroad,
  //   String? output,
  // }) async {
  //   final params = {
  //     'origin': origin,
  //     'destination': destination,
  //   };

  //   if (strategy != null) params['strategy'] = strategy;
  //   if (waypoints != null) params['waypoints'] = waypoints;
  //   if (avoidpolygons != null) params['avoidpolygons'] = avoidpolygons;
  //   if (avoidroad != null) params['avoidroad'] = avoidroad;
  //   if (output != null) params['output'] = output;

  //   return await _get('/direction/driving', params);
  // }
}
