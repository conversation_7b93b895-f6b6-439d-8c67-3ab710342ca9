/// 高德地图地理编码返回数据模型
class AmapLocationResponse {
  final String status;
  final Regeocode regeocode;
  final String info;
  final String infocode;

  AmapLocationResponse({
    required this.status,
    required this.regeocode,
    required this.info,
    required this.infocode,
  });

  factory AmapLocationResponse.fromJson(Map<String, dynamic> json) {
    return AmapLocationResponse(
      status: json['status'] as String,
      regeocode: Regeocode.fromJson(json['regeocode'] as Map<String, dynamic>),
      info: json['info'] as String,
      infocode: json['infocode'] as String,
    );
  }
}

class Regeocode {
  final AddressComponent addressComponent;
  final String formattedAddress;

  Regeocode({
    required this.addressComponent,
    required this.formattedAddress,
  });

  factory Regeocode.fromJson(Map<String, dynamic> json) {
    return Regeocode(
      addressComponent: AddressComponent.fromJson(
          json['addressComponent'] as Map<String, dynamic>),
      formattedAddress: json['formatted_address'] as String,
    );
  }


  /// 
  /// 从地址中移除省市街道
  /// 陕西省西安市未央区汉城街道凤城十二路85号西安北站行政中心美居酒店  =》凤城十二路85号西安北站行政中心美居酒店
  String get address {
    // 使用正则表达式匹配地址开头的省市区街道
    final pattern = RegExp(
        '^${addressComponent.province}${addressComponent.city}${addressComponent.district}${addressComponent.township}');
    return formattedAddress.replaceFirst(pattern, '').trim();
  }
}

class AddressComponent {
  final String province;
  final String city;
  final String district;
  final String township;
  // final String streetNumber;
  final String adcode;
  final String citycode;

  AddressComponent({
    required this.province,
    required this.city,
    required this.district,
    required this.township,
    // required this.streetNumber,
    required this.adcode,
    required this.citycode,
  });

  factory AddressComponent.fromJson(Map<String, dynamic> json) {
    return AddressComponent(
      province: json['province'] is List ? '' : (json['province'] as String),
      city: json['city'] is List ? '' : (json['city'] as String),
      district: json['district'] is List ? '' : (json['district'] as String),
      township: json['township'] is List ? '' : (json['township'] as String),
      // streetNumber: json['streetNumber'] as String,
      adcode: json['adcode'] is List ? '' : (json['adcode'] as String),
      citycode: json['citycode'] is List ? '' : (json['citycode'] as String),
    );
  }

}
