import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';

/// 高德地图输入提示返回数据模型
class AmapTipsResponse {
  final String status;
  final String info;
  final String infocode;
  final List<AmapTip> tips;

  AmapTipsResponse({
    required this.status,
    required this.info,
    required this.infocode,
    required this.tips,
  });

  factory AmapTipsResponse.fromJson(Map<String, dynamic> json) {
    return AmapTipsResponse(
      status: json['status'] as String,
      info: json['info'] as String,
      infocode: json['infocode'] as String,
      tips: (json['tips'] as List<dynamic>?)
              ?.map((e) => AmapTip.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }
}

/// 输入提示项
class AmapTip {
  final String id;
  final String name;
  final String district;
  final String adcode;
  final String location;
  final String address;
  final String typecode;

  AmapTip({
    required this.id,
    required this.name,
    required this.district,
    required this.adcode,
    required this.location,
    required this.address,
    required this.typecode,
  });

  factory AmapTip.fromJson(Map<String, dynamic> json) {
    debugPrint('fromJson name:${json["name"]}');
    return AmapTip(
      id: json['id'] is List ? '' : json['id'] as String? ?? '',
      name: json['name'] is List ? '' : json['name'] as String? ?? '',
      district:
          json['district'] is List ? '' : json['district'] as String? ?? '',
      adcode: json['adcode'] is List ? '' : json['adcode'] as String? ?? '',
      location:
          json['location'] is List ? '' : json['location'] as String? ?? '',
      address: json['address'] is List ? '' : json['address'] as String? ?? '',
      typecode:
          json['typecode'] is List ? '' : json['typecode'] as String? ?? '',
    );
  }

  Location2D? get location2D {
    final list = location.split(',');
    if (list.length != 2) return null;

    double? longitude = double.tryParse(list[0]);
    double? latitude = double.tryParse(list[1]);

    if (longitude == null || latitude == null) {
      return null;
    }

    return Location2D(longitude: longitude, latitude: latitude);
  }
}
