// ignore_for_file: pattern_never_matches_value_type

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/history/list/provider.dart';
import 'package:zrreport/pages/history/main/provider.dart';

import 'widgets/report_history_item.dart';

class HistoryListView extends ConsumerWidget {
  final QueryStatus status;
  final QueryType queryType;

  const HistoryListView({super.key, required this.status, required this.queryType});

  getProvider() {
    switch (queryType) {
      case QueryType.all:
        switch (status) {
          /// 所有分类
          case QueryStatus.all:
            return allReportListProvider;
          case QueryStatus.processing:
            return allProcessingReportListProvider;
          case QueryStatus.done:
            return allDoneReportListProvider;
          case QueryStatus.failed:
            return allFailedReportListProvider;
        }

      case QueryType.self:
        switch (status) {
          /// 所有分类
          case QueryStatus.all:
            return selfReportListProvider;
          case QueryStatus.processing:
            return selfProcessingReportListProvider;
          case QueryStatus.done:
            return selfDoneReportListProvider;
          case QueryStatus.failed:
            return selfFailedReportListProvider;
        }

      case QueryType.other:
        switch (status) {
          /// 所有分类
          case QueryStatus.all:
            return otherReportListProvider;
          case QueryStatus.processing:
            return otherProcessingReportListProvider;
          case QueryStatus.done:
            return otherDoneReportListProvider;
          case QueryStatus.failed:
            return otherFailedReportListProvider;
        }
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var provider = getProvider();
    
    // 监听搜索内容变化
    ref.listen(searchProvider, (prev, next) {
      debugPrint('search content changed ${next.content}');
      ref.read(provider.notifier).onSearch();
    });
    
    // 监听当前 Provider 的刷新触发器
    ref.listen(providerRefreshTriggerProvider, (prev, next) {
      final prevValue = prev?[provider] ?? 0;
      final nextValue = next[provider] ?? 0;
      if (prevValue != nextValue) {
        debugPrint('收到 Provider 刷新信号: ${provider.runtimeType}');
        // 触发下拉刷新动画
        final refreshController = ref.read(provider.notifier).refreshController;
        refreshController.callRefresh();
      }
    });
    
    // 监听全局刷新触发器
    ref.listen(globalRefreshTriggerProvider, (prev, next) {
      if (prev != next) {
        debugPrint('收到全局刷新信号: ${provider.runtimeType}');
        // 触发下拉刷新动画
        final refreshController = ref.read(provider.notifier).refreshController;
        refreshController.callRefresh();
      }
    });
    
    final state = ref.watch(provider);
    return state.when(
      empty: () => EmptyWidget(text: '暂无数据'),
      ready: (data) => _buildContent(context, ref, data),
      error: (String error) {
        return ErrorStatusWidget(
            text: error, onAttempt: () => ref.refresh(getProvider()));
      },
      loading: () => LoadingWidget(),
    );
  }

  Widget _buildContent(
      BuildContext ctx, WidgetRef ref, List<QueryReport> data) {
    final provider = getProvider();
    return Center(
      child: EasyRefresh(
        controller: ref.watch(provider.notifier).refreshController,
        child: ListView.builder(
          itemCount: data.length,
          itemBuilder: ((context, index) {
            return Padding(
              padding: EdgeInsets.only(top: index > 0 ? 24 : 0),
              child: ReportHistoryItem(
                history: data[index],
                queryType: queryType,
                status: status,
                sourceProvider: provider, // 传递 Provider 引用
              ),
            );
          }),
        ),
        onRefresh: () => ref.watch(provider.notifier).firstLoadPage(),
        onLoad: () => ref.watch(provider.notifier).loadMore(),
      ),
    );
  }
}
