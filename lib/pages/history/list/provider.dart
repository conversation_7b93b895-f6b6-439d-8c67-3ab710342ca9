import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';

import '../main/provider.dart';

/// 全局刷新触发器
final globalRefreshTriggerProvider = StateProvider<int>((ref) => 0);

/// Provider 刷新触发器映射
/// key: Provider 实例，value: 刷新计数器
final providerRefreshTriggerProvider = StateProvider<Map<Object, int>>((ref) => {});

/// 工具函数：触发特定 Provider 刷新
void refreshProvider(WidgetRef ref, Object provider) {
  final currentMap = ref.read(providerRefreshTriggerProvider);
  final currentValue = currentMap[provider] ?? 0;
  
  ref.read(providerRefreshTriggerProvider.notifier).state = {
    ...currentMap,
    provider: currentValue + 1,
  };
  
  debugPrint('触发 Provider 刷新: ${provider.runtimeType}');
}

/// 工具函数：触发全局刷新
void refreshAllProviders(WidgetRef ref) {
  final current = ref.read(globalRefreshTriggerProvider);
  ref.read(globalRefreshTriggerProvider.notifier).state = current + 1;
  debugPrint('触发全局刷新');
}

// AutoDisposeStateNotifierProvider<PaginationViewStateNotifier<QueryReport>,
//         PaginationViewState<List<QueryReport>>>
createListReportProvider(QueryType type, QueryStatus status) {
  return StateNotifierProvider.autoDispose<
      PaginationViewStateNotifier<QueryReport>,
      PaginationViewState<List<QueryReport>>>((ref) {
    return PaginationViewStateNotifier<QueryReport>(fetchItems:
        (int pageIndex, int pageSize, {CancelToken? cancelToken}) async {
      final searchContent = ref.read(searchProvider.notifier).state.content;
      final result = await QueryApi.listReport(
          cancelToken: cancelToken,
          entity: ListReportEntity(pageIndex, pageSize,
              likeName: searchContent,
              queryStatus: status.status,
              queryType: type.type));
      return result.data;
    });
  });
}

/// 所有的历史报告
final allReportListProvider =
    createListReportProvider(QueryType.all, QueryStatus.all);

/// 所有正在查询中的报告
final allProcessingReportListProvider =
    createListReportProvider(QueryType.all, QueryStatus.processing);

/// 所有已完成的报告
final allDoneReportListProvider =
    createListReportProvider(QueryType.all, QueryStatus.done);

/// 所有失败的报告
final allFailedReportListProvider =
    createListReportProvider(QueryType.all, QueryStatus.failed);

/// 自己的历史报告
final selfReportListProvider =
    createListReportProvider(QueryType.self, QueryStatus.all);

/// 自己正在查询中的报告
final selfProcessingReportListProvider =
    createListReportProvider(QueryType.self, QueryStatus.processing);

/// 自己已完成的报告
final selfDoneReportListProvider =
    createListReportProvider(QueryType.self, QueryStatus.done);

/// 自己失败的报告
final selfFailedReportListProvider =
    createListReportProvider(QueryType.self, QueryStatus.failed);

/// 他人的历史报告
final otherReportListProvider =
    createListReportProvider(QueryType.other, QueryStatus.all);

/// 他人正在查询中的报告
final otherProcessingReportListProvider =
    createListReportProvider(QueryType.other, QueryStatus.processing);

/// 他人已完成的报告
final otherDoneReportListProvider =
    createListReportProvider(QueryType.other, QueryStatus.done);

/// 他人失败的报告
final otherFailedReportListProvider =
    createListReportProvider(QueryType.other, QueryStatus.failed);
