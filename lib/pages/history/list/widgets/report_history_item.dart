import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:zrreport/common/index.dart';

class ReportHistoryItem extends ConsumerWidget {
  final QueryReport history;
  final void Function()? onTapDetail;
  final void Function()? onTapRetry;
  final QueryType queryType;
  final QueryStatus status;
  final Object? sourceProvider;

  const ReportHistoryItem({
    super.key,
    required this.history,
    this.onTapDetail,
    this.onTapRetry,
    required this.queryType,
    required this.status,
    this.sourceProvider,
  });

  DateTime? get dateTime {
    final createTime = history.createTime;
    DateTime? dt;
    if (createTime != null && createTime.isNotEmpty) {
      dt = DateTime.tryParse(createTime.replaceAll('/', '-'));
    }
    return dt;
  }

  void _navigateToDetail() {
    Get.toNamed(RouteNames.reportDetail, arguments: {
      'shareCode': history.shareCode ?? '',
      'sourceProvider': sourceProvider,
    });
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    String dateLabel =
        dateTime != null ? DateFormat('M月d日 HH:mm').format(dateTime!) : '';

        return Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (dateLabel.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Text(
                  dateLabel,
                  style:
                      const TextStyle(color: Color(0xFF222222), fontSize: 12),
                ),
              ),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.all(
                  Radius.circular(12),
                ),
              ),
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
              child: Stack(
                children: [
                  if (this.history.channel == 'scancode')
                    Positioned(
                        top: 10,
                        right: 10,
                        child: SvgPicture.asset(AssetsSvgs.queryByOthersSvg,
                            width: 65, height: 65)),
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16.0, vertical: 19),
                child: _content(ref),
                  )
                ],
              ),
            ),
          ],
    );
  }

  _content(WidgetRef ref) {
    if (history.queryStatus == QueryStatus.processing.status) {
      return _buildProcessingContent();
    } else if (history.queryStatus == QueryStatus.done.status) {
      return _buildDoneContent();
    } else if (history.queryStatus == QueryStatus.failed.status) {
      return _buildFailedContent();
    }
  }

  Widget _buildDoneContent() {
    const textStyle =
        TextStyle(fontSize: 14, color: Color(0xFF555555), height: 20.0 / 14);
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _name(),
            if ((history.legalPerson ?? "").isNotEmpty) ...[
              const SizedBox(height: 12),
              Text('法人姓名：${history.legalPerson!}', style: textStyle)
            ],
            if ((history.registerAddress ?? '').isNotEmpty) ...[
              const SizedBox(height: 12),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('注册地址：', style: textStyle),
                  Expanded(
                      child: Text('${history.registerAddress}',
                          style: textStyle, maxLines: 2)),
                ],
              )
            ],
            const SizedBox(height: 10),
            Divider(),
            const SizedBox(height: 10),
            Row(
              children: [
                Text('已完成', style: textStyle),
                Spacer(),
            Consumer(
              builder: (context, ref, child) {
                return GestureDetector(
                  onTap: () => _navigateToDetail(),
                  child: const Text(
                    '查看详情 》',
                    style: TextStyle(color: AppColors.primary),
                  ),
                );
              },
                )
              ],
            ),
          ],
    );
  }

  Column _buildFailedContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _name(),
            SizedBox(height: 6),
            Row(
              children: [
                Text(
                  '未知状态',
                  style: TextStyle(
                      color: Color(0xFFF0822F),
                      fontWeight: FontWeight.w600,
                      fontSize: 15),
                ),
                SizedBox(width: 8),
                RichText(
                    maxLines: 2,
                    text: TextSpan(
                      style: const TextStyle(color: Colors.grey),
                      children: [
                        const TextSpan(text: '具体原因请'),
                        TextSpan(
                            text: '联系客服',
                            style: TextStyle(
                                color: AppColors.primary, fontSize: 14),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () => gotoWeb(UrlPath.userAgreement,
                                  title: '用户协议')),
                      ],
                    )),
                Spacer(),
                Consumer(
                  builder: (context, ref, child) {
                    return GestureDetector(
                      child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 5),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(14),
                  ),
                  child: const Text(
                    '重新查询',
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 13,
                        fontWeight: FontWeight.w600),
                  ),
                      ),
                    );
                  },
                )
              ],
            ),
          ],
        ),
        const SizedBox(height: 10),
        Divider(),
        const SizedBox(height: 10),
        Text(
          '温馨提示：我们会持续请求数据，请30分钟后再查看',
          style: TextStyle(color: Colors.grey, fontSize: 13),
        )
      ],
    );
  }

  _buildProcessingContent() {
    return Row(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _name(),
            Text(
              DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime!),
              style: const TextStyle(color: Colors.grey, fontSize: 14),
            ),
          ],
        ),
        Spacer(),
        Text('查询中',
            style: TextStyle(
                color: Color(0xFF488AFD), fontWeight: FontWeight.bold)),
      ],
    );
  }

  _name() {
    return Text(
      history.taxpayerName,
      style: const TextStyle(
          color: Color(0xFF222222), fontWeight: FontWeight.bold, fontSize: 15),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }
}
