import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';

final searchProvider =
    StateNotifierProvider.autoDispose<SearchStateNotifier, SearchState>(
  (ref) => SearchStateNotifier(),
);

class SearchState {
  final String content;

  SearchState({
    required this.content,
  });

  SearchState copyWith({String? contentParam, Timer? debounceParam}) {
    return SearchState(
      content: contentParam ?? '',
    );
  }
}

class SearchStateNotifier extends StateNotifier<SearchState> {
  Timer? _debounce;

  SearchStateNotifier()
      : super(SearchState(
          content: '',
        ));

  updateContent(String newContent) {
    // 取消之前的计时器
    _debounce?.cancel();

    // 设置新的计时器
    _debounce = Timer(const Duration(milliseconds: 500), () {
      state = state.copyWith(contentParam: newContent);
    });
  }

  @override
  void dispose() {
    super.dispose();
    _debounce?.cancel();
  }
}
