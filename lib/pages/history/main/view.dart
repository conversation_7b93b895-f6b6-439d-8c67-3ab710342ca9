import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/history/index.dart';
import 'package:zrreport/pages/history/main/provider.dart';

class HistoryMain extends StatefulWidget {
  HistoryMain({Key? key}) : super(key: key);

  @override
  State<HistoryMain> createState() => _HistoryMainState();
}

class _HistoryMainState extends State<HistoryMain>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Consumer(builder: (context, ref, _) {
        return Scaffold(
          backgroundColor: Color(0xFFFAF8F9),
          appBar: AppBar(
            backgroundColor: AppColors.primary,
            title: Text('历史报告', style: TextStyle(color: Colors.white)),
            bottom: PreferredSize(
              preferredSize: Size.fromHeight(120),
              child: Column(
                children: [
                  _tabbar(),
                  <PERSON><PERSON><PERSON><PERSON>(height: 11.5),
                  _searchBar(ref),
                  Sized<PERSON><PERSON>(height: 15)
                ],
              ),
            ),
          ),
          body: GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
            child: TabBarView(
              children: [
                HistoryCategoryView(type: QueryType.all),
                HistoryCategoryView(type: QueryType.self),
                HistoryCategoryView(type: QueryType.other),
              ],
            ),
          ),
        );
      }
      ),
    );
  }

  _searchBar(WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 17.0),
      child: TextField(
        onChanged: (value) {
          ref.read(searchProvider.notifier).updateContent(value);
        },
        textInputAction: TextInputAction.search,
        style: TextStyle(color: Color(0xFF222222), fontSize: 14),
        decoration: InputDecoration(
          isDense: true,
          hintText: '请输入关键字搜索',
          hintStyle: TextStyle(
              fontSize: 14, color: AppColors.textColor9, height: 20 / 14),
          hintMaxLines: 1,
          fillColor: Colors.white,
          filled: true,
          prefixIcon: Icon(
            Icons.search_sharp,
            color: AppColors.textColor9,
            size: 24,
          ),
          contentPadding:
              EdgeInsetsDirectional.symmetric(horizontal: 24, vertical: 10),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(20),
            borderSide: BorderSide(
              color: Color(0xFFDDDDDD), // 边框颜色
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(20),
            borderSide: BorderSide(
              color: Color(0xFFDDDDDD), // 边框颜色
            ),
          ),
        ),
      ),
    );
  }

  _tabbar() {
    var selectedTextColor = Colors.white;
    var unselectedTextColor = Colors.white;

    return TabBar(
      indicator: MyCustomIndicator(color: selectedTextColor),
      labelColor: selectedTextColor,
      overlayColor: WidgetStateProperty.resolveWith<Color?>(
        (Set<WidgetState> states) {
          return Colors.transparent;
        },
      ),
      unselectedLabelColor: unselectedTextColor,
      unselectedLabelStyle: TextStyle(
        fontSize: 15,
        fontWeight: FontWeight.w600,
      ),
      labelStyle: TextStyle(
        fontSize: 17,
        fontWeight: FontWeight.w600,
      ),
      
      tabs: [
        Tab(text: '全部报告'),
        Tab(text: '我的历史'),
        Tab(text: '他人查询'),
      ],
    );
  }
}
