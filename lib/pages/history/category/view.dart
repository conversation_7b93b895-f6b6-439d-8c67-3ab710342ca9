import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/history/list/view.dart';

class HistoryCategoryView extends StatelessWidget {
  HistoryCategoryView({super.key, required this.type});

  QueryType type;

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 4,
      child: Column(
        children: [
          _tabbar(),
          <PERSON><PERSON><PERSON><PERSON>(height: 16),
          Expanded(
            child: Tab<PERSON>ar<PERSON>iew(
              children: [
                HistoryListView(status: QueryStatus.all, queryType: type),
                HistoryListView(
                    status: QueryStatus.processing, queryType: type),
                HistoryListView(status: QueryStatus.done, queryType: type),
                HistoryListView(status: QueryStatus.failed, queryType: type),
              ],
            ),
          ),
        ],
      ),
    );
  }

  _tabbar() {
    var textColor = Color(0xFF111111);

    return TabBar(
      indicator: MyCustomIndicator(color: AppColors.primary, indHeight: 4),
      labelColor: textColor,
      unselectedLabelColor: AppColors.textColor9,
      unselectedLabelStyle: TextStyle(
        fontSize: 15,
        fontWeight: FontWeight.w600,
      ),
      labelStyle: TextStyle(
        fontSize: 17,
        fontWeight: FontWeight.w600,
      ),
      tabs: [
        Tab(text: '全部'),
        Tab(text: '查询中'),
        Tab(text: '已完成'),
        Tab(text: '查询失败'),
      ],
    );
  }
}
