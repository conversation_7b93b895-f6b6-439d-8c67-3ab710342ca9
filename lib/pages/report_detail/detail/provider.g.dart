// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$getReportDetailHash() => r'79cb89edb5a24c25891f21c90375632199d30489';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [getReportDetail].
@ProviderFor(getReportDetail)
const getReportDetailProvider = GetReportDetailFamily();

/// See also [getReportDetail].
class GetReportDetailFamily
    extends Family<AsyncValue<BaseResponse<ReportDetailModel>>> {
  /// See also [getReportDetail].
  const GetReportDetailFamily();

  /// See also [getReportDetail].
  GetReportDetailProvider call(
    String shareCode,
  ) {
    return GetReportDetailProvider(
      shareCode,
    );
  }

  @override
  GetReportDetailProvider getProviderOverride(
    covariant GetReportDetailProvider provider,
  ) {
    return call(
      provider.shareCode,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'getReportDetailProvider';
}

/// See also [getReportDetail].
class GetReportDetailProvider
    extends AutoDisposeFutureProvider<BaseResponse<ReportDetailModel>> {
  /// See also [getReportDetail].
  GetReportDetailProvider(
    String shareCode,
  ) : this._internal(
          (ref) => getReportDetail(
            ref as GetReportDetailRef,
            shareCode,
          ),
          from: getReportDetailProvider,
          name: r'getReportDetailProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$getReportDetailHash,
          dependencies: GetReportDetailFamily._dependencies,
          allTransitiveDependencies:
              GetReportDetailFamily._allTransitiveDependencies,
          shareCode: shareCode,
        );

  GetReportDetailProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.shareCode,
  }) : super.internal();

  final String shareCode;

  @override
  Override overrideWith(
    FutureOr<BaseResponse<ReportDetailModel>> Function(
            GetReportDetailRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: GetReportDetailProvider._internal(
        (ref) => create(ref as GetReportDetailRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        shareCode: shareCode,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<BaseResponse<ReportDetailModel>>
      createElement() {
    return _GetReportDetailProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is GetReportDetailProvider && other.shareCode == shareCode;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, shareCode.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin GetReportDetailRef
    on AutoDisposeFutureProviderRef<BaseResponse<ReportDetailModel>> {
  /// The parameter `shareCode` of this provider.
  String get shareCode;
}

class _GetReportDetailProviderElement
    extends AutoDisposeFutureProviderElement<BaseResponse<ReportDetailModel>>
    with GetReportDetailRef {
  _GetReportDetailProviderElement(super.provider);

  @override
  String get shareCode => (origin as GetReportDetailProvider).shareCode;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
