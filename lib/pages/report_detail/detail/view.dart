import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/report_detail/basic/table_demo.dart';
import 'package:zrreport/pages/report_detail/detail/widgets/permission_bottom_sheet.dart';
import 'package:zrreport/pages/report_detail/detail/widgets/permission_setting_button.dart';
import 'package:zrreport/pages/report_detail/example_info.dart';
import 'package:zrreport/pages/report_detail/invoice_information/invoice_information_view.dart';
import 'package:zrreport/pages/report_detail/requery/refresh_notice_widget.dart';

import '../legal_information/case_page.dart';
import '../paidTaxes/view.dart';
import '../supplier_information/supplier_information_view.dart';
import 'provider.dart';
import '../basic/view.dart';
import 'package:get/get.dart';

class ReportDetail extends BasePage {
  const ReportDetail({super.key, required this.shareCode, this.isExample = false});

  final String shareCode;
  final bool isExample;

  // 获取来源 Provider 引用
  Object? get sourceProvider {
    final arguments = Get.arguments;
    if (arguments != null && arguments['sourceProvider'] != null) {
      return arguments['sourceProvider'];
    }
    return null;
  }
  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        if (isExample == true) {
          return DefaultTabController(
          length: 5,
          child: Scaffold(
            appBar: AppBar(
              title: Text('助融报告'),
            ),
            body: Container(
              decoration: BoxDecoration(
                color: Color(0xFFF4F8FB),
                image: DecorationImage(
                  alignment: Alignment.topCenter,
                  image: AssetImage(AssetsImages.historyBackgroundPng),
                  fit: BoxFit.fitWidth,
                ),
              ),
              child: Center(
                  child: _buildContent(example_DetailInfoModel)
              ),
            ),
          ),
        );
        } else {
           final response = ref.watch(GetReportDetailProvider(shareCode));
            return DefaultTabController(
          length: 5,
          child: Scaffold(
            appBar: AppBar(
              title: Text('助融报告'),
              actions: [
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  child: _PermissionSettingButtonWrapper(
                    shareCode: shareCode,
                  ),
                ),
              ],
            ),
            body: Container(
              decoration: BoxDecoration(
                color: Color(0xFFF4F8FB),
                image: DecorationImage(
                  alignment: Alignment.topCenter,
                  image: AssetImage(AssetsImages.historyBackgroundPng),
                  fit: BoxFit.fitWidth,
                ),
              ),
              child: Center(
                  child:
              switch (response) {
                AsyncData(:final value) => _buildContent(value.data!),
                AsyncError() => ErrorStatusWidget(
                    text: '${response.error}',
                    onAttempt: () =>
                        ref.refresh(getReportDetailProvider(shareCode))),
                _ => const LoadingWidget(),
              }
              ),
            ),
          ),
        );
        }
      },
    );
  }

  _buildHeader(ReportDetailModel mode) {
    return Padding(
      padding: const EdgeInsets.only(left: 20, right: 20, top: 18),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '税票分析报告',
            style: TextStyle(fontSize: 14, color: Colors.white),
          ),
          SizedBox(height: 18),
          Center(
            child: FittedBox(
              child: Text(
                textAlign: TextAlign.center,
                mode.enterpriseName ?? '',
                style: TextStyle(
                    fontSize: 20,
                    height: 28 / 20.0,
                    color: Colors.white,
                    fontWeight: FontWeight.w600),
              ),
            ),
          ),
          SizedBox(height: 5),
          Center(
            child: Text(
              mode.createTime,
              style: TextStyle(fontSize: 14, color: Colors.white),
            ),
          ),
          SizedBox(height: 16),
        ],
      ),
    );
  }

  _buildContent(ReportDetailModel model) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(model),
        Expanded(
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Column(
              children: [
                // 重新查询区域
                RefreshNoticeWidget(
                  shareCode: shareCode,
                  sourceProvider: sourceProvider,
                ),
                _tabbar(),
                Divider(
                  height: 1,
                ),
                Expanded(
                  child: TabBarView(
                    children: [
                      ReportBasicInfoView(shareCode: shareCode, isExample: isExample),
                      TaxInfoView(shareCode: shareCode, isExample: isExample),
                      InvoiceInformationView(shareCode: shareCode, isExample: isExample),
                      SupplierInformationView(shareCode: shareCode, isExample: isExample),
                      ReportCasePageView(
                          shareCode: shareCode, isExample: isExample),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  _tabbar() {
    var selectedTextColor = AppColors.primary;
    var unselectedTextColor = AppColors.textColor9;
    return TabBar(
      isScrollable: true,
      indicatorSize: TabBarIndicatorSize.tab,
      indicator: MyCustomIndicator(color: selectedTextColor, indWidth: 40),
      labelColor: selectedTextColor,
      unselectedLabelColor: unselectedTextColor,
      unselectedLabelStyle: TextStyle(
        fontSize: 13,
        fontWeight: FontWeight.w600,
      ),
      labelStyle: TextStyle(
        fontSize: 13,
        fontWeight: FontWeight.w600,
      ),
      tabs: [
        Tab(text: '基本信息'),
        Tab(text: '纳税信息'),
        Tab(text: '开票信息'),
        Tab(text: '供应商信息'),
        Tab(text: '工商司法'),
      ],
    );
  }
}

// 权限设置按钮包装器，用于防多次点击
class _PermissionSettingButtonWrapper extends ConsumerStatefulWidget {
  final String shareCode;

  const _PermissionSettingButtonWrapper({
    required this.shareCode,
  });

  @override
  ConsumerState<_PermissionSettingButtonWrapper> createState() => _PermissionSettingButtonWrapperState();
}

class _PermissionSettingButtonWrapperState extends ConsumerState<_PermissionSettingButtonWrapper> {
  bool _isShowingSheet = false;

  // 权限设置底部弹窗
  Future<void> _showPermissionSheet(BuildContext context) async {
    // 防多次点击保护
    if (_isShowingSheet) {
      return;
    }
    
    setState(() {
      _isShowingSheet = true;
    });
    
    try {
      // 先刷新报告详情数据，确保获取最新的visibleType
      // ignore: unused_result
      await ref.refresh(getReportDetailProvider(widget.shareCode));
      
      // 获取当前的报告详情数据
      final reportDetailResponse = ref.read(getReportDetailProvider(widget.shareCode));
      ReportDetailModel? reportDetail;
      int? visibleType;
      
      if (reportDetailResponse.hasValue && reportDetailResponse.value?.data != null) {
        reportDetail = reportDetailResponse.value!.data!;
        // 从报告详情中获取visibleType
        visibleType = reportDetail.visibleType;
      }
      
      await PermissionBottomSheet.show(
        context,
        initialType: PermissionType.public,
        shareCode: widget.shareCode,
        reportDetail: reportDetail,
        visibleType: visibleType,
        onConfirm: (PermissionType type, List<String>? ids) {
          // 权限设置成功后，刷新报告详情数据以获取最新的visibleType
          // ignore: unused_result
          ref.refresh(getReportDetailProvider(widget.shareCode));
        },
      );
    } finally {
      // 弹窗关闭后重置状态
      if (mounted) {
        setState(() {
          _isShowingSheet = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return PermissionSettingButton(
      onTap: () => _showPermissionSheet(context),
    );
  }
}
