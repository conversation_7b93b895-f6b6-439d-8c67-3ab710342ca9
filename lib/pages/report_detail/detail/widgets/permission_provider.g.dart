// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'permission_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$specifyAccessUserListHash() =>
    r'ace09bd833abd0909f218ef8919ae3f79012cc3b';

/// See also [SpecifyAccessUserList].
@ProviderFor(SpecifyAccessUserList)
final specifyAccessUserListProvider = AutoDisposeNotifierProvider<
    SpecifyAccessUserList, SpecifyAccessUserState>.internal(
  SpecifyAccessUserList.new,
  name: r'specifyAccessUserListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$specifyAccessUserListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SpecifyAccessUserList = AutoDisposeNotifier<SpecifyAccessUserState>;
String _$permissionSettingHash() => r'56bbfefba548eb5a6342c792e5e071ae54caf23d';

/// See also [PermissionSetting].
@ProviderFor(PermissionSetting)
final permissionSettingProvider = AutoDisposeNotifierProvider<PermissionSetting,
    PermissionSettingState>.internal(
  PermissionSetting.new,
  name: r'permissionSettingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$permissionSettingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PermissionSetting = AutoDisposeNotifier<PermissionSettingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
