import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';
import 'dart:math' as math;
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'permission_provider.dart';
import '../provider.dart';

enum PermissionType {
  public,      // 内容全公开
  partPublic,  // 内容部分公开
  private,     // 私密
  specified    // 指定访问
}

class PermissionBottomSheet extends ConsumerStatefulWidget {
  final PermissionType initialType;
  final Function(PermissionType, List<String>?) onConfirm;
  final Function(PermissionType)? onInfoTap;
  final String shareCode;
  final ReportDetailModel? reportDetail;
  final int? visibleType;

  const PermissionBottomSheet({
    Key? key,
    this.initialType = PermissionType.public,
    required this.onConfirm,
    this.onInfoTap,
    required this.shareCode,
    this.reportDetail,
    this.visibleType,
  }) : super(key: key);

  /// 显示权限设置底部弹窗
  static Future<void> show(
      BuildContext context, {
        PermissionType initialType = PermissionType.public,
        required Function(PermissionType, List<String>?) onConfirm,
        Function(PermissionType)? onInfoTap,
        required String shareCode,
        ReportDetailModel? reportDetail,
        int? visibleType,
      }) async {
    await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      enableDrag: true,
      isDismissible: true,
      useSafeArea: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => PermissionBottomSheet(
        initialType: initialType,
        onConfirm: onConfirm,
        onInfoTap: onInfoTap,
        shareCode: shareCode,
        reportDetail: reportDetail,
        visibleType: visibleType,
      ),
    );
  }

  @override
  ConsumerState<PermissionBottomSheet> createState() => _PermissionBottomSheetState();
}

class _PermissionBottomSheetState extends ConsumerState<PermissionBottomSheet> {
  late PermissionType _selectedType;
  PermissionType? _showingTooltipFor;
  OverlayEntry? _overlayEntry;
  final List<GlobalKey> _optionKeys = List.generate(4, (_) => GlobalKey());
  final List<GlobalKey> _infoKeys = List.generate(4, (_) => GlobalKey());
  
  // 用于"指定访问"的ID输入控制器 - 总共10个
  final List<TextEditingController> _idControllers = List.generate(
    10, 
    (_) => TextEditingController()
  );
  
  // 为每个输入框创建焦点节点
  final List<FocusNode> _focusNodes = List.generate(
    10,
    (_) => FocusNode()
  );
  
  // 创建焦点范围节点，用于管理所有输入框的焦点
  final FocusScopeNode _focusScopeNode = FocusScopeNode();
  
  // 滚动控制器
  final ScrollController _scrollController = ScrollController();
  
  // 当前获得焦点的输入框索引
  int _currentFocusIndex = -1;
  
  // 存储输入框中是否有内容的状态
  final List<bool> _hasInputContent = List.generate(10, (_) => false);
  
  // 是否显示输入框
  bool get _showInputFields => _selectedType == PermissionType.specified;
  
  // 是否展开所有输入框
  bool _expandedAll = false;

  @override
  void initState() {
    super.initState();
    
    // 清空之前的状态，确保每次打开弹窗都是最新状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(specifyAccessUserListProvider.notifier).clearUsers();
      ref.read(permissionSettingProvider.notifier).reset();
    });
    
    // 根据visibleType设置默认选中的权限类型
    if (widget.visibleType != null) {
      _selectedType = _getPermissionTypeFromVisibleType(widget.visibleType!);
    } else {
    _selectedType = widget.initialType;
    }
    
    // 为每个焦点节点添加监听器
    for (int i = 0; i < _focusNodes.length; i++) {
      final int index = i;
      _focusNodes[i].addListener(() {
        if (_focusNodes[index].hasFocus) {
          if (_currentFocusIndex != index) {
            setState(() {
              _currentFocusIndex = index;
            });
            // 当输入框获得焦点时，自动滚动到可见位置
            _scrollToInputField(index);
          }
        }
        
        // 当焦点失去时验证手机号
        if (!_focusNodes[index].hasFocus && _currentFocusIndex == index) {
          _validatePhoneNumber(index);
        }
      });
    }
    
    // 如果默认选中的是指定访问，需要获取用户列表
    if (_selectedType == PermissionType.specified) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(specifyAccessUserListProvider.notifier).getSpecifyAccessUserList(widget.shareCode);
      });
    }
  }

  @override
  void dispose() {
    _removeTooltip();
    
    // 释放控制器和焦点节点资源
    for (var controller in _idControllers) {
      controller.dispose();
    }
    
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    
    // 释放焦点范围节点和滚动控制器
    _focusScopeNode.dispose();
    _scrollController.dispose();
    
    super.dispose();
  }

  // 处理指定访问选项被选中时的逻辑
  void _onSpecifiedAccessSelected() {
    setState(() {
      _selectedType = PermissionType.specified;
      _expandedAll = false;
    });
    _removeTooltip();
    
    // 触发API请求获取用户列表
    ref.read(specifyAccessUserListProvider.notifier).getSpecifyAccessUserList(widget.shareCode);
  }

  // 填充输入框数据
  void _fillInputFields(List<SpecifyAccessUser> users) {
    for (var controller in _idControllers) {
      controller.clear();
    }

    for (int i = 0; i < _hasInputContent.length; i++) {
      _hasInputContent[i] = false;
    }
    for (int i = 0; i < users.length && i < _idControllers.length; i++) {
      _idControllers[i].text = users[i].phone;
      _hasInputContent[i] = users[i].phone.isNotEmpty;
    }

    setState(() {});
  }

  void _removeTooltip() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    _showingTooltipFor = null;
  }

  void _showTooltip(PermissionType type, BuildContext context, GlobalKey infoKey, int index) {
    // 如果已经显示了相同类型的提示，则不做操作
    if (_showingTooltipFor == type) return;
    
    // 移除现有的提示（如果有）
    _removeTooltip();
    
    // 获取信息图标的位置和大小
    final RenderBox? infoRenderBox = infoKey.currentContext?.findRenderObject() as RenderBox?;
    if (infoRenderBox == null) return;
    
    final infoPosition = infoRenderBox.localToGlobal(Offset.zero);
    final infoSize = infoRenderBox.size;
    
    // 获取屏幕尺寸
    final screenSize = MediaQuery.of(context).size;
    final statusBarHeight = MediaQuery.of(context).padding.top;
    
    _showingTooltipFor = type;
    
    // 根据权限类型调整浮窗宽度
    double tooltipWidth = 240;
    if (type == PermissionType.public || type == PermissionType.private) {
      tooltipWidth = 200;
    }
    
    // 计算浮窗的水平位置 - 尽量让三角形指向"i"图标
    double tooltipLeft = infoPosition.dx - tooltipWidth / 2 + infoSize.width / 2;
    
    // 边界检查 - 确保浮窗不超出屏幕左右边界
    if (tooltipLeft < 16) {
      tooltipLeft = 16; // 左边界保持16px的间距
    } else if (tooltipLeft + tooltipWidth > screenSize.width - 16) {
      tooltipLeft = screenSize.width - tooltipWidth - 16; // 右边界保持16px的间距
    }
    
    // 计算三角形的位置 - 应该指向"i"图标中心
    double triangleLeft = infoPosition.dx - tooltipLeft + infoSize.width / 2 - 8; // 8是三角形宽度的一半
    
    // 确保三角形在浮窗内的合理位置
    triangleLeft = math.max(16, math.min(tooltipWidth - 32, triangleLeft));
    
    // 计算浮窗高度 - 根据内容类型估算
    double estimatedTooltipHeight;
    switch (type) {
      case PermissionType.partPublic:
        estimatedTooltipHeight = 60;
        break;
      case PermissionType.public:
        estimatedTooltipHeight = 40;
        break;
      case PermissionType.private:
        estimatedTooltipHeight = 40;
        break;
      case PermissionType.specified:
        estimatedTooltipHeight = 50;
        break;
      default:
        estimatedTooltipHeight = 50;
    }
    
    // 统一使用第三个浮窗的位置计算逻辑
    double tooltipTop = infoPosition.dy - estimatedTooltipHeight - 10;
    
    // 检查是否超出屏幕顶部
    if (tooltipTop < statusBarHeight) {
      // 如果会超出屏幕顶部，则放在"i"图标下方
      tooltipTop = infoPosition.dy + infoSize.height + 10;
    }
    
    _overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // 透明层，用于捕获点击事件关闭提示
          Positioned.fill(
            child: GestureDetector(
              onTap: _removeTooltip,
              behavior: HitTestBehavior.opaque,
              child: Container(color: Colors.transparent),
            ),
          ),
          // 提示信息浮层
          Positioned(
            left: tooltipLeft,
            top: tooltipTop,
            child: Material(
              color: Colors.transparent,
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  // 黑色背景气泡
                  Container(
                    width: tooltipWidth,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.black,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _getPermissionTitle(type),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // 小三角指示器 - 指向"i"图标
                  Positioned(
                    bottom: -8,
                    left: triangleLeft,
                    child: CustomPaint(
                      size: const Size(16, 8),
                      painter: TrianglePointer(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
    
    Overlay.of(context).insert(_overlayEntry!);
  }

  String _getPermissionTitle(PermissionType type) {
    switch (type) {
      case PermissionType.public:
        return '所有访问者登录后即可查看完全部报告内容';
      case PermissionType.partPublic:
        return '访问者登录后以下内容将会被隐藏：企业名称、法人姓名、手机号、信用代码等基本信息';
      case PermissionType.private:
        return '仅自己可见';
      case PermissionType.specified:
        return '直接添加用户并开启查看权限';
      default:
        return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    // 监听指定访问用户列表状态变化
    ref.listen<SpecifyAccessUserState>(specifyAccessUserListProvider, (previous, next) {
      if (next.isSuccess && next.hasData && _selectedType == PermissionType.specified) {
        // 当数据加载成功时，填充输入框
        _fillInputFields(next.users);
      } else if (next.isError) {
        // 当加载失败时，显示错误提示
        Loading.toast('加载用户列表失败: ${next.error ?? "未知错误"}');
      }
    });

    // 获取键盘高度和屏幕信息
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final screenHeight = MediaQuery.of(context).size.height;
    final safeAreaTop = MediaQuery.of(context).padding.top;
    final safeAreaBottom = MediaQuery.of(context).padding.bottom;
    
    // 计算可用的屏幕高度
    final availableHeight = screenHeight - safeAreaTop - safeAreaBottom;
    
    // 动态计算内容区域高度，考虑键盘状态
    double contentHeight;
    if (_showInputFields) {
      // 显示输入框时的固定高度（无论是否展开都保持相同）
      contentHeight = 335.0;
    } else {
      // 只显示4个选项时的高度
      contentHeight = 250.0;
    }
    
    // 当键盘弹起时，限制内容高度以避免溢出
    if (keyboardHeight > 0) {
      // 预留空间：拖拽指示器(28) + 按钮区域(96) + 键盘高度 + 安全边距(32)
      final reservedSpace = 28 + 96 + keyboardHeight + 32;
      final maxContentHeight = availableHeight - reservedSpace;
      contentHeight = contentHeight.clamp(150.0, maxContentHeight);
    }
    
    return AnimatedPadding(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      padding: EdgeInsets.only(bottom: keyboardHeight),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: availableHeight * 0.9,
        ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
        ),
        child: SafeArea(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
              // 顶部拖拽指示器
              Container(
                margin: const EdgeInsets.only(top: 12, bottom: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // 动态高度的可滚动内容区域
              Flexible(
                child: SizedBox(
                  height: contentHeight,
                  child: SingleChildScrollView(
                    controller: _scrollController,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: _buildAllContent(),
                  ),
                ),
              ),
              // 固定底部按钮区域
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    top: BorderSide(
                      color: Colors.grey[200]!,
                      width: 1,
                    ),
                  ),
                ),
                child: _buildButtons(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建所有内容的方法
  Widget _buildAllContent() {
    return Column(
      children: [
        _buildOptionItem(PermissionType.public, '内容全公开', 0),
        const SizedBox(height: 12),
        _buildOptionItem(PermissionType.partPublic, '内容部分公开', 1),
        const SizedBox(height: 12),
        _buildOptionItem(PermissionType.private, '私密', 2),
        const SizedBox(height: 12),
        _buildOptionItem(PermissionType.specified, '指定访问', 3),
        // 条件性地显示输入框
        if (_showInputFields) ...[
          const SizedBox(height: 16),
          _buildInputFields(),
        ],
        // 确保底部有足够空间
        const SizedBox(height: 16),
      ],
    );
  }

  // 构建ID输入框部分
  Widget _buildInputFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 当展开时显示全部行，折叠时只显示一行
        ...List.generate(_expandedAll ? 5 : 1, (rowIndex) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: _buildInputRow(rowIndex),
          );
        }),
        
        // 展开/收起按钮
        Center(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _expandedAll = !_expandedAll;
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 12),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _expandedAll ? '收起所有' : '展开所有',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF4080FF),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Icon(
                    _expandedAll ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    size: 18,
                    color: const Color(0xFF4080FF),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
  
  // 构建一行两个输入框
  Widget _buildInputRow(int rowIndex) {
    return Row(
      children: List.generate(2, (colIndex) {
        final int controllerIndex = rowIndex * 2 + colIndex;
        final TextEditingController controller = _idControllers[controllerIndex];
        final FocusNode focusNode = _focusNodes[controllerIndex];
        
        // 判断输入框是否有焦点
        final bool hasFocus = _currentFocusIndex == controllerIndex;
        
        return Expanded(
          child: GestureDetector(
            onTap: () {
              // 先取消所有焦点
              _focusScopeNode.unfocus();
              
              // 更新当前焦点索引
              setState(() {
                _currentFocusIndex = controllerIndex;
              });
              
              // 请求新的焦点
              FocusScope.of(context).requestFocus(focusNode);
              
              // 自动滚动到可见位置
              _scrollToInputField(controllerIndex);
            },
            child: Container(
              margin: EdgeInsets.only(
                right: colIndex == 0 ? 8 : 0,
                left: colIndex == 1 ? 8 : 0,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  // 有焦点时使用蓝色边框，否则使用灰色边框
                  color: hasFocus ? const Color(0xFF4080FF) : const Color(0xFFDDDDDD),
                ),
              ),
              child: TextField(
                controller: controller,
                focusNode: focusNode,
                decoration: InputDecoration(
                  hintText: '请输入用户手机号',
                  hintStyle: const TextStyle(
                    color: Color(0xFFCCCCCC),
                    fontSize: 12,
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                  border: InputBorder.none,
                  isDense: true,
                  // 控制图标大小约束，防止影响输入框高度
                  suffixIconConstraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                  // 只有当输入框有内容且拥有焦点时才显示删除按钮
                  suffixIcon: (_hasInputContent[controllerIndex] && _currentFocusIndex == controllerIndex)
                      ? Padding(
                          padding: const EdgeInsets.only(right: 10),
                          child: GestureDetector(
                            onTap: () {
                              // 清空输入框并更新状态
                              controller.clear();
                              setState(() {
                                _hasInputContent[controllerIndex] = false;
                              });
                            },
                            child: Container(
                              width: 14,
                              height: 14,
                              decoration: const BoxDecoration(
                                color: Color(0xFFD9D9D9),
                                shape: BoxShape.circle,
                              ),
                              child: const Center(
                                child: Icon(
                                  Icons.close,
                                  size: 10,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        )
                      : const SizedBox(width: 14, height: 14), // 占位，保持高度一致
                ),
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
                textInputAction: TextInputAction.next,
                onChanged: (value) {
                  // 根据是否有内容更新状态
                  final bool hasContent = value.isNotEmpty;
                  if (hasContent != _hasInputContent[controllerIndex]) {
                    setState(() {
                      _hasInputContent[controllerIndex] = hasContent;
                    });
                  }
                  
                  // 实时验证手机号（延迟执行，避免频繁提示）
                  if (value.length == 11) {
                    // 当输入达到11位时进行验证
                    Future.delayed(const Duration(milliseconds: 300), () {
                      if (_idControllers[controllerIndex].text.trim() == value.trim()) {
                        _validatePhoneNumber(controllerIndex);
                      }
                    });
                  }
                },
                onSubmitted: (_) {
                  // 提交后自动切换焦点到下一个输入框
                  if (controllerIndex < 9) {
                    // 更新当前焦点索引
                    setState(() {
                      _currentFocusIndex = controllerIndex + 1;
                    });
                    FocusScope.of(context).requestFocus(_focusNodes[controllerIndex + 1]);
                  } else {
                    // 取消所有焦点
                    setState(() {
                      _currentFocusIndex = -1;
                    });
                    FocusScope.of(context).unfocus();
                  }
                },
              ),
            ),
          ),
        );
      }),
    );
  }
  
  // 验证手机号的方法
  void _validatePhoneNumber(int index) {
    final String value = _idControllers[index].text.trim();
    
    // 如果输入为空，不做验证
    if (value.isEmpty) {
      return;
    }
    
    // 检查手机号长度
    if (value.length != 11) {
      Loading.toast('手机号码错误！');
      return;
    }
    
    // 中国大陆手机号正则表达式
    final RegExp phoneRegExp = RegExp(r'^1[3-9]\d{9}$');
    final bool isValid = phoneRegExp.hasMatch(value);
    
    // 如果手机号格式无效，显示Toast提示
    if (!isValid) {
      Loading.toast('手机号码错误！');
      return;
    }
    
    // 检查是否与其他输入框中的手机号重复
    for (int i = 0; i < _idControllers.length; i++) {
      if (i != index) { // 不检查自己
        final String otherValue = _idControllers[i].text.trim();
        if (otherValue.isNotEmpty && otherValue == value) {
          Loading.toast('手机号码重复输入');
          return;
        }
      }
    }
  }

  // 当输入框获得焦点时，自动滚动到可见位置
  void _scrollToInputField(int index) {
    if (!_scrollController.hasClients) return;
    
    Future.delayed(const Duration(milliseconds: 400), () {
      if (!mounted || !_scrollController.hasClients) return;
      
      // 获取键盘高度
      final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
      
      // 计算输入框的位置
      final double baseOffset = 294.0;
      final int rowIndex = index ~/ 2;
      final double inputRowHeight = 60.0; // 输入框行高度 + 间距
      final double targetOffset = baseOffset + (rowIndex * inputRowHeight);
      
      // 获取可视区域信息
      final double viewportHeight = _scrollController.position.viewportDimension;
      final double maxScrollExtent = _scrollController.position.maxScrollExtent;
      
      // 考虑键盘高度，计算实际可用的视口高度
      final double availableHeight = viewportHeight - keyboardHeight;
      
      // 计算理想的滚动位置，确保输入框在可见区域的上半部分
      final double idealOffset = targetOffset - (availableHeight * 0.3);
      
      // 限制滚动范围
      final double clampedOffset = idealOffset.clamp(0.0, maxScrollExtent);
      
      _scrollController.animateTo(
        clampedOffset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    });
  }

  Widget _buildOptionItem(PermissionType type, String label, int keyIndex) {
    bool isSelected = _selectedType == type;
    final borderRadius = BorderRadius.circular(8);

    return InkWell(
      key: _optionKeys[keyIndex],
      onTap: () {
        if (type == PermissionType.specified) {
          // 如果是指定访问，调用专门的处理方法
          _onSpecifiedAccessSelected();
        } else {
        setState(() {
          _selectedType = type;
        });
          _removeTooltip();
        }
      },
      borderRadius: borderRadius,
      child: Stack(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            decoration: BoxDecoration(
              color: const Color(0xFFF7F8FA),
              borderRadius: borderRadius,
            ),
        child: Row(
          children: [
            Text(
              label,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(width: 8),
                GestureDetector(
                  key: _infoKeys[keyIndex],
                  onTap: () {
                    _showTooltip(type, context, _infoKeys[keyIndex], keyIndex);
                    if (widget.onInfoTap != null) {
                      widget.onInfoTap!(type);
                    }
                  },
                  child: Container(
                    width: 16,
                    height: 16,
                    decoration: const BoxDecoration(
                      color: Color(0xFFCCCCCC),
                      shape: BoxShape.circle,
                    ),
                    child: const Center(
                      child: Text(
                        'i',
              style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
              ),
            ),
            const Spacer(),
                // 占位，确保布局一致
                const SizedBox(width: 24, height: 24),
              ],
            ),
          ),
          Positioned(
            top: 0,
            right: 0,
            child: Stack(
              children: [
                // 三角形背景
                ClipPath(
                  clipper: CornerClipper(radius: 8),
                  child: Container(
                    width: 24,
                    height: 24,
                    color: isSelected ? const Color(0xFF4080FF) : Colors.grey.shade300,
              ),
                ),
                // 无论是否选中，都显示白色对钩
                Positioned(
                  top: 4,
                  right: 4,
                  child: CustomPaint(
                    size: const Size(10, 10),
                    painter: CheckmarkPainter(),
              ),
            ),
          ],
        ),
          ),
        ],
      ),
    );
  }

  Widget _buildButtons() {
    final blueColor = const Color(0xFF4080FF);
    
    return Row(
        children: [
          Expanded(
            child: TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              style: TextButton.styleFrom(
              backgroundColor: Colors.white,
              side: BorderSide(
                color: blueColor,
                width: 1,
                ),
                shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(vertical: 14),
            ),
            child: Text(
                '取消',
                style: TextStyle(
                  fontSize: 16,
                color: blueColor,
                fontWeight: FontWeight.normal,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
          Expanded(
            child: Consumer(
              builder: (context, ref, child) {
                final permissionSettingState = ref.watch(permissionSettingProvider);
                
                return TextButton(
                  onPressed: permissionSettingState.isLoading ? null : () async {
                    await _handleConfirm(ref);
              },
              style: TextButton.styleFrom(
                  backgroundColor: blueColor,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                ),
                  padding: const EdgeInsets.symmetric(vertical: 14),
              ),
                  child: permissionSettingState.isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text(
                '确定',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                          fontWeight: FontWeight.normal,
                ),
              ),
                );
              },
            ),
          ),
        ],
    );
  }

  // 处理确定按钮点击
  Future<void> _handleConfirm(WidgetRef ref) async {
    try {
      // 收集输入框中的手机号
      List<String>? specifyAccessUsers;
      if (_selectedType == PermissionType.specified) {
        final phoneList = _idControllers
            .map((controller) => controller.text.trim())
            .where((text) => text.isNotEmpty)
            .toList();
        // 限制最多10个手机号
        if (phoneList.length > 10) {
          Loading.toast('最多只能添加10个手机号');
          return;
        }
        
        // 验证手机号格式和重复性
        final RegExp phoneRegExp = RegExp(r'^1[3-9]\d{9}$');
        final List<String> phoneNumbers = [];
        for (String phone in phoneList) {
          // 检查长度
          if (phone.length != 11) {
            Loading.toast('手机号码错误！');
            return;
          }
          
          // 检查格式
          if (!phoneRegExp.hasMatch(phone)) {
            Loading.toast('手机号码错误！');
            return;
          }
          
          // 检查重复
          if (phoneNumbers.contains(phone)) {
            Loading.toast('手机号码重复输入');
            return;
          }
          
          phoneNumbers.add(phone);
        }
        
        // 直接使用List<String>类型
        if (phoneNumbers.isNotEmpty) {
          specifyAccessUsers = phoneNumbers;
        }
      }

      // 转换权限类型为数字
      int visibleType = _getVisibleTypeValue(_selectedType);

      // 获取报告详情数据
      ReportDetailModel? reportDetail = widget.reportDetail;
      if (reportDetail == null) {
        // 如果没有传入报告详情，尝试从provider获取
        final reportDetailResponse = ref.read(getReportDetailProvider(widget.shareCode));

        if (reportDetailResponse.hasValue) {
          final baseResponse = reportDetailResponse.value;
          if (baseResponse != null && baseResponse.data != null) {
            reportDetail = baseResponse.data!;
          } else {
            Loading.toast('获取报告信息失败，请重试');
            return;
          }
        } else if (reportDetailResponse.hasError) {
          Loading.toast('获取报告信息失败，请重试');
          return;
        } else {
          Loading.toast('报告信息加载中，请稍后重试');
          return;
        }
      } else {
      }

      // 转换id和taskId为int类型
      int? id;
      int taskId;
      
      try {
        // 处理id转换（可能为null）
        if (reportDetail.id != null) {
          if (reportDetail.id is String) {
            id = int.tryParse(reportDetail.id as String);
          } else if (reportDetail.id is int) {
            id = reportDetail.id as int;
          }
        }
        
        // 处理taskId转换（必需）
        if (reportDetail.taskId is String) {
          taskId = int.parse(reportDetail.taskId as String);
        } else if (reportDetail.taskId is int) {
          taskId = reportDetail.taskId as int;
        } else {
          throw Exception('taskId类型不支持: ${reportDetail.taskId.runtimeType}');
        }
        
      } catch (e) {
        Loading.toast('参数格式错误，请重试');
        return;
      }

      debugPrint('API参数: id=$id, taskId=$taskId, shareCode=${reportDetail.shareCode}, visibleType=$visibleType, specifyAccessUsers=$specifyAccessUsers');
      
      // 调用权限设置API
      final success = await ref.read(permissionSettingProvider.notifier).setPermissionVisible(
        id: id,
        taskId: taskId,
        shareCode: reportDetail.shareCode,
        visibleType: visibleType,
        specifyAccessUsers: specifyAccessUsers,
      );


      if (success) {
        widget.onConfirm(_selectedType, specifyAccessUsers);
        // 关闭弹窗
        Navigator.of(context).pop();
        // 显示成功提示
        Loading.toast('权限设置成功');
      } else {
        final error = ref.read(permissionSettingProvider).error;
        debugPrint('权限设置失败，错误信息: $error');
        
        // 特殊处理服务端500错误
        if (error != null && error.contains('当前网络繁忙，请稍后再试')) {
          Loading.toast('权限设置功能暂时不可用，请联系客服或稍后重试');
        } else {
          Loading.toast('权限设置失败: ${error ?? "未知错误"}');
        }
      }
    } catch (e) {

      Loading.toast('权限设置失败: $e');
    }
  }

  // 将权限类型转换为对应的数字值
  int _getVisibleTypeValue(PermissionType type) {
    switch (type) {
      case PermissionType.public:
        return 0; // 内容全公开
      case PermissionType.partPublic:
        return 1; // 内容部分公开
      case PermissionType.private:
        return 2; // 私密
      case PermissionType.specified:
        return 3; // 指定访问
    }
  }

  // 将visibleType数值转换为PermissionType枚举
  PermissionType _getPermissionTypeFromVisibleType(int visibleType) {
    switch (visibleType) {
      case 0:
        return PermissionType.public; // 内容全公开
      case 1:
        return PermissionType.partPublic; // 内容部分公开
      case 2:
        return PermissionType.private; // 私密
      case 3:
        return PermissionType.specified; // 指定访问
      default:
        return PermissionType.public; // 默认为内容全公开
    }
  }
}

// 自定义对钩绘制器 - 更精确控制对钩的样式
class CheckmarkPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5
      ..strokeCap = StrokeCap.round;
    
    final path = Path();
    // 起点在左下
    path.moveTo(size.width * 0.2, size.height * 0.5);
    // 连接到中间偏下位置
    path.lineTo(size.width * 0.45, size.height * 0.75);
    // 连接到右上角
    path.lineTo(size.width * 0.8, size.height * 0.25);
    
    canvas.drawPath(path, paint);
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldPainter) => false;
}

class CornerClipper extends CustomClipper<Path> {
  final double radius;

  CornerClipper({required this.radius});

  @override
  Path getClip(Size size) {
    final path = Path();
    
    // 从右上角开始，考虑圆角
    path.moveTo(size.width - radius, 0);
    
    // 右上角圆角弧线
    path.quadraticBezierTo(size.width, 0, size.width, radius);
    
    // 右边
    path.lineTo(size.width, size.height);
    
    // 回到起点
    path.lineTo(0, 0);
    path.close();
    
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

class TrianglePointer extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;
    
    final path = Path();
    path.moveTo(0, 0);
    path.lineTo(size.width, 0);
    path.lineTo(size.width / 2, size.height);
    path.close();
    
    canvas.drawPath(path, paint);
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldPainter) => false;
}