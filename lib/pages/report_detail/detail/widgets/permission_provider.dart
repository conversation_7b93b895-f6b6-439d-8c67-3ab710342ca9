import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';

part 'permission_provider.g.dart';

/// 指定访问用户列表状态
class SpecifyAccessUserState {
  final PageStatus pageStatus;
  final List<SpecifyAccessUser> users;
  final String? error;

  SpecifyAccessUserState({
    this.pageStatus = PageStatus.initial,
    this.users = const [],
    this.error,
  });

  SpecifyAccessUserState copyWith({
    PageStatus? pageStatus,
    List<SpecifyAccessUser>? users,
    String? error,
  }) {
    return SpecifyAccessUserState(
      pageStatus: pageStatus ?? this.pageStatus,
      users: users ?? this.users,
      error: error ?? this.error,
    );
  }

  bool get hasData => users.isNotEmpty;
  bool get isLoading => pageStatus == PageStatus.loading;
  bool get isSuccess => pageStatus == PageStatus.success;
  bool get isError => pageStatus == PageStatus.error;
}

@riverpod
class SpecifyAccessUserList extends _$SpecifyAccessUserList {
  @override
  SpecifyAccessUserState build() {
    return SpecifyAccessUserState();
  }

  /// 获取指定访问用户列表
  Future<void> getSpecifyAccessUserList(String shareCode) async {
    try {
      state = state.copyWith(pageStatus: PageStatus.loading);
      
      final response = await QueryApi.getSpecifyAccessUserlist(shareCode);
      
      if (response.data != null) {
        state = state.copyWith(
          pageStatus: PageStatus.success,
          users: response.data!,
        );
      } else {
        state = state.copyWith(
          pageStatus: PageStatus.empty,
          users: [],
        );
      }
    } catch (e) {
      state = state.copyWith(
        pageStatus: PageStatus.error,
        error: e.toString(),
      );
      debugPrint('获取指定访问用户列表失败: $e');
    }
  }

  /// 清空用户列表
  void clearUsers() {
    state = SpecifyAccessUserState();
  }
}

/// 权限设置状态
class PermissionSettingState {
  final PageStatus pageStatus;
  final bool? result;
  final String? error;

  PermissionSettingState({
    this.pageStatus = PageStatus.initial,
    this.result,
    this.error,
  });

  PermissionSettingState copyWith({
    PageStatus? pageStatus,
    bool? result,
    String? error,
  }) {
    return PermissionSettingState(
      pageStatus: pageStatus ?? this.pageStatus,
      result: result ?? this.result,
      error: error ?? this.error,
    );
  }

  bool get isLoading => pageStatus == PageStatus.loading;
  bool get isSuccess => pageStatus == PageStatus.success;
  bool get isError => pageStatus == PageStatus.error;
}

@riverpod
class PermissionSetting extends _$PermissionSetting {
  @override
  PermissionSettingState build() {
    return PermissionSettingState();
  }

  /// 设置权限可见性
  Future<bool> setPermissionVisible({
    required int? id,
    required int taskId,
    required String shareCode,
    required int visibleType,
    List<String>? specifyAccessUsers,
  }) async {
    try {
      state = state.copyWith(pageStatus: PageStatus.loading);
      
      final entity = PermissionVisibleEntity(
        id: id,
        taskId: taskId,
        shareCode: shareCode,
        visibleType: visibleType,
        specifyAccessUsers: specifyAccessUsers,
      );
      debugPrint('权限设置请求的入参:${entity.toJson()}');
      
      final response = await QueryApi.setPermissionVisible(entity);
      debugPrint('权限设置请求的响应:${response.code} - ${response.message}');
      
      if (response.code == 200) {
        state = state.copyWith(
          pageStatus: PageStatus.success,
          result: true,
        );
        debugPrint('权限设置成功: code=${response.code}, message=${response.message}');
        return true;
      } else {
        state = state.copyWith(
          pageStatus: PageStatus.error,
          error: response.message ?? '设置失败',
        );
        debugPrint('权限设置失败: code=${response.code}, message=${response.message}');
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        pageStatus: PageStatus.error,
        error: e.toString(),
      );
      debugPrint('设置权限可见性失败: $e');
      debugPrint('错误类型: ${e.runtimeType}');
      return false;
    }
  }

  /// 重置状态
  void reset() {
    state = PermissionSettingState();
  }
} 