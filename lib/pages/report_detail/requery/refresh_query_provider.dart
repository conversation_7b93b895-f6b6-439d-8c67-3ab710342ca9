import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:get/get.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/report_detail/requery/verify_code_dialog.dart';
import 'package:zrreport/pages/history/list/provider.dart';

part 'refresh_query_provider.g.dart';

/// 重新查询状态
enum RefreshQueryStatus {
  initial,
  loading,
  success,
  needVerifyCode,
  error,
}

/// 重新查询状态管理
class RefreshQueryState {
  final RefreshQueryStatus status;
  final RefreshQueryResponse? response;
  final String? errorMessage;
  final Object? sourceProvider; // 来源 Provider 引用

  RefreshQueryState({
    this.status = RefreshQueryStatus.initial,
    this.response,
    this.errorMessage,
    this.sourceProvider,
  });

  RefreshQueryState copyWith({
    RefreshQueryStatus? status,
    RefreshQueryResponse? response,
    String? errorMessage,
    Object? sourceProvider,
  }) {
    return RefreshQueryState(
      status: status ?? this.status,
      response: response ?? this.response,
      errorMessage: errorMessage ?? this.errorMessage,
      sourceProvider: sourceProvider ?? this.sourceProvider,
    );
  }
}

@riverpod
class RefreshQuery extends _$RefreshQuery {
  @override
  RefreshQueryState build() {
    return RefreshQueryState();
  }

  /// 执行重新查询
  Future<void> executeRefreshQuery({
    required String taskId,
    required String creditCode,
    ReportDetailModel? reportDetail,
    Object? sourceProvider,
  }) async {
    try {
      debugPrint('开始执行重新查询: taskId=$taskId, creditCode=$creditCode');
      
      // 设置加载状态，同时保存来源信息
      state = state.copyWith(
        status: RefreshQueryStatus.loading,
        sourceProvider: sourceProvider,
      );
      debugPrint('状态设置为loading，sourceProvider: ${sourceProvider.runtimeType}');

      // 构建请求参数
      final entity = RefreshQueryEntity(
        taskId: taskId,
        creditCode: creditCode,
      );

      // 调用API
      debugPrint('调用重新查询API');
      final response = await QueryApi.refreshQuery(entity);

      if (response.data != null) {
        final refreshData = response.data!;
        debugPrint('API响应成功: code=${refreshData.code}, needVerifyCode=${refreshData.needVerifyCode}');
        
        // 先保存响应数据，但不设置状态为success
        state = state.copyWith(
          response: refreshData,
        );

        // 根据返回结果处理业务逻辑，在这里决定最终状态
        await _handleRefreshResult(refreshData, reportDetail);
      } else {
        debugPrint('API响应失败: response.data为null');
        final errorMessage = '重新查询失败';
        state = state.copyWith(
          status: RefreshQueryStatus.error,
          errorMessage: errorMessage,
        );
        // 显示错误提示
        Loading.toast(errorMessage);
      }
    } catch (e) {
      debugPrint('重新查询错误: $e');
      final errorMessage = e.toString();
      state = state.copyWith(
        status: RefreshQueryStatus.error,
        errorMessage: errorMessage,
      );
      // 显示错误提示
      Loading.toast(errorMessage);
    }
  }

  /// 处理重新查询结果
  Future<void> _handleRefreshResult(RefreshQueryResponse refreshData, ReportDetailModel? reportDetail) async {
    // 检查接口返回的code

    if (refreshData.needVerifyCode == false) {
      // 不需要验证码，直接设置为成功状态
      debugPrint('不需要验证码，设置状态为success');
      state = state.copyWith(
        status: RefreshQueryStatus.success,
      );
      debugPrint('状态已更新为: ${state.status}');
    } else {
      // 需要验证码，设置为需要验证码状态
      if(refreshData.code == '0'){
        debugPrint('需要验证码，设置状态为needVerifyCode');
        state = state.copyWith(status: RefreshQueryStatus.needVerifyCode);
        // 显示验证码弹框
        _showVerifyCodeDialog(reportDetail);
      }
    }
  }

  /// 触发导航和刷新（由UI层调用）
  void triggerNavigateAndRefresh(WidgetRef ref) {
    try {
      // 优先使用特定 Provider 刷新
      if (state.sourceProvider != null) {
        debugPrint('触发特定 Provider 刷新: ${state.sourceProvider.runtimeType}');
        refreshProvider(ref, state.sourceProvider!);
      } else {
        // 兜底：触发全局刷新
        debugPrint('来源信息缺失，触发全局刷新');
        refreshAllProviders(ref);
      }
      
      debugPrint('已通知刷新');
      
    } catch (e) {
      debugPrint('触发刷新失败: $e');
    }
  }

  /// 显示验证码弹框
  void _showVerifyCodeDialog(ReportDetailModel? reportDetail) {
    final context = Get.context;
    if (context == null || state.response == null) {
      debugPrint('无法显示验证码弹框：context或response为空');
      return;
    }

    final response = state.response!;
    final queryTaskVo = response.queryTaskVo;
    
    if (queryTaskVo == null) {
      debugPrint('无法显示验证码弹框：queryTaskVo为空');
      return;
    }

    // 显示验证码弹窗
    VerifyCodeDialog.show(
      context,
      defaultPhoneNumber: reportDetail?.phone ?? '',
      uuid: response.uuid,
      province: queryTaskVo.province ?? '',
      creditCode: reportDetail?.creditCode ?? '',
      taskId: reportDetail?.taskId ?? '',
      onVerifySuccess: () {
        // 验证成功后，更新状态为success
        state = state.copyWith(status: RefreshQueryStatus.success);
        debugPrint('验证码验证成功，状态已更新为success');
      },
      onCancel: () {
        // 取消验证码输入，重置状态
        reset();
      },
    );
  }

  /// 重置状态
  void reset() {
    state = RefreshQueryState();
  }
} 