// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'refresh_query_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$refreshQueryHash() => r'c9b998db5d53b8fa533c027c4f6e99807f1a4bd5';

/// See also [RefreshQuery].
@ProviderFor(RefreshQuery)
final refreshQueryProvider =
    AutoDisposeNotifierProvider<RefreshQuery, RefreshQueryState>.internal(
  RefreshQuery.new,
  name: r'refreshQueryProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$refreshQueryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RefreshQuery = AutoDisposeNotifier<RefreshQueryState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
