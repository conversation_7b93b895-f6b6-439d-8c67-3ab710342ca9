import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/report_detail/detail/provider.dart';
import 'package:zrreport/pages/report_detail/requery/refresh_query_provider.dart';
import 'package:get/get.dart';

/// 重新查询提示组件
class RefreshNoticeWidget extends ConsumerWidget {
  final String shareCode;
  final VoidCallback? onRefreshTap;
  final String? message;
  final String? buttonText;
  final Object? sourceProvider; // 来源 Provider 引用

  const RefreshNoticeWidget({
    super.key,
    required this.shareCode,
    this.onRefreshTap,
    this.message,
    this.buttonText,
    this.sourceProvider,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听报告详情数据
    final reportDetailAsync = ref.watch(getReportDetailProvider(shareCode));
    // 监听重新查询状态
    final refreshQueryState = ref.watch(refreshQueryProvider);
    
    // 监听重新查询状态变化，处理成功后的导航和刷新
    ref.listen<RefreshQueryState>(refreshQueryProvider, (previous, next) {
      debugPrint('RefreshNoticeWidget: 状态变化 ${previous?.status} -> ${next.status}');
      if (next.status == RefreshQueryStatus.success && 
          previous?.status != RefreshQueryStatus.success) {
        debugPrint('RefreshNoticeWidget: 检测到成功状态，触发导航和刷新');
        // 状态变为成功，触发刷新并返回上级页面
        _handleSuccessNavigation(ref);
      } else {
        debugPrint('RefreshNoticeWidget: 不满足导航条件 - next.status=${next.status}, previous?.status=${previous?.status}');
      }
    });
    
    return reportDetailAsync.when(
      data: (response) {
        final reportDetail = response.data;
        
        // 判断是否需要显示组件
        // 条件：dateStatus为false 且 source不为空且不为"common"
        if (reportDetail != null && 
            !reportDetail.dateStatus &&
            reportDetail.source.isNotEmpty &&
            reportDetail.source != "common") {
          return _buildNoticeWidget(context, ref, reportDetail, refreshQueryState);
        }
        
        return const SizedBox.shrink();
      },
      loading: () => const SizedBox.shrink(),
      error: (error, stack) => const SizedBox.shrink(),
    );
  }

  Widget _buildNoticeWidget(
    BuildContext context, 
    WidgetRef ref, 
    ReportDetailModel reportDetail, 
    RefreshQueryState refreshState
  ) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: const Color(0xFFF7F8FA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              message ?? '您正在查看的历史数据可能有更新，建议重新查询以获取最新信息',
              style: TextStyle(
                fontSize: 13,
                color: AppColors.textColor6,
                height: 1.4,
              ),
            ),
          ),
          const SizedBox(width: 12),
          GestureDetector(
            onTap: refreshState.status == RefreshQueryStatus.loading 
                ? null 
                : () => _handleRefreshTap(ref, reportDetail),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: refreshState.status == RefreshQueryStatus.loading 
                    ? AppColors.textColor9 
                    : AppColors.primary,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (refreshState.status == RefreshQueryStatus.loading) ...[
                    SizedBox(
                      width: 12,
                      height: 12,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                    const SizedBox(width: 6),
                  ],
                  Text(
                    refreshState.status == RefreshQueryStatus.loading 
                        ? '查询中...' 
                        : (buttonText ?? '重新查询'),
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 处理重新查询按钮点击
  void _handleRefreshTap(WidgetRef ref, ReportDetailModel reportDetail) {
    if (onRefreshTap != null) {
      onRefreshTap!();
    } else {
      // 检查必要参数是否有效
      final taskId = reportDetail.taskId;
      final creditCode = reportDetail.creditCode;
      
      if (taskId.isEmpty || creditCode.isEmpty) {
        return;
      }
      
      // 执行重新查询 - 所有业务逻辑都在Provider中处理
      ref.read(refreshQueryProvider.notifier).executeRefreshQuery(
        taskId: taskId,
        creditCode: creditCode,
        reportDetail: reportDetail,
        sourceProvider: sourceProvider,
      );
    }
  }

  /// 处理成功后的导航和刷新
  void _handleSuccessNavigation(WidgetRef ref) {
    try {
      ref.read(refreshQueryProvider.notifier).triggerNavigateAndRefresh(ref);
      Get.back();
      Loading.toast('重新查询成功');

    } catch (e) {
      debugPrint('处理成功导航失败: $e');
      // 简单的错误处理：直接返回上级页面
      Get.back();
    }
  }
} 