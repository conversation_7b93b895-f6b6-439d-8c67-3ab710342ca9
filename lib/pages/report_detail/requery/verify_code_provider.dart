import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';

part 'verify_code_provider.g.dart';

/// 验证码状态
enum VerifyCodeStatus {
  initial,
  sendingCode,
  codeSent,
  verifying,
  verifySuccess,
  error,
}

/// 验证码状态管理
class VerifyCodeState {
  final VerifyCodeStatus status;
  final bool isSendingCode;
  final bool isVerifying;
  final int countdown;
  final String? errorMessage;
  final String phoneNumber;
  final String verifyCode;

  VerifyCodeState({
    this.status = VerifyCodeStatus.initial,
    this.isSendingCode = false,
    this.isVerifying = false,
    this.countdown = 0,
    this.errorMessage,
    this.phoneNumber = '',
    this.verifyCode = '',
  });

  VerifyCodeState copyWith({
    VerifyCodeStatus? status,
    bool? isSendingCode,
    bool? isVerifying,
    int? countdown,
    String? errorMessage,
    String? phoneNumber,
    String? verifyCode,
  }) {
    return VerifyCodeState(
      status: status ?? this.status,
      isSendingCode: isSendingCode ?? this.isSendingCode,
      isVerifying: isVerifying ?? this.isVerifying,
      countdown: countdown ?? this.countdown,
      errorMessage: errorMessage ?? this.errorMessage,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      verifyCode: verifyCode ?? this.verifyCode,
    );
  }

  /// 是否可以发送验证码
  bool get canSendCode => countdown == 0 && !isSendingCode;

  /// 是否可以验证
  bool get canVerify => phoneNumber.length == 11 && verifyCode.length > 1 && !isVerifying;
}

@riverpod
class VerifyCode extends _$VerifyCode {
  @override
  VerifyCodeState build() {
    return VerifyCodeState();
  }

  /// 更新手机号
  void updatePhoneNumber(String phoneNumber) {
    state = state.copyWith(phoneNumber: phoneNumber);
  }

  /// 更新验证码
  void updateVerifyCode(String verifyCode) {
    state = state.copyWith(verifyCode: verifyCode);
  }

  /// 发送验证码
  Future<void> sendVerifyCode({
    required String uuid,
    required String province,
    required String creditCode,
  }) async {
    if (!state.canSendCode) return;

    final phoneNumber = state.phoneNumber.trim();
    if (phoneNumber.isEmpty) {
      _setError('请输入手机号');
      return;
    }

    if (phoneNumber.length != 11) {
      _setError('请输入正确的手机号');
      return;
    }

    state = state.copyWith(
      status: VerifyCodeStatus.sendingCode,
      isSendingCode: true,
      errorMessage: null,
    );

    try {
      final entity = AppSendSmsEntity(
        province: province,
        creditCode: creditCode,
        uuid: uuid,
        account: phoneNumber,
      );

      final response = await QueryApi.appSendSms(entity);
      
      if (response.code == 200) {
        state = state.copyWith(
          status: VerifyCodeStatus.codeSent,
          isSendingCode: false,
        );
        _startCountdown();
        Loading.toast(response.message);
      } else {
        _setError(response.message ?? '发送验证码失败');
        // Loading.toast(response.message);
      }
    } catch (e) {
      debugPrint('发送验证码错误: $e');
      _setError('发送验证码失败');
    }
  }

  /// 验证验证码
  Future<bool> verifyCode({
    required String uuid,
    required String creditCode,
    required String taskId,
  }) async {
    if (!state.canVerify) return false;

    state = state.copyWith(
      status: VerifyCodeStatus.verifying,
      isVerifying: true,
      errorMessage: null,
    );

    try {
      final entity = AppVerifySmsEntity(
        taskId: taskId,
        creditCode: creditCode,
        uuid: uuid,
        account: state.phoneNumber,
        code: state.verifyCode,
      );

      final response = await QueryApi.appVerifySms(entity).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          throw Exception('发送超时，请重试');
        },
      );
      
      if (response.code == 200) {
        state = state.copyWith(
          status: VerifyCodeStatus.verifySuccess,
          isVerifying: false,
        );
        Loading.toast(response.message);
        return true;
      } else {
        _setError(response.message ?? '验证失败');
         // Loading.toast(response.message);
        return false;
      }
    } catch (e) {
      debugPrint('验证码验证错误: $e');
      _setError('验证失败');
      return false;
    }
  }

  /// 开始倒计时
  void _startCountdown() {
    state = state.copyWith(countdown: 60);
    
    Future.doWhile(() async {
      await Future.delayed(const Duration(seconds: 1));
      if (state.countdown > 0) {
        state = state.copyWith(countdown: state.countdown - 1);
        return true;
      }
      return false;
    });
  }

  /// 设置错误状态
  void _setError(String message) {
    state = state.copyWith(
      status: VerifyCodeStatus.error,
      isSendingCode: false,
      isVerifying: false,
      errorMessage: message,
    );
    Loading.toast(message);
  }

  /// 重置状态
  void reset() {
    state = VerifyCodeState();
  }
} 