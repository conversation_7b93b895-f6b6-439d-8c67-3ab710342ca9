import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/report_detail/requery/verify_code_provider.dart';

/// 验证码弹窗
class VerifyCodeDialog extends ConsumerStatefulWidget {
  final String defaultPhoneNumber;
  final String uuid; // 来自 /tax/app/loginSms 接口
  final String province; // 来自 /tax/app/loginSms 接口
  final String creditCode; // 来自 tax/share/getShareReport 接口
  final String taskId; // 来自 tax/share/getShareReport 接口
  final VoidCallback? onVerifySuccess;
  final VoidCallback? onCancel;

  const VerifyCodeDialog({
    super.key,
    required this.defaultPhoneNumber,
    required this.uuid,
    required this.province,
    required this.creditCode,
    required this.taskId,
    this.onVerifySuccess,
    this.onCancel,
  });

  @override
  ConsumerState<VerifyCodeDialog> createState() => _VerifyCodeDialogState();

  /// 显示验证码弹窗
  static Future<void> show(
    BuildContext context, {
    required String defaultPhoneNumber,
    required String uuid,
    required String province,
    required String creditCode,
    required String taskId,
    VoidCallback? onVerifySuccess,
    VoidCallback? onCancel,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => VerifyCodeDialog(
        defaultPhoneNumber: defaultPhoneNumber,
        uuid: uuid,
        province: province,
        creditCode: creditCode,
        taskId: taskId,
        onVerifySuccess: onVerifySuccess,
        onCancel: onCancel,
      ),
    );
  }
}

class _VerifyCodeDialogState extends ConsumerState<VerifyCodeDialog> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _codeController = TextEditingController();
  final FocusNode _phoneFocusNode = FocusNode();
  final FocusNode _codeFocusNode = FocusNode();
  
  // 焦点状态管理
  int _currentFocusIndex = -1; // -1: 无焦点, 0: 手机号, 1: 验证码
  // 输入内容状态管理
  bool _hasPhoneContent = false;
  bool _hasCodeContent = false;

  @override
  void initState() {
    super.initState();
    // 默认填充手机号并更新到Provider
    _phoneController.text = widget.defaultPhoneNumber;
    _hasPhoneContent = widget.defaultPhoneNumber.isNotEmpty;
    
    // 添加焦点监听器
    _phoneFocusNode.addListener(_onPhoneFocusChange);
    _codeFocusNode.addListener(_onCodeFocusChange);
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(verifyCodeProvider.notifier).updatePhoneNumber(widget.defaultPhoneNumber);
    });
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _codeController.dispose();
    _phoneFocusNode.dispose();
    _codeFocusNode.dispose();
    super.dispose();
  }

  void _onPhoneFocusChange() {
    setState(() {
      if (_phoneFocusNode.hasFocus) {
        _currentFocusIndex = 0;
      } else if (_currentFocusIndex == 0) {
        _currentFocusIndex = -1;
        // 失去焦点时校验手机号
        _validatePhoneNumber();
      }
    });
  }

  void _onCodeFocusChange() {
    setState(() {
      if (_codeFocusNode.hasFocus) {
        _currentFocusIndex = 1;
      } else if (_currentFocusIndex == 1) {
        _currentFocusIndex = -1;
      }
    });
  }

  /// 校验手机号格式
  void _validatePhoneNumber() {
    final String value = _phoneController.text.trim();
    
    // 如果输入为空，不做验证
    if (value.isEmpty) {
      return;
    }
    
    // 检查手机号长度
    if (value.length != 11) {
      Loading.toast('手机号码错误！');
      return;
    }
    
    // 中国大陆手机号正则表达式
    final RegExp phoneRegExp = RegExp(r'^1[3-9]\d{9}$');
    final bool isValid = phoneRegExp.hasMatch(value);
    
    // 如果手机号格式无效，显示Toast提示
    if (!isValid) {
      Loading.toast('手机号码错误！');
      return;
    }
  }

  @override
  Widget build(BuildContext context) {
    // 监听验证成功状态
    ref.listen<VerifyCodeState>(verifyCodeProvider, (previous, next) {
      if (next.status == VerifyCodeStatus.verifySuccess) {
        Navigator.of(context).pop();
        widget.onVerifySuccess?.call();
        // 通过回调通知验证成功，让外部处理状态更新
      }
    });

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        width: 300,
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 手机号输入区域
            _buildPhoneInputArea(),
            const SizedBox(height: 20),
            
            // 验证码输入区域
            _buildCodeInputArea(),
            const SizedBox(height: 30),
            
            // 按钮区域
            _buildButtonArea(),
          ],
        ),
      ),
    );
  }

  /// 构建手机号输入区域
  Widget _buildPhoneInputArea() {
    return Container(
      height: 44,
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextField(
        controller: _phoneController,
        focusNode: _phoneFocusNode,
        keyboardType: TextInputType.phone,
        maxLength: 11,
        textAlign: TextAlign.left,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppColors.textColor1,
        ),
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
          LengthLimitingTextInputFormatter(11),
        ],
        onChanged: (value) {
          // 根据是否有内容更新状态
          final bool hasContent = value.isNotEmpty;
          if (hasContent != _hasPhoneContent) {
            setState(() {
              _hasPhoneContent = hasContent;
            });
          }
          ref.read(verifyCodeProvider.notifier).updatePhoneNumber(value);
        },
        decoration: InputDecoration(
          hintText: '请输入手机号',
          hintStyle: TextStyle(
            color: AppColors.textColor9,
            fontSize: 16,
          ),
          border: InputBorder.none,
          counterText: '',
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 12,
          ),
          // 控制图标大小约束，防止影响输入框高度
          suffixIconConstraints: const BoxConstraints(
            minWidth: 32,
            minHeight: 32,
          ),
          // 只有当输入框有内容且拥有焦点时才显示删除按钮
          suffixIcon: (_hasPhoneContent && _currentFocusIndex == 0)
              ? Padding(
                  padding: const EdgeInsets.only(right: 10),
                  child: GestureDetector(
                    onTap: () {
                      // 清空输入框并更新状态
                      _phoneController.clear();
                      setState(() {
                        _hasPhoneContent = false;
                      });
                      ref.read(verifyCodeProvider.notifier).updatePhoneNumber('');
                    },
                    child: Container(
                      width: 14,
                      height: 14,
                      decoration: const BoxDecoration(
                        color: Color(0xFFD9D9D9),
                        shape: BoxShape.circle,
                      ),
                      child: const Center(
                        child: Icon(
                          Icons.close,
                          size: 10,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                )
              : const SizedBox(width: 14, height: 14), // 占位，保持高度一致
        ),
      ),
    );
  }

  /// 构建验证码输入区域
  Widget _buildCodeInputArea() {
    return Row(
      children: [
        // 验证码输入框
        Expanded(
          child: Container(
            height: 44,
            decoration: BoxDecoration(
              color: const Color(0xFFF5F5F5),
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextField(
              controller: _codeController,
              focusNode: _codeFocusNode,
              keyboardType: TextInputType.number,
              maxLength: 11,
              textAlign: TextAlign.left,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: AppColors.textColor1,
              ),
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(11),
              ],
              onChanged: (value) {
                // 根据是否有内容更新状态
                final bool hasContent = value.isNotEmpty;
                if (hasContent != _hasCodeContent) {
                  setState(() {
                    _hasCodeContent = hasContent;
                  });
                }
                ref.read(verifyCodeProvider.notifier).updateVerifyCode(value);
              },
              decoration: InputDecoration(
                hintText: '',
                hintStyle: TextStyle(
                  color: AppColors.textColor9,
                  fontSize: 16,
                ),
                border: InputBorder.none,
                counterText: '',
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 12,
                ),
                // 控制图标大小约束，防止影响输入框高度
                suffixIconConstraints: const BoxConstraints(
                  minWidth: 32,
                  minHeight: 32,
                ),
                // 只有当输入框有内容且拥有焦点时才显示删除按钮
                suffixIcon: (_hasCodeContent && _currentFocusIndex == 1)
                    ? Padding(
                        padding: const EdgeInsets.only(right: 10),
                        child: GestureDetector(
                          onTap: () {
                            // 清空输入框并更新状态
                            _codeController.clear();
                            setState(() {
                              _hasCodeContent = false;
                            });
                            ref.read(verifyCodeProvider.notifier).updateVerifyCode('');
                          },
                          child: Container(
                            width: 14,
                            height: 14,
                            decoration: const BoxDecoration(
                              color: Color(0xFFD9D9D9),
                              shape: BoxShape.circle,
                            ),
                            child: const Center(
                              child: Icon(
                                Icons.close,
                                size: 10,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      )
                    : const SizedBox(width: 14, height: 14), // 占位，保持高度一致
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        
        // 获取验证码按钮
        _buildGetCodeButton(),
      ],
    );
  }

  /// 构建获取验证码按钮
  Widget _buildGetCodeButton() {
    return Consumer(
      builder: (context, ref, child) {
        final state = ref.watch(verifyCodeProvider);
        
        return GestureDetector(
          onTap: state.canSendCode ? _sendVerifyCode : null,
          child: Container(
            height: 44,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color:
                  state.canSendCode ? AppColors.primary : AppColors.textColor9,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: state.isSendingCode
                  ? SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      state.countdown > 0 ? '${state.countdown}s后重发' : '获取验证码',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
            ),
          ),
        );
      },
    );
  }

  /// 构建按钮区域
  Widget _buildButtonArea() {
    return Consumer(
      builder: (context, ref, child) {
        final state = ref.watch(verifyCodeProvider);
        
        return Row(
          children: [
            // 取消按钮
            Expanded(
              child: GestureDetector(
                onTap: state.isVerifying ? null : _onCancel,
                child: Container(
                  height: 44,
                  decoration: BoxDecoration(
                    color: AppColors.disableBackground,
                    border: Border.all(color: AppColors.textColor9),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Text(
                      '取消',
                      style: TextStyle(
                        fontSize: 16,
                        color: AppColors.textColor9,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            
            // 确定按钮
            Expanded(
              child: GestureDetector(
                onTap: state.canVerify && !state.isVerifying ? _onConfirm : null,
                child: Container(
                  height: 44,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: state.isVerifying
                        ? SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            '确定',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 发送验证码
  Future<void> _sendVerifyCode() async {
    await ref.read(verifyCodeProvider.notifier).sendVerifyCode(
      uuid: widget.uuid,
      province: widget.province,
      creditCode: widget.creditCode,
    );
  }

  /// 确定按钮点击
  Future<void> _onConfirm() async {
    await ref.read(verifyCodeProvider.notifier).verifyCode(
      uuid: widget.uuid,
      creditCode: widget.creditCode,
      taskId: widget.taskId,
    );
  }

  /// 取消按钮点击
  void _onCancel() {
    Navigator.of(context).pop();
    widget.onCancel?.call();
  }
} 