// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'paid_taxes_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$paidTaxInfoHash() => r'a7937e99f07c51cc82fc2bde47d3bc309b3211a0';

/// See also [PaidTaxInfo].
@ProviderFor(PaidTaxInfo)
final paidTaxInfoProvider =
    AutoDisposeNotifierProvider<PaidTaxInfo, PaidTaxesInfoState>.internal(
  PaidTaxInfo.new,
  name: r'paidTaxInfoProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$paidTaxInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PaidTaxInfo = AutoDisposeNotifier<PaidTaxesInfoState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
