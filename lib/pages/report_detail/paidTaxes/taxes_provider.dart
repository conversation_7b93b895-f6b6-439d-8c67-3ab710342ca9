import 'dart:convert';

import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/common/models/taxes_info.dart';
import 'package:zrreport/pages/report_detail/example_info.dart';
import 'package:zrreport/pages/report_detail/widget/listItem.dart';

part 'taxes_provider.g.dart';

class TaxInfoInfoState {
  var pageStatus = PageStatus.initial;
  TaxesInfo? taxesInfo;

  TaxInfoInfoState copyWith(
      {required PageStatus pageStatus, TaxesInfo? model}) {
    return TaxInfoInfoState()
      ..pageStatus = pageStatus
      ..taxesInfo = model ?? this.taxesInfo;
  }

  List<ListItemViewData> get taxInfoHeadList {
    List<ListItemViewData> res = [
      ListItemViewData(
          dark: true,
          title: '纳税信用等级',
          value: taxesInfo!.evaluationResult ?? '_'),
      ListItemViewData(
          dark: false, title: '纳税人种类', value: taxesInfo!.qualification ?? '_'),
      ListItemViewData(
        dark: true,
        title: '纳税人种类生效日期',
        value: taxesInfo!.qualificationValidFrom ?? '_',
      ),
      ListItemViewData(
        dark: false,
        title: '税务登记日期',
        value: taxesInfo!.taxRegister ?? '_',
      ),
      ListItemViewData(
        dark: true,
        title: '历史有无欠税记录',
        value: taxesInfo!.unpaidTaxStatus == true ? '有' : '无',
      ),
      ListItemViewData(
        dark: false,
        title: '近1年欠税记录数',
        value: taxesInfo!.taxDelinquencyRecordCountPast12Months != null
            ? taxesInfo!.taxDelinquencyRecordCountPast12Months.toString()
            : '_',
      ),
      ListItemViewData(
        dark: true,
        title: '近1年最大连续0纳税申报月数',
        value: taxesInfo!.last12MaxZeroTaxMonths != null
            ? taxesInfo!.last12MaxZeroTaxMonths.toString()
            : '_',
      ),
      ListItemViewData(
        dark: false,
        title: '滞纳金情况',
        value: taxesInfo!.last12MaxZeroTaxMonths != null ? '无' : '_',
      ),
      ListItemViewData(
        dark: true,
        title: '当前欠税金额(元)',
        value: taxesInfo!.unpaidTaxAmount != null
            ? taxesInfo!.unpaidTaxAmount.toString()
            : '_',
      ),
    ];
    return res;
  }

  // 纳税数据分析
  bool get hasEnterpriseMonthPaidList =>
      taxesInfo!.enterpriseMonthPaidList != null &&
      taxesInfo!.enterpriseMonthPaidList!.isNotEmpty;

  // 表头
  List<String> get enterpriseMonthPaidListTableHeadData {
    List<String> res = ['纳税数据分析'];
    for (var value in taxesInfo!.enterpriseMonthPaidList!) {
      res.add('近${value.month ?? '_'}个月');
    }
    return res;
  }

// 表内容
  List<List<String>> get enterpriseMonthPaidListTableData {
    List<List<String>> res = [];
    int cols = taxesInfo!.enterpriseMonthPaidList!.length;
    const titles = [
      '欠税金额',
      '纳税销售额',
      '纳税实缴金额',
      '0申报月数',
      '负债率',
      '利润率',
      '增值税应纳额',
      '企业所得税',
      '减免税额',
      '滞纳金次数',
      '滞纳金金额'
    ];
    int rows = titles.length;
    for (var i = 0; i < rows; i++) {
      List<String> inner = [];
      for (var j = 0; j < cols + 1; j++) {
        if (j == 0) {
          inner.add(titles[i]);
        } else {
          final itemData = taxesInfo!.enterpriseMonthPaidList![j - 1];
          if (i == 0) {
            // 欠税金额
            if (itemData.month != null) {
              inner.add(itemData.taxEnterprisePaidDate!.unpaidTaxAmount != null
                  ? formatLargeNumber(itemData
                      .taxEnterprisePaidDate!.unpaidTaxAmount!
                      .toDouble())
                  : '_');
            } else {
              inner.add('_');
            }
          } else if (i == 1) {
            // 纳税销售额
            if (itemData.taxEnterprisePaidDate != null) {
              inner.add(
                  '${itemData.taxEnterprisePaidDate!.taxSaleAmount ?? '-'}%');
            } else {
              inner.add('-%');
            }
          } else if (i == 2) {
            // 纳税实缴金额
            if (itemData.taxEnterprisePaidDate != null) {
              inner.add(
                  '${itemData.taxEnterprisePaidDate!.taxPaidAmount ?? '-'}%');
            } else {
              inner.add('-%');
            }
          } else if (i == 3) {
            // 0申报月数
            if (itemData.taxEnterprisePaidDate != null) {
              inner.add(
                  '${itemData.taxEnterprisePaidDate!.coiled0Months ?? '_'}');
            } else {
              inner.add('_');
            }
          } else if (i == 4) {
            // 负债率
            if (itemData.taxEnterprisePaidDate != null) {
              inner.add(itemData.taxEnterprisePaidDate!.debtRatio ?? '_');
            } else {
              inner.add('_');
            }
          } else if (i == 5) {
            // 利润率
            if (itemData.taxEnterprisePaidDate != null) {
              inner.add(
                  '${itemData.taxEnterprisePaidDate!.profitRatio ?? '-'}%');
            } else {
              inner.add('-%');
            }
          } else if (i == 6) {
            // 增值税应纳额
            if (itemData.taxEnterprisePaidDate != null) {
              inner.add(
                  '${itemData.taxEnterprisePaidDate!.vatPayableAmount ?? '-'}%');
            } else {
              inner.add('-%');
            }
          } else if (i == 7) {
            // 企业所得税
            if (itemData.taxEnterprisePaidDate != null) {
              inner.add(
                  '${itemData.taxEnterprisePaidDate!.taxIncomeAmount ?? '_'}');
            } else {
              inner.add('_');
            }
          } else if (i == 8) {
            // 减免税额
            if (itemData.taxEnterprisePaidDate != null) {
              inner.add(
                  '${itemData.taxEnterprisePaidDate!.taxWaiverAmount ?? '_'}');
            } else {
              inner.add('_');
            }
          } else if (i == 9) {
            // 滞纳金次数
            if (itemData.taxEnterprisePaidDate != null) {
              inner
                  .add('${itemData.taxEnterprisePaidDate!.lateFeesNum ?? '_'}');
            } else {
              inner.add('_');
            }
          } else {
            // 滞纳金金额
            if (itemData.taxEnterprisePaidDate != null) {
              inner.add(
                  '${itemData.taxEnterprisePaidDate!.lateFeesAmount ?? '_'}');
            } else {
              inner.add('_');
            }
          }
        }
      }
      res.add(inner);
    }
    return res;
  }

  /// 滞纳金情况
  bool get hasTaxLateFeesList =>
      taxesInfo!.taxLateFeesList != null &&
      taxesInfo!.taxLateFeesList!.isNotEmpty;

  /// 表头
  List<String> get taxLateFeesListTableHeadData {
    return ['起始时间', '缴款时间', '滞纳金金额'];
  }

  /// 表内容
  List<List<String>> taxLateFeesListTableData() {
    final feesList = taxesInfo!.taxLateFeesList;
    List<List<String>> res = [];
    int cols = 3;
    int rows = feesList!.length;

    for (var i = 0; i < rows; i++) {
      List<String> inner = [];
       final itemData = feesList[i];
      for (var j = 0; j < cols + 1; j++) {
        if (j == 0) {
          inner.add(itemData.taxPeriodStart ?? '_');
        } else if (j == 1) {
          inner.add(itemData.paymentDate ?? '_');
        } else {
          inner.add('${itemData.paymentAmount}');
        }
      }
      res.add(inner);
    }
    return res;
  }


  /// 纳税信用评级
   bool get hasCreditEvaluationList =>
      taxesInfo!.creditEvaluationList != null &&
      taxesInfo!.creditEvaluationList!.isNotEmpty;

 /// 表头
  List<String> get creditEvaluationListTableHeadData {
    return ['评价年度', '评价得分', '评价结果', '扣减分项'];
  }

   /// 表内容
  List<List<String>> creditEvaluationListTableData() {
    final creditList = taxesInfo!.creditEvaluationList;
    List<List<String>> res = [];
    int cols = 4;
    int rows = creditList!.length;

    for (var i = 0; i < rows; i++) {
      List<String> inner = [];
       final itemData = creditList[i];
      for (var j = 0; j < cols + 1; j++) {
        if (j == 0) {
          inner.add(itemData.evaluationYear ?? '_');
        } else if (j == 1) {
           if (itemData.evaluationScore != null) {
              inner.add(
                  '${itemData.evaluationScore}');
            } else {
              inner.add('_');
            }
        } else if (j == 2) {
          inner.add(itemData.evaluationResult ?? '_');
        } else {
           if (itemData.indicatorNames != null && itemData.indicatorNames!.isNotEmpty) {
              inner.add(
                  '${itemData.indicatorNames!.join(',')}');
            } else {
              inner.add('_');
            }
        }
      }
      res.add(inner);
    }
    return res;
  }


   /// 税务违规信息
   bool get hasLawRegulationViolationVoList => 
      taxesInfo!.lawRegulationViolationVoList != null &&
      taxesInfo!.lawRegulationViolationVoList!.isNotEmpty;

/// 表头
  List<String> get lawRegulationViolationVoListTableHeadData {
     List<String> res = ['违法行为名称'];
    for (var value in taxesInfo!.lawRegulationViolationVoList!) {
      res.add(value.actionName ?? '_');
    }
    return res;
  }

   /// 表内容
  List<List<String>> lawRegulationViolationVoListTableData() {
    final lawList = taxesInfo!.lawRegulationViolationVoList;
    List<List<String>> res = [];
    int cols = 1 + lawList!.length;
    const titles = [
      '税收违法手段',
      '税务局登记日期',
      '违法违章所属期起',
      '违法违章所属期止',
      '处理状态',
      '案件状态',
      '违法事实',
      '是否社保违法'
    ];
    int rows = titles.length;

     for (var i = 0; i < rows; i++) {
      List<String> inner = [];
      for (var j = 0; j < cols; j++) {
        if (j == 0) {
          inner.add(titles[i]);
        } else {
          final itemData = taxesInfo!.lawRegulationViolationVoList![j - 1];
          if (i == 0) {
            // 税收违法手段
            inner.add(itemData.infractionMeans ?? '_');
          } else if (i == 1) {
            // 税务局登记日期
            inner.add(itemData.registerDate ?? '_');
          } else if (i == 2) {
            // 违法违章所属期起
            inner.add(itemData.taxPeriodStart ?? '_');
          } else if (i == 3) {
            // 违法违章所属期止
            inner.add(itemData.taxPeriodEnd ?? '_');
          } else if (i == 4) {
            // 处理状态
            inner.add(itemData.processState ?? '_');
          } else if (i == 5) {
            // 案件状态
            inner.add(itemData.caseState ?? '_');
          } else if (i == 6) {
            // 违法事实
            inner.add(itemData.illegalFact ?? '_');
          } else {
            // 滞纳金金额
            inner.add(itemData.isSocialSecurityViolation ?? '_');
          }
        }
      }
      res.add(inner);
    }
    return res;
  }
 


  /// 财务情况
   bool get hasSpiderFinanceInfos => 
      taxesInfo!.spiderFinanceInfosYearList != null &&
      taxesInfo!.spiderFinanceInfosYearList!.isNotEmpty;

/// 表头
  List<String> get spiderFinanceInfosYearListTableHeadData {
     List<String> res = ['财务指标'];
    for (var value in taxesInfo!.spiderFinanceInfosYearList!) {
      res.add('${value.year ?? '_'}年');
    }
    return res;
  }

   /// 表内容
  List<List<String>> spiderFinanceInfosYearListTableData() {
    final spiderList = taxesInfo!.spiderFinanceInfosYearList;
    List<List<String>> res = [];
    int cols = spiderList!.length;
    const titles = [
      '资产合计',
      '负债合计',
      '所有者权益合计',
      '负债和所有权益总计',
      '营业收入',
      '营业利润',
      '利润总额',
      '净利润',
      '现金净增加额',
      '期末现金余额'
    ];
    int rows = titles.length;

     for (var i = 0; i < rows; i++) {
      List<String> inner = [];
      for (var j = 0; j < cols + 1; j++) {
        if (j == 0) {
          inner.add(titles[i]);
        } else {
          final itemData = spiderList![j - 1].spiderFinanceInfoVo;
          if (i == 0) {
            // 资产合计
            inner.add(itemData?.totalAssets ?? '_');
          } else if (i == 1) {
            // 负债合计
            inner.add(itemData?.totalLiabilities ?? '_');
          } else if (i == 2) {
            // 所有者权益合计
            inner.add(itemData?.ownerEquity ?? '_');
          } else if (i == 3) {
            // 负债和所有权益总计
            inner.add(itemData?.liabilitiesOwnerEquity ?? '_');
          } else if (i == 4) {
            // 营业收入
            inner.add(itemData?.businessIncome ?? '_');
          } else if (i == 5) {
            // 营业利润
            inner.add(itemData?.businessProfit ?? '_');
          } else if (i == 6) {
            // 利润总额
            inner.add(itemData?.totalProfit ?? '_');
          } else if (i == 7) {
            // 净利润
            inner.add(itemData?.netProfits ?? '_');
          } else if (i == 8) {
            // 现金净增加额
            inner.add(itemData?.thisYearNetIncrease ?? '_');
          } 
           else {
            // 期末现金余额
            inner.add(itemData?.thisYearFinalBalance ?? '_');
          }
        }
      }
      res.add(inner);
    }
    return res;
  }
}

@riverpod
class TaxInfo extends _$TaxInfo {
  @override
  TaxInfoInfoState build() {
    return TaxInfoInfoState();
  }

  Future<void> getTaxesDetail(String shareCode, {bool isExample = false}) async {
    if (isExample) {
      state = await state.copyWith(pageStatus: PageStatus.success, model: example_TaxListModle);
    } else {
      try {
      final response = await QueryApi.taxesDetail(shareCode);
      if (response.data != null) {
        state = state.copyWith(
            pageStatus: PageStatus.success, model: response.data);
      } else {
        state = state.copyWith(pageStatus: PageStatus.empty);
      }
    } catch (e) {
      state = state.copyWith(pageStatus: PageStatus.error);
      debugPrint('${e}');
    }
    }
  }
}




