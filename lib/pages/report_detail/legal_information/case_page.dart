import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:zrreport/common/index.dart';

import 'case_page_provider.dart';

class ReportCasePageView extends BasePage {
  final String shareCode;
  final bool isExample;
  const ReportCasePageView(
      {super.key, required this.shareCode, this.isExample = false});

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ProviderScope.containerOf(context)
          .read(casePageInfoProvider.notifier)
          .getDetail(shareCode, isExample: isExample);
    });

    return Consumer(
      builder: (context, ref, child) {
        final detailProvider = ref.watch(casePageInfoProvider);
        return detailProvider.model != null
            ? Container(
                child: _contentView(detailProvider.model!),
              )
            : LoadingWidget();
      },
    );
  }

  Widget _contentView(EnterpriseCaseModel model) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 基本信息
          _buildSectionHeader('工商司法'),
          SizedBox(height: 12),
          _buildBasicInfoSection(model),

          SizedBox(height: 15),

          _buildSectionHeader('工商变更信息'),
          SizedBox(height: 12),
          _buildChangeInfoSection(),

          SizedBox(height: 15),
          _buildSectionHeader('判决文书'),
          SizedBox(height: 12),
          _buildCaseInfoSection(),
        ],
      ),
    );
  }

  Widget _buildBasicInfoSection(EnterpriseCaseModel model) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            cell(
                label: '失信被执行条数',
                value: model.enterpriseDishonestSum.toString(),
                isOdd: true),
            cell(
                label: '被执行条数',
                value: model.executedPersonSum.toString(),
                isOdd: false),
          ],
        ),
        Row(
          children: [
            cell(
                label: '法院公告条数',
                value: model.courtNoticeSum.toString(),
                isOdd: true),
            cell(
                label: '判决文书条数',
                value: model.enterpriseCaseSum.toString(),
                isOdd: false),
          ],
        ),
        Row(
          children: [
            cell(
                label: '总涉案金额', //formatLargeNumber
                value: model.subjectAmount ?? ""),
            cell(
                label: '近6月是否有法人变更',
                value: (model.changeStatus ?? false) ? "是" : "否",
                isOdd: false),
          ],
        ),
      ],
    );
  }

  Widget cell(
      {required String label, required String value, bool isOdd = false}) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
              alignment: Alignment.centerLeft,
              padding: EdgeInsets.only(left: 16),
              color: Color(0xFFF1F5F9),
              height: 36,
              child: Text(
                label,
                style: TextStyle(color: AppColors.textColor9),
              )),
          Container(
              alignment: Alignment.centerLeft,
              padding: EdgeInsets.only(left: 16),
              height: 36,
              child: Text(
                value,
                style: TextStyle(color: Color(0xFF222222)),
              )),
        ],
      ),
    );
  }

  Widget _buildChangeInfoSection() {
    return Consumer(builder: (context, ref, child) {
      final state = ref.watch(casePageInfoProvider);
      return SfDataGrid(
        verticalScrollPhysics: NeverScrollableScrollPhysics(),
        source: DynamicDataSource(
          data: state.enterpriseChangeListTableData,
          columns: state.changeHeaders,
          cellBuilder: (context, cell, index) {
            return Container(
              alignment: Alignment.center,
              child: Text(
                cell.value ?? '',
              ),
            );
          },
        ),
        gridLinesVisibility: GridLinesVisibility.both,
        headerGridLinesVisibility: GridLinesVisibility.both,
        columnWidthMode: ColumnWidthMode.fill,
        shrinkWrapRows: true,
        rowHeight: 120,
        columns: state.changeHeaders.asMap().entries.map((entry) {
          final e = entry.value;
          return GridColumn(
              columnName: e,
              label: Container(
                  color: Color(0xffF1F5F9),
                  alignment: Alignment.center,
                  child: Text(
                    e,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  )));
        }).toList(),
      );
    });
  }

  Widget _buildCaseInfoSection() {
    return Consumer(builder: (context, ref, child) {
      final state = ref.watch(casePageInfoProvider);
      return SfDataGrid(
        source: DynamicDataSource(
          data: state.caseListTableData,
          columns: state.caseHeaders,
          cellBuilder: (context, cell, index) {
            return Container(
              alignment: Alignment.center,
              color: Colors.white,
              child: Text(
                cell.value ?? '',
                maxLines: 6,
                overflow: TextOverflow.ellipsis,
              ),
            );
          },
        ),
        gridLinesVisibility: GridLinesVisibility.both,
        headerGridLinesVisibility: GridLinesVisibility.both,
        columnWidthMode: ColumnWidthMode.fill,
        shrinkWrapRows: true,
        rowHeight: 120,
        columns: state.changeHeaders.asMap().entries.map((entry) {
          final e = entry.value;
          return GridColumn(
              columnName: e,
              label: Container(
                  color: Color(0xffF1F5F9),
                  alignment: Alignment.center,
                  child: Text(
                    e,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  )));
        }).toList(),
      );
    });
  }



  Widget _buildSectionHeader(String title) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Container(
          width: 5,
          height: 16,
          decoration: BoxDecoration(
              color: Color(0xff488AFD),
              borderRadius: BorderRadius.vertical(
                  top: Radius.circular(2.5), bottom: Radius.circular(2.5))),
        ),
        SizedBox(width: 9),
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }
}
