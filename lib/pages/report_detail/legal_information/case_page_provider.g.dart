// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'case_page_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$casePageInfoHash() => r'2bed69e3a268c8331baa94db08429f7b793e259f';

/// See also [CasePageInfo].
@ProviderFor(CasePageInfo)
final casePageInfoProvider =
    AutoDisposeNotifierProvider<CasePageInfo, CaseInfoState>.internal(
  CasePageInfo.new,
  name: r'casePageInfoProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$casePageInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CasePageInfo = AutoDisposeNotifier<CaseInfoState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
