import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';

part 'case_page_provider.g.dart';

class CaseInfoState {
  EnterpriseCaseModel? model;

  CaseInfoState copyWith(EnterpriseCaseModel? model) {
    return CaseInfoState()..model = model ?? this.model;
  }

  List<EnterpriseChange> getChangeList() =>
      model?.qccEnterpriseChangeList ?? [];
  List<EnterpriseCase> getCaseList() => model?.qccEnterpriseCaseList ?? [];

  /// 股东变更明细
  bool get hasEnterpriseChange => model!.qccEnterpriseChangeList.isNotEmpty;

  /// 表头
  List<String> get changeHeaders {
    return ['变更类型', '变更时间', '变更前', '变更后'];
  }

  /// 表内容
  List<List<String>> get enterpriseChangeListTableData {
    final feesList = model!.qccEnterpriseChangeList;
    List<List<String>> res = [];
    int cols = 4;
    int rows = feesList.length;

    for (var i = 0; i < rows; i++) {
      List<String> inner = [];
      final itemData = feesList[i];
      for (var j = 0; j < cols + 1; j++) {
        if (j == 0) {
          inner.add(itemData.name);
        } else if (j == 1) {
          inner.add(itemData.changeDate);
        } else if (j == 2) {
          inner.add(itemData.beforeContent);
        } else {
          inner.add(itemData.affterContent);
        }
      }
      res.add(inner);
    }
    return res;
  }

  /// 股东明细
  bool get hasCases => model?.qccEnterpriseCaseList.isNotEmpty ?? false;

  /// 表头
  List<String> get caseHeaders {
    return ['文书标题', '案由', '案件身份', '案件日期'];
  }

  /// 表内容
  List<List<String>> get caseListTableData {
    final feesList = model!.qccEnterpriseCaseList;
    List<List<String>> res = [];
    int cols = 4;
    int rows = feesList.length;

    for (var i = 0; i < rows; i++) {
      List<String> inner = [];
      final itemData = feesList[i];
      for (var j = 0; j < cols + 1; j++) {
        if (j == 0) {
          inner.add(itemData.name ?? '');
        } else if (j == 1) {
          inner.add(itemData.result ?? '');
        } else if (j == 2) {
          inner.add(itemData.roleType ?? '');
        } else {
          inner.add('${itemData.judgeDate}');
        }
      }
      res.add(inner);
    }
    return res;
  }
}

@riverpod
class CasePageInfo extends _$CasePageInfo {
  @override
  CaseInfoState build() {
    return CaseInfoState();
  }

  Future<void> getDetail(String shareCode, {bool isExample = false}) async {
    if (isExample) {
      // state = state.copyWith(example_DetailInfoModel);
    } else {
      try {
        final response = await QueryApi.caseInfo(shareCode);
        state = state.copyWith(response.data);
      } catch (e) {
        debugPrint('${e}');
      }
    }
  }
}
