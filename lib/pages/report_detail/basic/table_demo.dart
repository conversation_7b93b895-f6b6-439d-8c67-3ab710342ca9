import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

class Student {
  final String name;
  final int chinese;
  final int math;
  final int english;
  final int physics;

  Student(this.name, this.chinese, this.math, this.english, this.physics);
}

class StudentDataSource extends DataGridSource {
  final List<DataGridRow> _rows;

  StudentDataSource(List<Student> students)
      : _rows = students
            .map((s) => DataGridRow(cells: [
                  DataGridCell<String>(columnName: 'name', value: s.name),
                  DataGridCell<int>(columnName: 'chinese', value: s.chinese),
                  DataGridCell<int>(columnName: 'math', value: s.math),
                  DataGridCell<int>(columnName: 'english', value: s.english),
                  DataGridCell<int>(columnName: 'physics', value: s.physics),
                ]))
            .toList();

  @override
  List<DataGridRow> get rows => _rows;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    return DataGridRowAdapter(
        cells: row.getCells().map((cell) {
      return Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: Text(cell.value.toString()),
      );
    }).toList());
  }
}

class TableDemo extends StatelessWidget {
  const TableDemo({super.key});

  static final List<Student> _students = [
    Student("张三", 98, 89, 94, 87),
    Student("李四", 76, 88, 85, 90),
    Student("王五", 90, 91, 93, 89),
    Student("赵六", 85, 80, 86, 88),
    Student("钱七", 78, 70, 88, 82),
  ];

  Widget _buildTable(String title) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("📋 $title", style: const TextStyle(fontSize: 18)),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: Colors.grey.shade300),
          ),
          clipBehavior: Clip.hardEdge,
          child: SfDataGrid(
            frozenColumnsCount: 1,
            source: StudentDataSource(_students),
            columns: [
              GridColumn(
                  columnName: 'name',
                  width: 100,
                  label: Center(child: Text('姓名'))),
              GridColumn(
                  columnName: 'chinese',
                  width: 120,
                  label: Center(child: Text('语文'))),
              GridColumn(
                  columnName: 'math',
                  width: 120,
                  label: Center(child: Text('数学'))),
              GridColumn(
                  columnName: 'english',
                  width: 120,
                  label: Center(child: Text('英语'))),
              GridColumn(
                  columnName: 'physics',
                  width: 120,
                  label: Center(child: Text('物理'))),
            ],
            columnWidthMode: ColumnWidthMode.none,
            shrinkWrapRows: true, // 自动高度，关键
            horizontalScrollPhysics: const ClampingScrollPhysics(),
            verticalScrollPhysics:
                const NeverScrollableScrollPhysics(), // 禁用内部分页滚动
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildTable("语文考试成绩"),
          _buildTable("数学考试成绩"),
        ],
      ),
    );
  }
}
