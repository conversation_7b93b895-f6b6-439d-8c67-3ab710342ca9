import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/common/models/query_company.dart';
import 'package:zrreport/pages/report_detail/example_info.dart';
import 'package:zrreport/pages/report_detail/widget/listItem.dart';

part 'basic_provider.g.dart';

class BasicInfoState {
  ReportDetailModel? model;

  BasicInfoState copyWith(ReportDetailModel? model) {
    return BasicInfoState()..model = model ?? this.model;
  }

  List<EnterprisePartner> getPartnersData() {
    return model!.qccEnterprisePartnersList;
  }

  /// 股东变更明细
  bool get hasEnterpriseChange => model!.qccEnterpriseChangeList.isNotEmpty;

  /// 表头
  List<String> get enterpriseChangeListTableHeadData {
    return ['变更类型', '变更时间', '变更前', '变更后'];
  }

  /// 表内容
  List<List<String>> get enterpriseChangeListTableData {
    final feesList = model!.qccEnterpriseChangeList;
    List<List<String>> res = [];
    int cols = 4;
    int rows = feesList.length;

    for (var i = 0; i < rows; i++) {
      List<String> inner = [];
      final itemData = feesList[i];
      for (var j = 0; j < cols + 1; j++) {
        if (j == 0) {
          inner.add(itemData.name);
        } else if (j == 1) {
          inner.add(itemData.changeDate);
        } else if (j == 2) {
          inner.add(itemData.beforeContent);
        } else {
          inner.add('${itemData.affterContent}');
        }
      }
      res.add(inner);
    }
    return res;
  }

  /// 股东明细
  bool get hasqccEnterprisePartnersList =>
      model!.qccEnterprisePartnersList.isNotEmpty;

  /// 表头
  List<String> get enterprisePartnersListTableHeadData {
    return ['股东名称', '股东类型', '参股数额(万)', '参股比例'];
  }

  /// 表内容
  List<List<String>> get enterprisePartnersListTableData {
    final feesList = model!.qccEnterprisePartnersList;
    List<List<String>> res = [];
    int cols = 4;
    int rows = feesList.length;

    for (var i = 0; i < rows; i++) {
      List<String> inner = [];
      final itemData = feesList[i];
      for (var j = 0; j < cols + 1; j++) {
        if (j == 0) {
          inner.add(itemData.stockName);
        } else if (j == 1) {
          inner.add(itemData.stockType);
        } else if (j == 2) {
          inner.add(itemData.realCapi);
        } else {
          inner.add('${itemData.stockPercent}');
        }
      }
      res.add(inner);
    }
    return res;
  }
}

@riverpod
class BasicInfo extends _$BasicInfo {
  @override
  BasicInfoState build() {
    return BasicInfoState();
  }

  Future<void> getReposrtDetail(String shareCode,
      {bool isExample = false}) async {
    if (isExample) {
      state = state.copyWith(example_DetailInfoModel);
    } else {
      try {
        final response = await QueryApi.reportDetail(shareCode);
        state = state.copyWith(response.data);
      } catch (e) {
        debugPrint('${e}');
      }
    }
  }
}
