// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'basic_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$basicInfoHash() => r'661cc3968bcd63e71b8a556842e172bb02cfc74b';

/// See also [BasicInfo].
@ProviderFor(BasicInfo)
final basicInfoProvider =
    AutoDisposeNotifierProvider<BasicInfo, BasicInfoState>.internal(
  BasicInfo.new,
  name: r'basicInfoProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$basicInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BasicInfo = AutoDisposeNotifier<BasicInfoState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
