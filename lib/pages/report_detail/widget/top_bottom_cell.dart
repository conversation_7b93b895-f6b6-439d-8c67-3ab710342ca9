import 'package:flutter/material.dart';

class TopBottomCell extends StatelessWidget {
  final String topTitle;
  final String bottomTitle;
  const TopBottomCell({super.key, required this.topTitle, required this.bottomTitle});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFF8F8F8),
      width: double.infinity,
      child: Padding(
        padding: EdgeInsets.all(5),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              topTitle,
              style: TextStyle(
                color: Colors.black,
                fontSize: 15,
              ),
            ),
            Text(
              bottomTitle,
              style: TextStyle(
                color: Colors.black,
                fontSize: 15,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
