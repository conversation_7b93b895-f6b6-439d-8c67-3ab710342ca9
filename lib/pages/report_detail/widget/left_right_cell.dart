import 'package:flutter/material.dart';
import 'package:zrreport/pages/report_detail/widget/listItem.dart';

class LeftRightCell extends StatelessWidget {
  final ListItemViewData cellData;
  const LeftRightCell({super.key, required this.cellData});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: cellData.dark ? Color(0xfff5f5f5) : Colors.white,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            cellData.title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Color(0xff999999),
            ),
          ),
          Text(
            cellData.value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Color(0xff222222),
            ),
          ),
        ],
      ),
    );
  }
}
