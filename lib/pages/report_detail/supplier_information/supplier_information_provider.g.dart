// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'supplier_information_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$supplierInfoShowButtonHash() =>
    r'71c7a9f36b20d5803cf423f271f9978166ff72df';

/// See also [SupplierInfoShowButton].
@ProviderFor(SupplierInfoShowButton)
final supplierInfoShowButtonProvider =
    AutoDisposeNotifierProvider<SupplierInfoShowButton, bool>.internal(
  SupplierInfoShowButton.new,
  name: r'supplierInfoShowButtonProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$supplierInfoShowButtonHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SupplierInfoShowButton = AutoDisposeNotifier<bool>;
String _$supplierInfoHash() => r'710b3be41ee5b598658dda6302f2eb423a4fcd58';

/// See also [SupplierInfo].
@ProviderFor(SupplierInfo)
final supplierInfoProvider = AutoDisposeNotifierProvider<SupplierInfo,
    SupplierInformationViewModel>.internal(
  SupplierInfo.new,
  name: r'supplierInfoProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$supplierInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SupplierInfo = AutoDisposeNotifier<SupplierInformationViewModel>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
