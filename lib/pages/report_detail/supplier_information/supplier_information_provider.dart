import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/api/query.dart';
import 'package:zrreport/common/enum/page_status.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/common/models/supplier_information.dart';
import 'package:zrreport/pages/report_detail/example_info.dart';

part 'supplier_information_provider.g.dart';

class SupplierInformationViewModel {
  var pageStatus = PageStatus.initial;
  SupplierInformation? supplierInformation;

  bool get hasData => supplierInformation != null;

  SupplierInformationViewModel copyWith({
    required PageStatus pageStatus,
    SupplierInformation? supplierInformation,
  }) {
    return SupplierInformationViewModel()
      ..pageStatus = pageStatus
      ..supplierInformation = supplierInformation ?? this.supplierInformation;
  }

  /// 表头
  List<String> get tableHeadData1 {
    List<String> res = ['排名', '供应商名称', '采购额(元)', '金额占比(%)', '是否关联方'];
    return res;
  }

  // 表内容
  List<List<String>> get tableData1 {
    List<List<String>> res = [];
    if (supplierInformation!.paidTaxesDataList != null &&
        supplierInformation!.paidTaxesDataList!.isNotEmpty) {
      int rows = supplierInformation!.paidTaxesDataList!.length;
      int cols = tableHeadData1.length;
      for (var i = 0; i < rows; i++) {
        List<String> inner = [];
        for (var j = 0; j < cols; j++) {
          final item = supplierInformation!.paidTaxesDataList![i];
          if (j == 0) {
            inner.add(item.sort != null ? item.sort!.toString() : '_');
          } else if (j == 1) {
            inner.add(item.buySaleName ?? '_');
          } else if (j == 2) {
            inner.add(item.sumAmount != null
                ? formatLargeNumber(item.sumAmount!.toDouble())
                : '_');
          } else if (j == 3) {
            inner.add(item.amountRate != null ? '${item.amountRate!}%' : '_');
          } else {
            inner.add(item.relatedStatus != null
                ? (item.relatedStatus! ? '是' : '否')
                : '_');
          }
        }
        res.add(inner);
      }
    }
    return res;
  }

  bool get table2HasData =>
      supplierInformation!.redInvoiceInfoDataList != null &&
      supplierInformation!.redInvoiceInfoDataList!.isNotEmpty;
  // 表内容
  List<List<String>> get tableData2 {
    List<List<String>> res = [];
    if (supplierInformation!.redInvoiceInfoDataList != null &&
        supplierInformation!.redInvoiceInfoDataList!.isNotEmpty) {
      int rows = supplierInformation!.redInvoiceInfoDataList!.length;
      int cols = tableHeadData1.length;
      for (var i = 0; i < rows; i++) {
        List<String> inner = [];
        for (var j = 0; j < cols; j++) {
          final item = supplierInformation!.redInvoiceInfoDataList![i];
          if (j == 0) {
            inner.add(item.sort != null ? item.sort!.toString() : '_');
          } else if (j == 1) {
            inner.add(item.buySaleName ?? '_');
          } else if (j == 2) {
            inner.add(item.sumAmount != null
                ? formatLargeNumber(item.sumAmount!.toDouble())
                : '_');
          } else if (j == 3) {
            inner.add(item.amountRate != null ? '${item.amountRate!}%' : '_');
          } else {
            inner.add(item.relatedStatus != null
                ? (item.relatedStatus! ? '是' : '否')
                : '_');
          }
        }
        res.add(inner);
      }
    }
    return res;
  }
}

@riverpod
class SupplierInfoShowButton extends _$SupplierInfoShowButton {
  @override
  bool build() {
    return false;
  }

  void hidden() {
    if (state != false) {
      state = false;
    }
  }

  void show() {
    if (state != true) {
      state = true;
    }
  }
}

@riverpod
class SupplierInfo extends _$SupplierInfo {
  @override
  SupplierInformationViewModel build() {
    return SupplierInformationViewModel();
  }

  Future<void> loadData(String shareCode, bool isExample) async {
    if (isExample) {
      state = await state.copyWith(pageStatus: PageStatus.success, supplierInformation: example_ShoperInfo);
    } else {
       try {
      /// 如果页面没有数据解开启加载状态
      if (state.hasData == false) {
        state = state.copyWith(pageStatus: PageStatus.loading);
      }
      final response = await QueryApi.supplierInformation(shareCode);
      if (response.data != null) {
        state = state.copyWith(
          pageStatus: PageStatus.success,
          supplierInformation: response.data,
        );
      } else {
        state = state.copyWith(pageStatus: PageStatus.empty);
      }
    } catch (e) {
      state = state.copyWith(pageStatus: PageStatus.error);
      print(e);
    }
    }
  }
}
