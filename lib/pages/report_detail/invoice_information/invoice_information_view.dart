import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/report_detail/invoice_information/declaration_data_view.dart';
import 'package:zrreport/pages/report_detail/invoice_information/get_invoice_view.dart';
import 'package:zrreport/pages/report_detail/invoice_information/invoice_information_provider.dart';

import 'issue_Invoice_view.dart';

class InvoiceInformationView extends StatelessWidget {
  const InvoiceInformationView({super.key, required this.shareCode, this.isExample = false});

  final String shareCode;
  final bool isExample;

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ProviderScope.containerOf(context)
          .read(invoiceInformationProvider.notifier)
          .setType(InvoiceInformationViewType.issueInvoice, shareCode, isExample);
    });

    return Consumer(builder: (context, ref, child) {
      final response = ref.watch(invoiceInformationProvider);
      switch (response.pageStatus) {
        case PageStatus.initial:
        case PageStatus.loading:
          return Center(
            child: LoadingWidget(),
          );
        case PageStatus.success:
          return InvoiceInformationBodyView(shareCode: shareCode, isExample: isExample);
        case PageStatus.empty:
          return Center(
            child: Text(''),
          );
        case PageStatus.error:
          return Center(
            child: Text(''),
          );
      }
    });
    // InvoiceInformationBodyView(shareCode: shareCode)
  }
}

class InvoiceInformationBodyView extends HookWidget {
  const InvoiceInformationBodyView({super.key, required this.shareCode, this.isExample = false});

  final String shareCode;
   final bool isExample;

  @override
  Widget build(BuildContext context) {
    final scrollController = useScrollController();
    return Consumer(builder: (context, ref, child) {
      final notifier = ref.read(invoiceInformationShowButtonProvider.notifier);
      scrollController.addListener(() {
        if (scrollController.offset > 0) {
          notifier.show();
        } else {
          notifier.hidden();
        }
      });
      return Stack(
        children: [
          SingleChildScrollView(
            controller: scrollController,
            padding: EdgeInsets.all(16),
            child: Column(
              children: [
                headWidget(),
                SizedBox(height: 16),
                Consumer(
                  builder: (context, ref, child) {
                    final response = ref.watch(invoiceInformationProvider);
                    switch (response.type) {
                      case InvoiceInformationViewType.issueInvoice:
                        return IssueInvoiceView();
                      case InvoiceInformationViewType.declarationData:
                        return DeclarationDataView();
                      case InvoiceInformationViewType.getInvoice:
                        return GetInvoiceView();
                    }
                  },
                )
              ],
            ),
          ),
          Positioned(
            right: 32,
            bottom: 32,
            child: Consumer(builder: (context, ref, child) {
              return Visibility(
                visible: ref.watch(invoiceInformationShowButtonProvider),
                child: InkWell(
                  onTap: () {
                    scrollController.animateTo(
                      0.0, // 滚动到顶部位置 (0.0)
                      duration: const Duration(milliseconds: 500), // 滚动动画时长
                      curve: Curves.easeOut, // 滚动动画曲线
                    );
                  },
                  child: Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      color: Color(0xFFDDDDDD),
                      borderRadius: BorderRadius.circular(22),
                    ),
                    child: Center(
                      child: Icon(Icons.arrow_upward),
                    ),
                  ),
                ),
              );
            }),
          ),
        ],
      );
    });
  }

  Widget headWidget() {
    return Consumer(
      builder: (context, ref, child) {
        final response = ref.watch(invoiceInformationProvider);
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(14),
            color: Color(0xffd3e3fe),
          ),
          height: 44,
          child: Row(
            children: [
              Expanded(
                child: _itemButton(
                  title: '开具发票',
                  selected:
                      response.type == InvoiceInformationViewType.issueInvoice,
                  onPressed: () {
                    if (response.type !=
                        InvoiceInformationViewType.issueInvoice) {
                      ref.read(invoiceInformationProvider.notifier).setType(
                          InvoiceInformationViewType.issueInvoice, shareCode, isExample);
                    }
                  },
                ),
              ),
              Expanded(
                child: _itemButton(
                  title: '申报数据',
                  selected: response.type ==
                      InvoiceInformationViewType.declarationData,
                  onPressed: () {
                    if (response.type !=
                        InvoiceInformationViewType.declarationData) {
                      ref.read(invoiceInformationProvider.notifier).setType(
                          InvoiceInformationViewType.declarationData,
                          shareCode, 
                          isExample);
                    }
                  },
                ),
              ),
              Expanded(
                child: _itemButton(
                  title: '取得发票',
                  selected:
                      response.type == InvoiceInformationViewType.getInvoice,
                  onPressed: () {
                    if (response.type !=
                        InvoiceInformationViewType.getInvoice) {
                      ref.read(invoiceInformationProvider.notifier).setType(
                          InvoiceInformationViewType.getInvoice, shareCode, isExample);
                    }
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  ElevatedButton _itemButton(
      {required String title,
      required bool selected,
      required VoidCallback? onPressed}) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        minimumSize: Size(double.infinity, double.infinity),
        backgroundColor: selected ? Color(0xff488afd) : Color(0xffd3e3fe),
        elevation: 0,
      ),
      child: Text(
        title,
        style: TextStyle(
            fontSize: 15,
            fontWeight: FontWeight.bold,
            color: selected ? Colors.white : Colors.black),
      ),
    );
  }
}
