import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/report_detail/widget/section_left_more_header.dart';

import '../widget/center_cell.dart';
import 'invoice_information_provider.dart';

class DeclarationDataView extends StatelessWidget {
  const DeclarationDataView({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      final response = ref.read(invoiceInformationProvider);
      return Column(
        children: [
          // 头部列表
          ...response.declarationDataHeadList.map((e) {
            return CenterCell(cellData: e);
          }),
          Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: SectionLeftMoreHeader(title: '申报数据对比分析')),
          _buildTable1(response),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: SectionHeader(title: '进三年申报总金额'),
          ),
          _buildTable2(response),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: SectionHeader(title: '近三年无票收入'),
          ),
          _buildTable3(response),
        ],
      );
    });
  }

  /// 申报数据对比分析
  Widget _buildTable1(InvoiceInformationViewModel response) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Color(0xffE5E7EB),
          width: 1,
        ),
      ),
      clipBehavior: Clip.antiAlias,
      child: SfDataGrid(
        source: DynamicDataSource(
          data: response.declarationDataTableData1,
          columns: response.declarationDataTableHeadData1,
          cellBuilder: (context, cell, index) {
            // 在这里处理table body
            return Container(
              alignment: index == 0 ? Alignment.centerLeft : Alignment.center,
              padding: EdgeInsets.symmetric(
                vertical: 8.0,
                horizontal: index == 0 ? 8.0 : 0,
              ),
              color: index == 0 ? Color(0xFFF8F8F8) : Colors.white,
              child: Text(
                cell.value ?? '',
              ),
            );
          },
        ),
        frozenColumnsCount: 1, // 第一列固定
        gridLinesVisibility: GridLinesVisibility.both,
        headerGridLinesVisibility: GridLinesVisibility.both,
        shrinkWrapRows: true, // 自动撑开高度
        verticalScrollPhysics: NeverScrollableScrollPhysics(), // 关闭滚动，让外层控制
        columns:
            response.declarationDataTableHeadData1.asMap().entries.map((entry) {
          // 在这里处理table head
          final index = entry.key;
          final e = entry.value;
          return GridColumn(
            columnName: e,
            width: index == 0 ? 135 : 100, // 第一列宽度不同
            label: Container(
              color: Color(0xffF1F5F9),
              padding: EdgeInsets.symmetric(
                  vertical: 16, horizontal: index == 0 ? 8 : 0),
              alignment: index == 0 ? Alignment.centerLeft : Alignment.center,
              child: Text(
                e,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 近三年申报总金额表格
  Widget _buildTable2(InvoiceInformationViewModel response) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Color(0xffE5E7EB),
          width: 1,
        ),
      ),
      clipBehavior: Clip.antiAlias,
      child: SfDataGrid(
        source: DynamicDataSource(
          data: response.declarationDataTableData2,
          columns: response.declarationDataTableHeadData2,
          cellBuilder: (context, cell, index) {
            // 在这里处理table body
            return Container(
              alignment: Alignment.center,
              padding: EdgeInsets.all(8.0),
              child: Text(cell.value ?? ''),
            );
          },
        ),
        columnWidthMode: ColumnWidthMode.fill,
        gridLinesVisibility: GridLinesVisibility.both,
        headerGridLinesVisibility: GridLinesVisibility.both,
        shrinkWrapRows: true, // 自动撑开高度
        verticalScrollPhysics: NeverScrollableScrollPhysics(), // 关闭滚动，让外层控制
        columns: response.declarationDataTableHeadData2.map((e) {
          // 在这里处理table head
          return GridColumn(
            columnName: e,
            label: Container(
              color: Color(0xffF1F5F9),
              padding: EdgeInsets.all(16.0),
              alignment: Alignment.center,
              child: Text(
                e,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 近三年无票收入表格
  Widget _buildTable3(InvoiceInformationViewModel response) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Color(0xffE5E7EB),
          width: 1,
        ),
      ),
      clipBehavior: Clip.antiAlias,
      child: SfDataGrid(
        source: DynamicDataSource(
          data: response.declarationDataTableData3,
          columns: response.declarationDataTableHeadData3,
          cellBuilder: (context, cell, index) {
            // 在这里处理table body
            return Container(
              alignment: Alignment.center,
              padding: EdgeInsets.all(8.0),
              child: Text(cell.value ?? ''),
            );
          },
        ),
        columnWidthMode: ColumnWidthMode.fill,
        gridLinesVisibility: GridLinesVisibility.both,
        headerGridLinesVisibility: GridLinesVisibility.both,
        shrinkWrapRows: true, // 自动撑开高度
        verticalScrollPhysics: NeverScrollableScrollPhysics(), // 关闭滚动，让外层控制
        columns: response.declarationDataTableHeadData3.map((e) {
          // 在这里处理table head
          return GridColumn(
            columnName: e,
            label: Container(
              color: Color(0xffF1F5F9),
              padding: EdgeInsets.all(16.0),
              alignment: Alignment.center,
              child: Text(
                e,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
