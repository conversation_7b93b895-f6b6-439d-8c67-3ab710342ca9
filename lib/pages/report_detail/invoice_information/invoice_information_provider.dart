import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/report_detail/example_info.dart';
import 'package:zrreport/pages/report_detail/widget/listItem.dart';
part 'invoice_information_provider.g.dart';

enum InvoiceInformationViewType { issueInvoice, declarationData, getInvoice }

class TopBottomCellData {
  final String top;
  final String bottom;
  const TopBottomCellData({required this.top, required this.bottom});
}

class InvoiceInformationViewModel {
  var pageStatus = PageStatus.initial;
  var type = InvoiceInformationViewType.issueInvoice;
  EnterpriseInvoiceInfo? invoiceInfo; // 开具发票
  EnterpriseApplyAmount? applyAmount; // 申报数据
  EnterpriseInvoiceInfoForIn? invoiceInfoForIn; // 取得发票

  InvoiceInformationViewModel copyWith({
    required PageStatus pageStatus,
    InvoiceInformationViewType? type,
    EnterpriseInvoiceInfo? invoiceInfo,
    EnterpriseApplyAmount? applyAmount,
    EnterpriseInvoiceInfoForIn? invoiceInfoForIn,
  }) {
    return InvoiceInformationViewModel()
      ..pageStatus = pageStatus
      ..type = type ?? this.type
      ..invoiceInfo = invoiceInfo ?? this.invoiceInfo
      ..applyAmount = applyAmount ?? this.applyAmount
      ..invoiceInfoForIn = invoiceInfoForIn ?? this.invoiceInfoForIn;
  }

  bool get hasData =>
      invoiceInfo != null || applyAmount != null || invoiceInfoForIn != null;

  /// 以下是发票信息：
  /// ---------------开具发票逻辑处理---------------
  List<ListItemViewData> get invoiceInfoHeadList {
    List<ListItemViewData> res = [
      ListItemViewData(
        dark: true,
        title: '近45日是否有开票记录',
        value: invoiceInfo!.billingRecords != null
            ? (invoiceInfo!.billingRecords! ? '有' : '无')
            : '_',
      ),
      ListItemViewData(
        dark: false,
        title: '近30天开票金额',
        value: invoiceInfo!.billingDaysSum != null
            ? formatLargeNumber(invoiceInfo!.billingDaysSum!.toDouble())
            : '_',
      ),
      ListItemViewData(
        dark: true,
        title: '近12月作废发票数量占比',
        value: invoiceInfo!.taxVoidRateMonth12 != null
            ? '${invoiceInfo!.taxVoidRateMonth12!}%'
            : '_',
      ),
      ListItemViewData(
        dark: false,
        title: '近12个月断票月数',
        value: invoiceInfo!.breakMonthsSum12 != null
            ? '${invoiceInfo!.breakMonthsSum12!}'
            : '_',
      ),
      ListItemViewData(
        dark: true,
        title: '近12个月月均开票金额同比增长率',
        value: invoiceInfo!.taxAmountGrowthRateMonth12 != null
            ? '${invoiceInfo!.taxAmountGrowthRateMonth12!}%'
            : '_',
      ),
      ListItemViewData(
        dark: false,
        title: '最早开票时间',
        value: invoiceInfo!.earliestTime != null
            ? invoiceInfo!.earliestTime!
            : '_',
      ),
      ListItemViewData(
        dark: true,
        title: '最晚开票时间',
        value: invoiceInfo!.latestTime != null ? invoiceInfo!.latestTime! : '_',
      ),
    ];
    return res;
  }

  bool get hasInvoiceInfoDataList =>
      invoiceInfo!.invoiceInfoDataList != null &&
      invoiceInfo!.invoiceInfoDataList!.isNotEmpty;

  /// 表头
  List<String> get invoiceInfoTableHeadData1 {
    List<String> res = ['月份'];
    for (var value in invoiceInfo!.invoiceInfoDataList!) {
      res.add('${value.year ?? '_'}年');
    }
    return res;
  }

  // 表内容
  List<List<String>> get invoiceInfoTableData1 {
    List<List<String>> res = [];
    if (invoiceInfo!.invoiceInfoDataList!.first.monthData != null &&
        invoiceInfo!.invoiceInfoDataList!.first.monthData!.isNotEmpty) {
      int rows = invoiceInfo!.invoiceInfoDataList!.first.monthData!.length;
      int cols = invoiceInfo!.invoiceInfoDataList!.length;
      var totalList = List<double>.filled(cols, 0, growable: false);
      for (var i = 0; i < rows + 1; i++) {
        List<String> inner = [];
        for (var j = 0; j < cols + 1; j++) {
          if (j == 0) {
            if (i == rows) {
              // 最后一行是合计信息
              inner.add('合计');
            } else {
              inner.add(
                  '${invoiceInfo!.invoiceInfoDataList![0].monthData![i].name ?? '_'}月');
            }
          } else {
            if (i == rows) {
              // 最后一行是合计信息
              inner.add(formatLargeNumber(totalList[j - 1]));
            } else {
              if (invoiceInfo!
                      .invoiceInfoDataList![j - 1].monthData![i].value ==
                  null) {
                inner.add('_');
              } else {
                final num = invoiceInfo!
                    .invoiceInfoDataList![j - 1].monthData![i].value!
                    .toDouble();
                inner.add(formatLargeNumber(num));
                totalList[j - 1] += num;
              }
            }
          }
        }
        res.add(inner);
      }
    }
    return res;
  }

  List<TopBottomCellData> get invoiceInfoTopBottomCellDatas {
    List<TopBottomCellData> res = [];
    res.add(TopBottomCellData(
        top: '近12月最长连续断票月数',
        bottom:
            '${invoiceInfo!.breakMonthsSum12 != null ? invoiceInfo!.breakMonthsSum12! : '_'}'));
    res.add(TopBottomCellData(
        top: '近12个月最大连续未开具发票间隔天数(销项)',
        bottom: invoiceInfo!.taxOutputNotInvoicedMonth12 ?? '_'));
    res.add(TopBottomCellData(
        top: '最近开具发票间隔天数',
        bottom: invoiceInfo!.recentBillingGapdays != null
            ? invoiceInfo!.recentBillingGapdays!
            : '_'));
    return res;
  }

  bool get hasEnterpriseMonthApplyList =>
      invoiceInfo!.enterpriseMonthApplyList != null &&
      invoiceInfo!.enterpriseMonthApplyList!.isNotEmpty;

  /// 表头
  List<String> get invoiceInfoTableHeadData2 {
    List<String> res = ['开票对比分析'];
    for (var value in invoiceInfo!.enterpriseMonthApplyList!) {
      res.add('近${value.month ?? '_'}个月');
    }
    return res;
  }

  // 表内容
  List<List<String>> get invoiceInfoTableData2 {
    List<List<String>> res = [];
    int rows = 9;
    int cols = invoiceInfo!.enterpriseMonthApplyList!.length;
    const titles = [
      '开票金额',
      '开票环比增长率',
      '开票同比增长率',
      '开票张数',
      '开票月数',
      '红冲发票张数占比',
      '红冲金额占比',
      '上游客户数量',
      '下游客户数量'
    ];
    for (var i = 0; i < rows; i++) {
      List<String> inner = [];
      for (var j = 0; j < cols + 1; j++) {
        if (j == 0) {
          inner.add(titles[i]);
        } else {
          final itemData = invoiceInfo!.enterpriseMonthApplyList![j - 1];
          if (i == 0) {
            // 开票金额
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(itemData.taxEnterpriseApplyDate!.invoiceAmount != null
                  ? formatLargeNumber(itemData
                      .taxEnterpriseApplyDate!.invoiceAmount!
                      .toDouble())
                  : '_');
            } else {
              inner.add('_');
            }
          } else if (i == 1) {
            // 开票环比增长率
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(
                  '${itemData.taxEnterpriseApplyDate!.invoiceGrowthRate ?? '-'}%');
            } else {
              inner.add('-%');
            }
          } else if (i == 2) {
            // 开票同比增长率
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(
                  '${itemData.taxEnterpriseApplyDate!.invoiceOverGrowthRate ?? '-'}%');
            } else {
              inner.add('-%');
            }
          } else if (i == 3) {
            // 开票张数
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(
                  '${itemData.taxEnterpriseApplyDate!.invoiceCount ?? '_'}');
            } else {
              inner.add('_');
            }
          } else if (i == 4) {
            // 开票月数
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(
                  itemData.taxEnterpriseApplyDate!.invoiceMonthsNum ?? '_');
            } else {
              inner.add('_');
            }
          } else if (i == 5) {
            // 红冲发票张数占比
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(
                  '${itemData.taxEnterpriseApplyDate!.invoiceRedCountRate ?? '-'}%');
            } else {
              inner.add('-%');
            }
          } else if (i == 6) {
            // 红冲金额占比
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(
                  '${itemData.taxEnterpriseApplyDate!.invoiceRedAmountRate ?? '-'}%');
            } else {
              inner.add('-%');
            }
          } else if (i == 7) {
            // 上游客户数量
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(
                  '${itemData.taxEnterpriseApplyDate!.customerUpNum ?? '_'}');
            } else {
              inner.add('_');
            }
          } else {
            // 下游客户数量
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(
                  '${itemData.taxEnterpriseApplyDate!.customerDownNum ?? '_'}');
            } else {
              inner.add('_');
            }
          }
        }
      }
      res.add(inner);
    }
    return res;
  }

  /// ---------------申报数据逻辑处理---------------
  List<ListItemViewData> get declarationDataHeadList {
    List<ListItemViewData> res = [
      ListItemViewData(
        dark: true,
        title: '近45日是否有申报记录',
        value: applyAmount!.applyRecords != null
            ? (applyAmount!.applyRecords! ? '有' : '无')
            : '_',
      ),
      ListItemViewData(
        dark: false,
        title: '近30天申报金额',
        value: applyAmount!.applyAmountMonths1 != null
            ? formatLargeNumber(applyAmount!.applyAmountMonths1!.toDouble())
            : '_',
      ),
      ListItemViewData(
        dark: true,
        title: '近12个月申报次数',
        value: applyAmount!.applySum12 != null ? applyAmount!.applySum12! : '_',
      ),
    ];
    return res;
  }

  bool get declarationDataHasEnterpriseMonthApplyList =>
      invoiceInfo!.enterpriseMonthApplyList != null &&
      invoiceInfo!.enterpriseMonthApplyList!.isNotEmpty;

  /// 申报数据对比分析: 表头
  List<String> get declarationDataTableHeadData1 {
    List<String> res = ['申报数据对比分析'];
    for (var value in applyAmount!.enterpriseMonthApplyList!) {
      res.add('近${value.month ?? '_'}个月');
    }
    return res;
  }

  // 申报数据对比分析: 表内容
  List<List<String>> get declarationDataTableData1 {
    List<List<String>> res = [];
    int rows = 3;
    int cols = applyAmount!.enterpriseMonthApplyList!.length;
    const titles = ['申报金额', '申报环比增长率', '申报同比增长率'];
    for (var i = 0; i < rows; i++) {
      List<String> inner = [];
      for (var j = 0; j < cols + 1; j++) {
        if (j == 0) {
          inner.add(titles[i]);
        } else {
          final itemData = applyAmount!.enterpriseMonthApplyList![j - 1];
          if (i == 0) {
            // 申报金额
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(itemData.taxEnterpriseApplyDate!.applyAmount != null
                  ? formatLargeNumber(
                      itemData.taxEnterpriseApplyDate!.applyAmount!.toDouble())
                  : '_');
            } else {
              inner.add('_');
            }
          } else if (i == 1) {
            // 申报环比增长率
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(
                  '${itemData.taxEnterpriseApplyDate!.applyGrowthRate ?? '-'}%');
            } else {
              inner.add('-%');
            }
          } else {
            // 申报同比增长率
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(
                  '${itemData.taxEnterpriseApplyDate!.applyOverGrowthRate ?? '-'}%');
            } else {
              inner.add('-%');
            }
          }
        }
      }
      res.add(inner);
    }
    return res;
  }

  /// 近三年申报总金额: 表头
  List<String> get declarationDataTableHeadData2 {
    List<String> res = ['月份'];
    for (var value in applyAmount!.applyInfoDataList!) {
      res.add('${value.year ?? '_'}年');
    }
    return res;
  }

  // 近三年申报总金额: 表内容
  List<List<String>> get declarationDataTableData2 {
    List<List<String>> res = [];
    if (applyAmount!.applyInfoDataList!.first.monthData != null &&
        applyAmount!.applyInfoDataList!.first.monthData!.isNotEmpty) {
      int rows = applyAmount!.applyInfoDataList!.first.monthData!.length;
      int cols = applyAmount!.applyInfoDataList!.length;
      var totalList = List<double>.filled(cols, 0, growable: false);
      for (var i = 0; i < rows + 1; i++) {
        List<String> inner = [];
        for (var j = 0; j < cols + 1; j++) {
          if (j == 0) {
            if (i == rows) {
              // 最后一行是合计信息
              inner.add('合计');
            } else {
              inner.add(
                  '${applyAmount!.applyInfoDataList![0].monthData![i].name ?? '_'}月');
            }
          } else {
            if (i == rows) {
              // 最后一行是合计信息
              inner.add(formatLargeNumber(totalList[j - 1]));
            } else {
              if (applyAmount!.applyInfoDataList![j - 1].monthData![i].value ==
                  null) {
                inner.add('_');
              } else {
                final num = applyAmount!
                    .applyInfoDataList![j - 1].monthData![i].value!
                    .toDouble();
                inner.add(formatLargeNumber(num));
                totalList[j - 1] += num;
              }
            }
          }
        }
        res.add(inner);
      }
    }
    return res;
  }

  /// 近三年无票收入: 表头
  List<String> get declarationDataTableHeadData3 {
    List<String> res = ['月份'];
    for (var value in applyAmount!.applySalesDataList!) {
      res.add('${value.year ?? '_'}年');
    }
    return res;
  }

  // 近三年无票收入: 表内容
  List<List<String>> get declarationDataTableData3 {
    List<List<String>> res = [];
    if (applyAmount!.applySalesDataList!.first.monthData != null &&
        applyAmount!.applySalesDataList!.first.monthData!.isNotEmpty) {
      int rows = applyAmount!.applySalesDataList!.first.monthData!.length;
      int cols = applyAmount!.applySalesDataList!.length;
      var totalList = List<double>.filled(cols, 0, growable: false);
      for (var i = 0; i < rows + 1; i++) {
        List<String> inner = [];
        for (var j = 0; j < cols + 1; j++) {
          if (j == 0) {
            if (i == rows) {
              // 最后一行是合计信息
              inner.add('合计');
            } else {
              inner.add(
                  '${applyAmount!.applySalesDataList![0].monthData![i].name ?? '_'}月');
            }
          } else {
            if (i == rows) {
              // 最后一行是合计信息
              inner.add(formatLargeNumber(totalList[j - 1]));
            } else {
              if (applyAmount!.applySalesDataList![j - 1].monthData![i].value ==
                  null) {
                inner.add('_');
              } else {
                final num = applyAmount!
                    .applySalesDataList![j - 1].monthData![i].value!
                    .toDouble();
                inner.add(formatLargeNumber(num));
                totalList[j - 1] += num;
              }
            }
          }
        }
        res.add(inner);
      }
    }
    return res;
  }

  /// ---------------取得发票逻辑处理---------------
  List<ListItemViewData> get getInvoiceHeadList {
    List<ListItemViewData> res = [
      ListItemViewData(
        dark: true,
        title: '近45日是否有取得发票记录',
        value: invoiceInfoForIn!.billingRecords != null
            ? (invoiceInfoForIn!.billingRecords! ? '有' : '无')
            : '_',
      ),
      ListItemViewData(
        dark: false,
        title: '近30天取得发票金额',
        value: invoiceInfoForIn!.billingDaysSum != null
            ? formatLargeNumber(invoiceInfoForIn!.billingDaysSum!.toDouble())
            : '_',
      ),
      ListItemViewData(
        dark: true,
        title: '近12月作废发票数量占比',
        value: invoiceInfoForIn!.taxVoidRateMonth12 != null
            ? invoiceInfoForIn!.taxVoidRateMonth12!
            : '_',
      ),
      ListItemViewData(
        dark: false,
        title: '近12个月断票月数',
        value: invoiceInfoForIn!.breakMonthsSum12 != null
            ? '${invoiceInfoForIn!.breakMonthsSum12!}'
            : '_',
      ),
      ListItemViewData(
        dark: true,
        title: '近12个月月均取得发票金额同比增长率',
        value: invoiceInfoForIn!.taxAmountGrowthRateMonth12 != null
            ? '${invoiceInfoForIn!.taxAmountGrowthRateMonth12!}%'
            : '_',
      ),
      ListItemViewData(
        dark: false,
        title: '最早取得发票时间',
        value: invoiceInfoForIn!.earliestTime != null
            ? invoiceInfoForIn!.earliestTime!
            : '_',
      ),
      ListItemViewData(
        dark: true,
        title: '最晚取得发票时间',
        value: invoiceInfo!.latestTime != null ? invoiceInfo!.latestTime! : '_',
      ),
    ];
    return res;
  }

  bool get hasGetInvoiceDataList =>
      invoiceInfoForIn!.invoiceForInDataList != null &&
      invoiceInfoForIn!.invoiceForInDataList!.isNotEmpty;

  /// 表头
  List<String> get getInvoiceTableHeadData1 {
    List<String> res = ['月份'];
    for (var value in invoiceInfoForIn!.invoiceForInDataList!) {
      res.add('${value.year ?? '_'}年');
    }
    return res;
  }

  // 表内容
  List<List<String>> get getInvoiceTableData1 {
    List<List<String>> res = [];
    if (invoiceInfoForIn!.invoiceForInDataList!.first.monthData != null &&
        invoiceInfoForIn!.invoiceForInDataList!.first.monthData!.isNotEmpty) {
      int rows =
          invoiceInfoForIn!.invoiceForInDataList!.first.monthData!.length;
      int cols = invoiceInfoForIn!.invoiceForInDataList!.length;
      var totalList = List<double>.filled(cols, 0, growable: false);
      for (var i = 0; i < rows + 1; i++) {
        List<String> inner = [];
        for (var j = 0; j < cols + 1; j++) {
          if (j == 0) {
            if (i == rows) {
              // 最后一行是合计信息
              inner.add('合计');
            } else {
              inner.add(
                  '${invoiceInfoForIn!.invoiceForInDataList![0].monthData![i].name ?? '_'}月');
            }
          } else {
            if (i == rows) {
              // 最后一行是合计信息
              inner.add(formatLargeNumber(totalList[j - 1]));
            } else {
              if (invoiceInfoForIn!
                      .invoiceForInDataList![j - 1].monthData![i].value ==
                  null) {
                inner.add('_');
              } else {
                final num = invoiceInfoForIn!
                    .invoiceForInDataList![j - 1].monthData![i].value!
                    .toDouble();
                inner.add(formatLargeNumber(num));
                totalList[j - 1] += num;
              }
            }
          }
        }
        res.add(inner);
      }
    }
    return res;
  }

  List<TopBottomCellData> get getInvoiceTopBottomCellDatas {
    List<TopBottomCellData> res = [];
    res.add(TopBottomCellData(
        top: '近12月最长连续断票月数',
        bottom:
            '${invoiceInfoForIn!.breakMonthsSum12 != null ? invoiceInfoForIn!.breakMonthsSum12! : '_'}'));
    res.add(TopBottomCellData(
        top: '近12个月最大连续未取得发票间隔天数(销项)',
        bottom: invoiceInfoForIn!.taxOutputNotInvoicedMonth12 ?? '_'));
    res.add(TopBottomCellData(
        top: '最近取得发票间隔天数',
        bottom: invoiceInfoForIn!.recentBillingGapdays != null
            ? invoiceInfoForIn!.recentBillingGapdays!
            : '_'));
    return res;
  }

  bool get hasGetInvoiceEnterpriseMonthApplyList =>
      invoiceInfoForIn!.taxEnterpriseMonthInvoiceForInVoList != null &&
      invoiceInfoForIn!.taxEnterpriseMonthInvoiceForInVoList!.isNotEmpty;

  /// 表头
  List<String> get getInvoiceTableHeadData2 {
    List<String> res = ['开票对比分析'];
    for (var value in invoiceInfoForIn!.taxEnterpriseMonthInvoiceForInVoList!) {
      res.add('近${value.month ?? '_'}个月');
    }
    return res;
  }

  // 表内容
  List<List<String>> get getInvoiceTableData2 {
    List<List<String>> res = [];
    int rows = 9;
    int cols = invoiceInfoForIn!.taxEnterpriseMonthInvoiceForInVoList!.length;
    const titles = [
      '取票金额',
      '取票环比增长率',
      '取票同比增长率',
      '取票张数',
      '取票月数',
      '红冲发票张数占比',
      '红冲金额占比',
      '上游客户数量',
      '下游客户数量'
    ];
    for (var i = 0; i < rows; i++) {
      List<String> inner = [];
      for (var j = 0; j < cols + 1; j++) {
        if (j == 0) {
          inner.add(titles[i]);
        } else {
          final itemData =
              invoiceInfoForIn!.taxEnterpriseMonthInvoiceForInVoList![j - 1];
          if (i == 0) {
            // 开票金额
            if (itemData.taxEnterpriseInvoiceDateForInVo != null) {
              inner.add(
                  itemData.taxEnterpriseInvoiceDateForInVo!.invoiceAmount !=
                          null
                      ? formatLargeNumber(itemData
                          .taxEnterpriseInvoiceDateForInVo!.invoiceAmount!
                          .toDouble())
                      : '_');
            } else {
              inner.add('_');
            }
          } else if (i == 1) {
            // 开票环比增长率
            if (itemData.taxEnterpriseInvoiceDateForInVo != null) {
              inner.add(
                  '${itemData.taxEnterpriseInvoiceDateForInVo!.invoiceGrowthRate ?? '-'}%');
            } else {
              inner.add('-%');
            }
          } else if (i == 2) {
            // 开票同比增长率
            if (itemData.taxEnterpriseInvoiceDateForInVo != null) {
              inner.add(
                  '${itemData.taxEnterpriseInvoiceDateForInVo!.invoiceOverGrowthRate ?? '-'}%');
            } else {
              inner.add('-%');
            }
          } else if (i == 3) {
            // 开票张数
            if (itemData.taxEnterpriseInvoiceDateForInVo != null) {
              inner.add(
                  '${itemData.taxEnterpriseInvoiceDateForInVo!.invoiceCount ?? '_'}');
            } else {
              inner.add('_');
            }
          } else if (i == 4) {
            // 开票月数
            if (itemData.taxEnterpriseInvoiceDateForInVo != null) {
              inner.add(
                  itemData.taxEnterpriseInvoiceDateForInVo!.invoiceMonthsNum ??
                      '_');
            } else {
              inner.add('_');
            }
          } else if (i == 5) {
            // 红冲发票张数占比
            if (itemData.taxEnterpriseInvoiceDateForInVo != null) {
              inner.add(
                  '${itemData.taxEnterpriseInvoiceDateForInVo!.invoiceRedCountRate ?? '-'}%');
            } else {
              inner.add('-%');
            }
          } else if (i == 6) {
            // 红冲金额占比
            if (itemData.taxEnterpriseInvoiceDateForInVo != null) {
              inner.add(
                  '${itemData.taxEnterpriseInvoiceDateForInVo!.invoiceRedAmountRate ?? '-'}%');
            } else {
              inner.add('-%');
            }
          } else if (i == 7) {
            // 上游客户数量
            if (itemData.taxEnterpriseInvoiceDateForInVo != null) {
              inner.add(
                  '${itemData.taxEnterpriseInvoiceDateForInVo!.customerUpNum ?? '_'}');
            } else {
              inner.add('_');
            }
          } else {
            // 下游客户数量
            if (itemData.taxEnterpriseInvoiceDateForInVo != null) {
              inner.add(
                  '${itemData.taxEnterpriseInvoiceDateForInVo!.customerDownNum ?? '_'}');
            } else {
              inner.add('_');
            }
          }
        }
      }
      res.add(inner);
    }
    return res;
  }
}

@riverpod
class InvoiceInformationShowButton extends _$InvoiceInformationShowButton {
  @override
  bool build() {
    return false;
  }

  void hidden() {
    if (state != false) {
      state = false;
    }
  }

  void show() {
    if (state != true) {
      state = true;
    }
  }
}

@riverpod
class InvoiceInformation extends _$InvoiceInformation {
  @override
  InvoiceInformationViewModel build() {
    return InvoiceInformationViewModel();
  }

  Future<void> setType(
      InvoiceInformationViewType type, String shareCode, bool isExample) async {
    if (isExample) {
      if (type == InvoiceInformationViewType.issueInvoice) {
        state = await state.copyWith(
          pageStatus: PageStatus.success,
          type: type,
          invoiceInfo: example_InvoiceList,
        );
      } else if (type == InvoiceInformationViewType.declarationData) {
        state = await state.copyWith(
          pageStatus: PageStatus.success,
          type: type,
          applyAmount: example_DeclareList,
        );
      } else {
        state = await state.copyWith(
          pageStatus: PageStatus.success,
          type: type,
          invoiceInfoForIn: example_InvoiceInList,
        );
      }
    } else {
      try {
        /// 如果页面没有数据解开启加载状态
        if (state.hasData == false) {
          state = state.copyWith(pageStatus: PageStatus.loading);
        }
        state.pageStatus = PageStatus.loading;
        if (type == InvoiceInformationViewType.issueInvoice) {
          /// 开具发票
          final response = await QueryApi.issueInvoice(shareCode);
          if (response.data != null) {
            state = state.copyWith(
              pageStatus: PageStatus.success,
              type: type,
              invoiceInfo: response.data,
            );
          } else {
            state = state.copyWith(pageStatus: PageStatus.empty);
          }
        } else if (type == InvoiceInformationViewType.declarationData) {
          /// 申报数据
          final response = await QueryApi.declarationData(shareCode);
          if (response.data != null) {
            state = state.copyWith(
              pageStatus: PageStatus.success,
              type: type,
              applyAmount: response.data,
            );
          } else {
            state = state.copyWith(pageStatus: PageStatus.empty);
          }
        } else {
          /// 取得发票
          final response = await QueryApi.getInvoice(shareCode);
          if (response.data != null) {
            state = state.copyWith(
              pageStatus: PageStatus.success,
              type: type,
              invoiceInfoForIn: response.data,
            );
          } else {
            state = state.copyWith(pageStatus: PageStatus.empty);
          }
        }
      } catch (e) {
        state = state.copyWith(pageStatus: PageStatus.error);
        print(e);
      }
    }
  }
}
