import 'package:zrreport/common/index.dart';
import 'package:zrreport/common/models/taxes_info.dart';


 final example_DetailInfoModel = ReportDetailModel(
    taskId: "",
    dateStatus: true,
    openAccountBank: "",
    createTime: "2024-06-18",
    shareCode: "ASDFG",
    enterpriseId: "2",
    enterpriseName: "延安****有限公司",
    legalPerson: "韩**",
    age: 28,
    stockStatus: true,
    stockPercent: 100.0,
    registerDate: "2020-06-02",
    creditCode: "91610600MA6Y*****",
    industry: "其他未列明零售业",
    loginRegisterType: "**有限责任公司",
    registerAddress: "陕西省延安市宝塔区新城街道*********",
    phone: "1529111****",
    registerMonth: 4,
    qualification: "增值税一般**",
    legalChangeStatus: true,
    enterpriseChange: EnterpriseChange(
      id: "210",
      name: "投资人变更",
      beforeContent: "姓名: **",
      affterContent: "姓名: **",
      changeDate: "2024-01-05 00:00:00"
    ),
    qccEnterpriseChangeList: [
      EnterpriseChange(
                id: "210",
        name: "投资人（股权）变更",
        beforeContent: "姓名: **",
        affterContent: "姓名: **",
        changeDate: "2024-01-05 00:00:00",
      ),
      EnterpriseChange(  id: "210",
        name: "投资人（股权）变更",
        beforeContent: "姓名: **",
        affterContent: "姓名: **",
        changeDate: "2024-01-05 00:00:00",
        ),
     EnterpriseChange(
       id: "210",
        name: "投资人（股权）变更",
        beforeContent: "姓名: **",
        affterContent: "姓名: **",
        changeDate: "2024-01-05 00:00:00",
     ),
     EnterpriseChange(
       id: "210",
        name: "投资人（股权）变更",
        beforeContent: "姓名: **",
        affterContent: "姓名: **",
        changeDate: "2024-01-05 00:00:00",
     ),
    ],
    bankTaxRecord: "无",
    qccEnterprisePartnersList: [
   EnterprisePartner(
        stockName: "韩**",
        stockType: "自然人股东",
        stockPercent: "**",
        shouldCapi: "**",
        realCapi: "**",
        capiDate: "2024-06-18 19:49:06",
        shoudDate: "2024-06-18 19:49:06",
        finalBenefitPercent: "**",
      ),
      EnterprisePartner(
         stockName: "韩**",
        stockType: "自然人股东",
        stockPercent: "**",
        shouldCapi: "**",
         realCapi: "**",
        capiDate: "2024-06-18 19:49:06",
        shoudDate: "2024-06-18 19:49:06",
        finalBenefitPercent: "**",
      ),
       EnterprisePartner(
         stockName: "韩**",
        stockType: "自然人股东",
        stockPercent: "**",
        shouldCapi: "**",
         realCapi: "**",
        capiDate: "2024-06-18 19:49:06",
        shoudDate: "2024-06-18 19:49:06",
        finalBenefitPercent: "**",
      ),
       EnterprisePartner(
         stockName: "韩**",
        stockType: "自然人股东",
        stockPercent: "**",
        shouldCapi: "**",
         realCapi: "**",
        capiDate: "2024-06-18 19:49:06",
        shoudDate: "2024-06-18 19:49:06",
        finalBenefitPercent: "**",
      ),
       EnterprisePartner(
         stockName: "韩**",
        stockType: "自然人股东",
        stockPercent: "**",
        shouldCapi: "**",
         realCapi: "**",
        capiDate: "2024-06-18 19:49:06",
        shoudDate: "2024-06-18 19:49:06",
        finalBenefitPercent: "**",
      ),
       EnterprisePartner(
         stockName: "韩**",
        stockType: "自然人股东",
        stockPercent: "**",
        shouldCapi: "**",
         realCapi: "**",
        capiDate: "2024-06-18 19:49:06",
        shoudDate: "2024-06-18 19:49:06",
        finalBenefitPercent: "**",
      ),
    ], 
    businessScope: '', 
    source: '',
);


// 纳税信息-其他纳税相关信息
final example_TaxListModle = TaxesInfo(
    evaluationResult: "A",
    qualification: "简易办法征收一般纳税人",
    // 纳税人种类生效日期
    qualificationValidFrom: "2024-01-01",
    // 税务登记日期
    taxRegister: "2024-01-01",
    // 近12月欠税记录数
    taxDelinquencyRecordCountPast12Months: 0,
    // 近12个月最长连续0纳税申报月数
    last12MaxZeroTaxMonths: 0,
    unpaidTaxStatus: false,
    unpaidTaxAmount: null,
    creditEvaluationList: [
      CreditEvaluationList(
        id: "226337830094376960",
        enterpriseId: "69",
        evaluationYear: "2023",
        evaluationScore: 92,
        evaluationResult: "A",
        indicatorNames: [
          "010101.未按规定期限纳税申报（按税种按次计算）",
          "060303.发现少缴税款行为,作出补缴税款处理_补税金额1万元以上且占当年应纳税额不满1%，已补缴税款、加收滞纳金、缴纳罚款的",
        ],
      ),
      CreditEvaluationList(
        id: "226337830225580032",
        enterpriseId: "69",
        evaluationYear: "2022",
        evaluationScore: 97,
        evaluationResult: "A",
        indicatorNames: [
          "060303.发现少缴税款行为,作出补缴税款处理_补税金额1万元以上且占当年应纳税额不满1%，已补缴税款、加收滞纳金、缴纳罚款的",
        ],
      ),
    ],
    spiderFinanceInfosYearList: [
      SpiderFinanceInfosYearList(
        year: 2021,
        spiderFinanceInfoVo: SpiderFinanceInfoVo(
          totalAssets: "1394326",
          totalLiabilities: "10319",
          thisYearNetIncrease: "12264",
          thisYearFinalBalance: "280",
          ownerEquity: "3623",
          liabilitiesOwnerEquity: "1394326",
          businessIncome: "1165",
          businessProfit: "29234",
          totalProfit: "29166",
          netProfits: "0",
       ),
      ),
      SpiderFinanceInfosYearList(
        year: 2022,
        spiderFinanceInfoVo: SpiderFinanceInfoVo(
          totalAssets: "1322504",
          totalLiabilities: "9510104",
          thisYearNetIncrease: "-688882",
          thisYearFinalBalance: "211668",
          ownerEquity: "37149450",
          liabilitiesOwnerEquity: "1327",
          businessIncome: "1251427",
          businessProfit: "191973",
          totalProfit: "1895307",
          netProfits: "0",
        ),
      ),
      SpiderFinanceInfosYearList(
        year: 2023,
        spiderFinanceInfoVo: SpiderFinanceInfoVo(
          totalAssets: "*********",
          totalLiabilities: "105323.28",
          thisYearNetIncrease: "19408.81",
          thisYearFinalBalance: "231075.66",
          ownerEquity: "389505.44",
          liabilitiesOwnerEquity: "14434172",
          businessIncome: "13916.18",
          businessProfit: "29246.8",
          totalProfit: "30211.87",
          netProfits: "0",
        ),
      ),
    ],
    enterpriseMonthPaidList: [
      EnterpriseMonthPaidList(
        month: 3,
        taxEnterprisePaidDate: TaxEnterprisePaidDate(
          unpaidTaxAmount: null,
          taxSaleAmount: "301278.06",
          taxPaidAmount: "639.85",
          coiled0Months: "0",
          debtRatio: "73.21",
          profitRatio: "0.93",
          vatPayableAmount: "818149",
          taxIncomeAmount: null,
          taxWaiverAmount: "7025514",
          lateFeesAmount: null,
          lateFeesNum: 0,
        ),
      ),
      EnterpriseMonthPaidList(
        month: 6,
        taxEnterprisePaidDate: TaxEnterprisePaidDate(
          unpaidTaxAmount: null,
          taxSaleAmount: "530218",
          taxPaidAmount: "10370193",
          coiled0Months: "0",
          debtRatio: "72.91",
          profitRatio: "0.86",
          vatPayableAmount: "1352468",
          taxIncomeAmount: null,
          taxWaiverAmount: "702554",
          lateFeesAmount: null,
          lateFeesNum: 0,
      ),
      ),
      EnterpriseMonthPaidList(
        month: 12,
        taxEnterprisePaidDate: TaxEnterprisePaidDate(
          unpaidTaxAmount: null,
          taxSaleAmount: "12419083297.55",
          taxPaidAmount: "*********.28",
          coiled0Months: "0",
          debtRatio: "73.01",
          profitRatio: "2.38",
          vatPayableAmount: "29550",
          taxIncomeAmount: "139186",
          taxWaiverAmount: "703423",
          lateFeesAmount: null,
          lateFeesNum: 0,
        ),
      ),
      EnterpriseMonthPaidList(
        month: 24,
        taxEnterprisePaidDate: TaxEnterprisePaidDate(
          unpaidTaxAmount: null,
          taxSaleAmount: "23651335.9",
          taxPaidAmount: "*********",
          coiled0Months: "0",
          debtRatio: "71.91",
          profitRatio: "1.64",
          vatPayableAmount: "522576.49",
          taxIncomeAmount: "2643293.77",
          taxWaiverAmount: "201928.83",
          lateFeesAmount: "74459.96",
          lateFeesNum: 1,
        ),
      ),
    ],
    taxLateFeesList: [
      TaxLateFeesList(
        id: 1606,
        paymentAmount: 56,
        paymentDate: "2024-01-26",
        taxPeriodEnd: "2023-06-12",
        taxPeriodStart: "2023-06-12"
      ),
      TaxLateFeesList(
        id: 1607,
        paymentAmount: 56,
        paymentDate: "2024-01-26",
        taxPeriodEnd: "2023-06-12",
        taxPeriodStart: "2023-06-12"
      ),
      TaxLateFeesList(
        id: 1608,
        paymentAmount: 56,
        paymentDate: "2024-01-26",
        taxPeriodEnd: "2023-06-12",
        taxPeriodStart: "2023-06-12"
      ),
      TaxLateFeesList(
        id: 1609,
        paymentAmount: 56,
        paymentDate: "2024-01-26",
        taxPeriodEnd: "2023-06-12",
        taxPeriodStart: "2023-06-12"
      ),
      TaxLateFeesList(
        id: 1610,
        paymentAmount: 56,
        paymentDate: "2024-01-26",
        taxPeriodEnd: "2023-06-12",
        taxPeriodStart: "2023-06-12"
      ),
      TaxLateFeesList(
        id: 1611,
        paymentAmount: 56,
        paymentDate: "2024-01-26",
        taxPeriodEnd: "2023-06-12",
        taxPeriodStart: "2023-06-12"
      ),
      TaxLateFeesList(
        id: 1612,
        paymentAmount: 56,
        paymentDate: "2024-01-26",
        taxPeriodEnd: "2023-06-12",
        taxPeriodStart: "2023-06-12"
      ),
    ],
    // 税务违法违章信息
    lawRegulationViolationVoList:[
      LawRegulationViolationVoList(
        actionName: "违反税收管理1",
        infractionMeans: "未按照规定期限办理纳税申报和报送纳税资料1",
        registerDate: "2021-06-21 00:00:00",
        taxPeriodStart: "2021-05-01 00:00:00",
        taxPeriodEnd: "2021-05-31 00:00:00",
        processState: "处理完毕",
        caseState: "已完结1"
      ),
       LawRegulationViolationVoList(
        actionName: "违反税收管理2",
        infractionMeans: "未按照规定期限办理纳税申报和报送纳税资料2",
        registerDate: "2022-06-21 00:00:00",
        taxPeriodStart: "2022-05-01 00:00:00",
        taxPeriodEnd: "2022-05-31 00:00:00",
        processState: "处理完毕2",
        caseState: "已完结2"
       )
    ]
);


//纳税信息-近三年纳税信息-正税
// ignore: non_constant_identifier_names
List<Datum> example_ThreeYearsTaxInfo = [
    Datum(
      total: "893524.35",
      year: 2022,
      monthData: [
        MonthDatum_Tax(
          name: "1",
          value: "51946.74",
        ),
        MonthDatum_Tax(
          name: "2",
          value: "39401.1",
        ),
        MonthDatum_Tax(
          name: "3",
          value: "105250.43",
        ),
        MonthDatum_Tax(
          name: "4",
          value: "43353",
        ),
        MonthDatum_Tax(
          name: "5",
          value: "53147.88",
        ),
        MonthDatum_Tax(
          name: "6",
          value: "244990.97",
        ),
        MonthDatum_Tax(
          name: "7",
          value: "19023.08",
        ),
        MonthDatum_Tax(
          name: "8",
          value: "19160.59",
        ),
        MonthDatum_Tax(
          name: "9",
          value: "168684.92",
        ),
        MonthDatum_Tax(
          name: "10",
          value: "27378.86",
        ),
        MonthDatum_Tax(
          name: "11",
          value: "5392.05",
        ),
        MonthDatum_Tax(
          name: "12",
          value: "115794.73",
        ),
      ],
    ),
    Datum(
      total: "716898.62",
      year: 2023,
      monthData: [
        MonthDatum_Tax(
          name: "1",
          value: "23432.57",
        ),
        MonthDatum_Tax(
          name: "2",
          value: "4661.12",
        ),
        MonthDatum_Tax(
          name: "3",
          value: "232082",
        ),
        MonthDatum_Tax(
          name: "4",
          value: "2235.73",
        ),
        MonthDatum_Tax(
          name: "5",
          value: "4420.09",
        ),
        MonthDatum_Tax(
          name: "6",
          value: "99119.3",
        ),
        MonthDatum_Tax(
          name: "7",
          value: "1824.62",
        ),
        MonthDatum_Tax(
          name: "8",
          value: "2367.4",
        ),
        MonthDatum_Tax(
          name: "9",
          value: "149933.58",
        ),
        MonthDatum_Tax(
          name: "10",
          value: "6473.86",
        ),
        MonthDatum_Tax(
          name: "11",
          value: "69587.52",
        ),
        MonthDatum_Tax(
          name: "12",
          value: "120760.83",
        ),
      ],
    ),
    Datum(
      total: "330959.11",
      year: 2024,
      monthData: [
         MonthDatum_Tax(
          name: "1",
          value: "58622.72",
         ),
         MonthDatum_Tax(
          name: "2",
          value: "25641.91",
         ),
         MonthDatum_Tax(
          name: "3",
          value: "92903.81",
         ),
         MonthDatum_Tax(
          name: "4",
          value: "38694.72",
         ),
         MonthDatum_Tax(
          name: "5",
          value: "15601.81",
         ),
         MonthDatum_Tax(
          name: "6",
          value: "99494.14",
         ),
         MonthDatum_Tax(
          name: "7",
          value: null,
         ),
         MonthDatum_Tax(
          name: "8",
          value: null,
         ),
          MonthDatum_Tax(
          name: "9",
          value: null,
         ),
         MonthDatum_Tax(
          name: "10",
          value: null,
         ),
          MonthDatum_Tax(
          name: "11",
          value: null,
         ),
         MonthDatum_Tax(
          name: "12",
          value: null,
         ),
      ],
    ),
];



//开票  开票数据
// ignore: non_constant_identifier_names
final example_InvoiceList = EnterpriseInvoiceInfo(
    billingRecords: true,
    // taxGrowthRateMonth3: "-15.34",
    // taxGrowthRateMonth6: "92.07",
    // taxGrowthRateMonth12: "863.6500",
    taxVoidRateMonth12: "11.58",
    // customerUpNumMonth12: "1853",
    // customerDownNumMonth12: "3925",
    taxAmountGrowthRateMonth12: "863.6500",
    taxOutputNotInvoicedMonth12: "287",
    breakMonthsSum12: 0,
    breakMonthsSum: 2,
    billingDaysSum: "7902495.34",
    // billingMonthsSum3: "26960322.98",
    // billingMonthsSum6: "58806027.27",
    // billingMonthsSum12: "89422661.86",
    // billingMonthsSum24: "98702281.48",
    // billingMonthsSum36: "99494162.68",
    // declareSum12: 12,
    // lastYearBillingSum: "49532150.87",
    // taxRedAmountRateMonth12: "4.00",
    // applySum12: "12",
    // applySum24: "12",
    // taxRedCountRateMonth12: "13.00",

    enterpriseMonthApplyList: [
      EnterpriseMonthApplyList(
        month: 3,
        taxEnterpriseApplyDate: TaxEnterpriseApplyDate(
          invoiceAmount: "2067075.48",
          invoiceGrowthRate: "-29.66",
          invoiceOverGrowthRate: "",
          invoiceCount: 58,
          invoiceMonthsNum: "2",
          breakMonthsSum: "",
          invoiceRedCountRate: "5.41",
          invoiceRedAmountRate: "0.95",
          customerUpNum: 9,
          customerDownNum: 8,
        ),
      ),
      EnterpriseMonthApplyList(
        month: 6,
        taxEnterpriseApplyDate: TaxEnterpriseApplyDate(
          invoiceAmount: "5005600.05",
          invoiceGrowthRate: "-27.5",
          invoiceCount: 194,
          invoiceMonthsNum: "5",
          invoiceRedCountRate: "6.55",
          invoiceRedAmountRate: "8.85",
          customerUpNum: 14,
          customerDownNum: 12,
        ),
      ),
      EnterpriseMonthApplyList(
        month: 12,
        taxEnterpriseApplyDate: TaxEnterpriseApplyDate(
          invoiceAmount: "11910253.08",
          invoiceGrowthRate: "-3.34",
          invoiceCount: 386,
          invoiceMonthsNum: "11",
          invoiceRedCountRate: "4.24",
          invoiceRedAmountRate: "5.74",
          customerUpNum: 19,
          customerDownNum: 15,
        ),
      ),
      EnterpriseMonthApplyList(
        month: 24,
        taxEnterpriseApplyDate: TaxEnterpriseApplyDate(
          invoiceAmount: "24232424.4",
          invoiceGrowthRate: null,
          invoiceCount: 727,
          invoiceMonthsNum: "23",
          invoiceRedCountRate: "10.28",
          invoiceRedAmountRate: "18.15",
          customerUpNum: 27,
          customerDownNum: 17,
        ),
      ),
    ],
    invoiceInfoDataList: [
      InvoiceInfoDataList(
        total: "16203124.85",
        year: 2022,
        monthData: [
          MonthDatum(
            name: "1",
            value: "968042.75",
          ),
          MonthDatum(
            name: "2",
            value: "2783791.63",
          ),
          MonthDatum(
            name: "3",
            value: "493695.11",
          ),
          MonthDatum(
            name: "4",
            value: "574348.42",
          ),
          MonthDatum(
            name: "5",
            value: "896207.71",
          ),
          MonthDatum(
            name: "6",
            value: "2079529.33",
          ),
          MonthDatum(
            name: "7",
            value: "995050.06",
          ),
          MonthDatum(
            name: "8",
            value: "982205.91",
          ),
          MonthDatum(
            name: "9",
            value: "2076904.45",
          ),
          MonthDatum(
            name: "10",
            value: "1085284.08",
          ),
          MonthDatum(
            name: "11",
            value: "1251890.91",
          ),
          MonthDatum(
            name: "12",
            value: "2016174.49",
          ),
        ],
      ),
      InvoiceInfoDataList(
        total: "11180902.12",
        year: 2023,
        monthData: [
          MonthDatum(
            name: "1",
            value: "815665.43",
          ),
          MonthDatum(
            name: "2",
            value: "716185.35",
          ),
          MonthDatum(
            name: "3",
            value: "2147680.41",
          ),
          MonthDatum(
            name: "4",
            value: "531277.21",
          ),
          MonthDatum(
            name: "5",
            value: "921573.21",
          ),
          MonthDatum(
            name: "6",
            value: "649689.89",
          ),
          MonthDatum(
            name: "7",
            value: "232389.21",
          ),
          MonthDatum(
            name: "8",
            value: "782670.36",
          ),
          MonthDatum(
            name: "9",
            value: "1162460.24",
          ),
          MonthDatum(
            name: "10",
            value: "1376094.45",
          ),
          MonthDatum(
            name: "11",
            value: "1734055.34",
          ),
          MonthDatum(
            name: "12",
            value: "111161.02",
          ),
        ],
      ),
      InvoiceInfoDataList(
        total: "8688942.27",
        year: 2024,
        monthData: [
           MonthDatum(
            name: "1",
            value: "2742282.25",
           ),
           MonthDatum(
            name: "2",
            value: "698380.79",
           ),
           MonthDatum(
            name: "3",
            value: "435009.68",
           ),
           MonthDatum(
            name: "4",
            value: "2151548.41",
           ),
           MonthDatum(
            name: "5",
            value: "271818.99",
           ),
           MonthDatum(
            name: "6",
            value: "334432.46",
           ),
           MonthDatum(
            name: "7",
            value: "2055469.69",
           ),
           MonthDatum(
            name: "8",
            value: null,
           ),
            MonthDatum(
            name: "9",
            value: null,
           ),
           MonthDatum(
            name: "10",
            value: null,
           ),
           MonthDatum(
            name: "11",
            value: null,
           ),
           MonthDatum(
            name: "12",
            value: null,
           ),
        ],
      ),
    ],
);







// 开票 申报数据
final example_DeclareList = EnterpriseApplyAmount(
    applyRecords: true,
    applyAmountMonths1: "10303",
    applySum12: "12",
    applyInfoDataList: [
      ApplyDataList(
        total: "11887",
        year: 2022,
        monthData: [
          MonthDatum(
            name: "1",
            value: "19916",
          ),
          MonthDatum(
            name: "2",
            value: "2977",
          ),
          MonthDatum(
            name: "3",
            value: "500857",
          ),
          MonthDatum(
            name: "4",
            value: "6873174",
          ),
          MonthDatum(
            name: "5",
            value: "10729",
          ),
          MonthDatum(
            name: "6",
            value: "141393",
          ),
          MonthDatum(
            name: "7",
            value: "8520096",
          ),
          MonthDatum(
            name: "8",
            value: "105965",
          ),
          MonthDatum(
            name: "9",
            value: "11100",
          ),
          MonthDatum(
            name: "10",
            value: "7125782",
          ),
          MonthDatum(
            name: "11",
            value: "95913256",
          ),
          MonthDatum(
            name: "12",
            value: "1229699",
          ),
        ],
      ),
      ApplyDataList(
        total: "119007",
        year: 2023,
        monthData: [
           MonthDatum(
            name: "1",
            value: "140685",
           ),
           MonthDatum(
            name: "2",
            value: "258893",
           ),
           MonthDatum(
            name: "3",
            value: "598068",
           ),
           MonthDatum(
            name: "4",
            value: "10119791",
           ),
           MonthDatum(
            name: "5",
            value: "94944",
           ),
           MonthDatum(
            name: "6",
            value: "93287",
           ),
           MonthDatum(
            name: "7",
            value: "941932",
           ),
           MonthDatum(
            name: "8",
            value: "112078",
           ),
           MonthDatum(
            name: "9",
            value: "11748805",
           ),
           MonthDatum(
            name: "10",
            value: "82750",
           ),
           MonthDatum(
            name: "11",
            value: "1087879",
           ),
           MonthDatum(
            name: "12",
            value: "158971",
           ),
        ],
      ),
      ApplyDataList(
        total: "7739111",
        year: 2024,
        monthData: [
          MonthDatum(
            name: "1",
            value: "2032306",
          ),
          MonthDatum(
            name: "2",
            value: "40460692",
          ),
          MonthDatum(
            name: "3",
            value: "3546",
          ),
          MonthDatum(
            name: "4",
            value: "84482143",
          ),
          MonthDatum(
            name: "5",
            value: "10899648",
          ),
          MonthDatum(
            name: "6",
            value: "1064100",
          ),
          MonthDatum(
            name: "7",
            value: "91832",
          ),
          MonthDatum(
            name: "8",
            value: "1030371",
          ),
           MonthDatum(
            name: "9",
            value: null,
          ),
           MonthDatum(
            name: "10",
            value: null,
          ),
           MonthDatum(
            name: "11",
            value: null,
          ),
           MonthDatum(
            name: "12",
            value: null,
          ),
        ],
      ),
    ],
    applySalesDataList: [
      ApplyDataList(
        total: "-157997",
        year: 2022,
        monthData: [
          MonthDatum(
            name: "1",
            value: "69096",
          ),
          MonthDatum(
            name: "2",
            value: "120002",
          ),
          MonthDatum(
            name: "3",
            value: "19699",
          ),
          MonthDatum(
            name: "4",
            value: "8801",
          ),
          MonthDatum(
            name: "5",
            value: "-18903",
          ),
          MonthDatum(
            name: "6",
            value: "1199",
          ),
          MonthDatum(
            name: "7",
            value: "57551",
          ),
          MonthDatum(
            name: "8",
            value: "103316",
          ),
          MonthDatum(
            name: "9",
            value: "1221",
          ),
          MonthDatum(
            name: "10",
            value: "486.66",
          ),
          MonthDatum(
            name: "11",
            value: "2171",
          ),
          MonthDatum(
            name: "12",
            value: "5612",
          ),
        ],
      ),
      ApplyDataList(
        total: "6674",
        year: 2023,
        monthData: [
           MonthDatum(
            name: "1",
            value: "38116",
           ),
           MonthDatum(
            name: "2",
            value: "33934",
           ),
           MonthDatum(
            name: "3",
            value: "30058",
           ),
           MonthDatum(
            name: "4",
            value: "19",
           ),
           MonthDatum(
            name: "5",
            value: "115",
           ),
           MonthDatum(
            name: "6",
            value: "4067",
           ),
           MonthDatum(
            name: "7",
            value: "11427",
           ),
           MonthDatum(
            name: "8",
            value: "42752",
           ),
           MonthDatum(
            name: "9",
            value: "2950",
           ),
           MonthDatum(
            name: "10",
            value: "534",
           ),
           MonthDatum(
            name: "11",
            value: "16156",
           ),
           MonthDatum(
            name: "12",
            value: "574",
           ),
        ],
      ),
      ApplyDataList(
        total: "1147109.76",
        year: 2024,
        monthData: [
          MonthDatum(
            name: "1",
            value: "5845",
          ),
          MonthDatum(
            name: "2",
            value: "0",
          ),
          MonthDatum(
            name: "3",
            value: "7574",
          ),
          MonthDatum(
            name: "4",
            value: "21904",
          ),
          MonthDatum(
            name: "5",
            value: "3544",
          ),
          MonthDatum(
            name: "6",
            value: "1178",
          ),
          MonthDatum(
            name: "7",
            value: "255",
          ),
          MonthDatum(
            name: "8",
            value: "6577",
          ),
          MonthDatum(
            name: "9",
            value: null,
          ),
          MonthDatum(
            name: "10",
            value: null,
          ),
          MonthDatum(
            name: "11",
            value: null,
          ),
          MonthDatum(
            name: "12",
            value: null,
          ),
        ],
      ),
    ],
    enterpriseMonthApplyList: [
      EnterpriseMonthApplyList1(
        month: 3,
        taxEnterpriseApplyDate: TaxEnterpriseApplyDate1(
          applyAmount: "3012",
          applyGrowthRate: "31.6",
          applyOverGrowthRate: null,
        ),
      ),
      EnterpriseMonthApplyList1(
        month: 6,
        taxEnterpriseApplyDate: TaxEnterpriseApplyDate1(
          applyAmount: "5302",
          applyGrowthRate: "-25.5",
          applyOverGrowthRate: null,
        ),
      ),
      EnterpriseMonthApplyList1(
        month: 12,
        taxEnterpriseApplyDate: TaxEnterpriseApplyDate1(
          applyAmount: "12419083",
          applyGrowthRate: "10.57",
          applyOverGrowthRate: null,
        ),
      ),
      EnterpriseMonthApplyList1(
        month: 24,
        taxEnterpriseApplyDate: TaxEnterpriseApplyDate1(
          applyAmount: "236513",
          applyGrowthRate: null,
          applyOverGrowthRate: null,
        ),
      ),
    ],
);



/// 取得发票
// ignore: non_constant_identifier_names
final example_InvoiceInList = EnterpriseInvoiceInfoForIn(
    billingRecords: true,
    // taxGrowthRateMonth3: "-15.34",
    // taxGrowthRateMonth6: "92.07",
    // taxGrowthRateMonth12: "863.6500",
    taxVoidRateMonth12: "11.58",
    // customerUpNumMonth12: "1853",
    // customerDownNumMonth12: "3925",
    taxAmountGrowthRateMonth12: "863.6500",
    taxOutputNotInvoicedMonth12: "287",
    breakMonthsSum12: 0,
    breakMonthsSum: 2,
    billingDaysSum: "7902495.34",
    // billingMonthsSum3: "26960322.98",
    // billingMonthsSum6: "58806027.27",
    // billingMonthsSum12: "89422661.86",
    // billingMonthsSum24: "98702281.48",
    // billingMonthsSum36: "99494162.68",
    // declareSum12: 12,
    // lastYearBillingSum: "49532150.87",
    // taxRedAmountRateMonth12: "4.00",
    // applySum12: "12",
    // applySum24: "12",
    // taxRedCountRateMonth12: "13.00",

    taxEnterpriseMonthInvoiceForInVoList: [
      TaxEnterpriseMonthInvoiceForInVoList(
        month: 3,
        taxEnterpriseInvoiceDateForInVo: TaxEnterpriseInvoiceDateForInVo(
          invoiceAmount: "2067075.48",
          invoiceGrowthRate: "-29.66",
          invoiceOverGrowthRate: "",
          invoiceCount: 58,
          invoiceMonthsNum: "2",
          breakMonthsSum: "",
          invoiceRedCountRate: "5.41",
          invoiceRedAmountRate: "0.95",
          customerUpNum: "9",
          customerDownNum: "8",
        ),
      ),
      TaxEnterpriseMonthInvoiceForInVoList(
        month: 6,
        taxEnterpriseInvoiceDateForInVo: TaxEnterpriseInvoiceDateForInVo(
          invoiceAmount: "5005600.05",
          invoiceGrowthRate: "-27.5",
          invoiceCount: 194,
          invoiceMonthsNum: "5",
          invoiceRedCountRate: "6.55",
          invoiceRedAmountRate: "8.85",
          customerUpNum: "14",
          customerDownNum: "12",
        ),
      ),
      TaxEnterpriseMonthInvoiceForInVoList(
        month: 12,
        taxEnterpriseInvoiceDateForInVo: TaxEnterpriseInvoiceDateForInVo(
          invoiceAmount: "11910253.08",
          invoiceGrowthRate: "-3.34",
          invoiceCount: 386,
          invoiceMonthsNum: "11",
          invoiceRedCountRate: "4.24",
          invoiceRedAmountRate: "5.74",
          customerUpNum: "19",
          customerDownNum: "15",
        ),
      ),
      TaxEnterpriseMonthInvoiceForInVoList(
        month: 24,
        taxEnterpriseInvoiceDateForInVo: TaxEnterpriseInvoiceDateForInVo(
          invoiceAmount: "24232424.4",
          invoiceGrowthRate: null,
          invoiceCount: 727,
          invoiceMonthsNum: "23",
          invoiceRedCountRate: "10.28",
          invoiceRedAmountRate: "18.15",
          customerUpNum: "27",
          customerDownNum: "17",
        ),
      ),
    ],
    invoiceForInDataList: [
      InvoiceForInDataList(
        total: "16203124.85",
        year: 2022,
        monthData: [
          MonthDatum(
            name: "1",
            value: "968042.75",
          ),
          MonthDatum(
            name: "2",
            value: "2783791.63",
          ),
          MonthDatum(
            name: "3",
            value: "493695.11",
          ),
          MonthDatum(
            name: "4",
            value: "574348.42",
          ),
          MonthDatum(
            name: "5",
            value: "896207.71",
          ),
          MonthDatum(
            name: "6",
            value: "2079529.33",
          ),
          MonthDatum(
            name: "7",
            value: "995050.06",
          ),
          MonthDatum(
            name: "8",
            value: "982205.91",
          ),
          MonthDatum(
            name: "9",
            value: "2076904.45",
          ),
          MonthDatum(
            name: "10",
            value: "1085284.08",
          ),
          MonthDatum(
            name: "11",
            value: "1251890.91",
          ),
          MonthDatum(
            name: "12",
            value: "2016174.49",
          ),
        ],
      ),
      InvoiceForInDataList(
        total: "11180902.12",
        year: 2023,
        monthData: [
          MonthDatum(
            name: "1",
            value: "815665.43",
          ),
          MonthDatum(
            name: "2",
            value: "716185.35",
          ),
          MonthDatum(
            name: "3",
            value: "2147680.41",
          ),
          MonthDatum(
            name: "4",
            value: "531277.21",
          ),
          MonthDatum(
            name: "5",
            value: "921573.21",
          ),
          MonthDatum(
            name: "6",
            value: "649689.89",
          ),
          MonthDatum(
            name: "7",
            value: "232389.21",
          ),
          MonthDatum(
            name: "8",
            value: "782670.36",
          ),
          MonthDatum(
            name: "9",
            value: "1162460.24",
          ),
          MonthDatum(
            name: "10",
            value: "1376094.45",
          ),
          MonthDatum(
            name: "11",
            value: "1734055.34",
          ),
          MonthDatum(
            name: "12",
            value: "111161.02",
          ),
        ],
      ),
      InvoiceForInDataList(
        total: "8688942.27",
        year: 2024,
        monthData: [
           MonthDatum(
            name: "1",
            value: "2742282.25",
           ),
           MonthDatum(
            name: "2",
            value: "698380.79",
           ),
           MonthDatum(
            name: "3",
            value: "435009.68",
           ),
           MonthDatum(
            name: "4",
            value: "2151548.41",
           ),
           MonthDatum(
            name: "5",
            value: "271818.99",
           ),
           MonthDatum(
            name: "6",
            value: "334432.46",
           ),
           MonthDatum(
            name: "7",
            value: "2055469.69",
           ),
           MonthDatum(
            name: "8",
            value: null,
           ),
           MonthDatum(
            name: "9",
            value: null,
           ),
           MonthDatum(
            name: "10",
            value: null,
           ),
           MonthDatum(
            name: "11",
            value: null,
           ),
           MonthDatum(
            name: "12",
            value: null,
           ),
        ],
      ),
    ],
);





//前十供应商
// ignore: non_constant_identifier_names
final example_ShoperInfo = SupplierInformation(
    paidTaxesDataList: [
      DataList(
        sort: 1,
        queryType: "取得发票",
        buySaleName: "******",
        sumAmount: "*********.33",
        amountRate: "92.93",
        relatedStatus: false,
        buySaleCode: "91110000*********",
      ),
      DataList(
        sort: 2,
        queryType: "取得发票",
        buySaleName: "******",
        sumAmount: "5168003.15",
        amountRate: "1.72",
        relatedStatus: false,
        buySaleCode: "91110000710935214B",
      ),
      DataList(
        sort: 3,
        queryType: "取得发票",
        buySaleName: "******",
        sumAmount: "2061658.82",
        amountRate: "0.69",
        relatedStatus: false,
        buySaleCode: "91632500927160659L",
      ),
      DataList(
        sort: 4,
        queryType: "取得发票",
        buySaleName: "******",
        sumAmount: "1521269.03",
        amountRate: "0.51",
        relatedStatus: false,
        buySaleCode: "",
      ),
      DataList(
        sort: 5,
        queryType: "取得发票",
        buySaleName: "******",
        sumAmount: "1386148.55",
        amountRate: "0.46",
        relatedStatus: false,
        buySaleCode: "91630123MA7539Q431",
      ),
      DataList(
        sort: 6,
        queryType: "取得发票",
        buySaleName: "******",
        sumAmount: "1034541.29",
        amountRate: "0.34",
        relatedStatus: false,
        buySaleCode: "91420600MA48Y1U86R",
      ),
      DataList(
        sort: 7,
        queryType: "取得发票",
        buySaleName: "******",
        sumAmount: "972800.54",
        amountRate: "0.32",
        relatedStatus: false,
        buySaleCode: "33009100DK00416",
      ),
      DataList(
        sort: 8,
        queryType: "取得发票",
        buySaleName: "******",
        sumAmount: "831257.13",
        amountRate: "0.28",
        relatedStatus: false,
        buySaleCode: "91632500927160122F",
      ),
      DataList(
        sort: 9,
        queryType: "取得发票",
        buySaleName: "******",
        sumAmount: "527310.07",
        amountRate: "0.18",
        relatedStatus: false,
        buySaleCode: "91500107203106163U",
      ),
      DataList(
        sort: 10,
        queryType: "取得发票",
        buySaleName: "**********",
        sumAmount: "522935.78",
        amountRate: "0.17",
        relatedStatus: false,
        buySaleCode: "91410100MA3X97M6XP",
      ),
    ],
    redInvoiceInfoDataList: [
     DataList( 
        sort: 1,
        queryType: "开具发票",
        buySaleName: "******",
        sumAmount: "32703989.21",
        amountRate: "64.61",
        relatedStatus: false,
        buySaleCode: "91630000226581557H",
     ),
      DataList(
        sort: 2,
        queryType: "开具发票",
        buySaleName: "******",
        sumAmount: "2233779.43",
        amountRate: "4.41",
        relatedStatus: false,
        buySaleCode: "91610623MA6YMMAM2X",
      ),
      DataList(
        sort: 3,
        queryType: "开具发票",
        buySaleName: "******",
        sumAmount: "2114159.33",
        amountRate: "4.18",
        relatedStatus: false,
        buySaleCode: "12650000457601578U",
      ),
      DataList(
        sort: 4,
        queryType: "开具发票",
        buySaleName: "******",
        sumAmount: "1317877.89",
        amountRate: "2.60",
        relatedStatus: false,
        buySaleCode: "91110108575170756M",
      ),
      DataList(
        sort: 5,
        queryType: "开具发票",
        buySaleName: "******",
        sumAmount: "1018484.65",
        amountRate: "2.01",
        relatedStatus: false,
        buySaleCode: "91141033MA0HDDHJ2U",
      ),
      DataList(
        sort: 6,
        queryType: "开具发票",
        buySaleName: "******",
        sumAmount: "947118.99",
        amountRate: "1.87",
        relatedStatus: false,
        buySaleCode: "91652922MA781XEL3D",
      ),
      DataList(
        sort: 7,
        queryType: "开具发票",
        buySaleName: "******",
        sumAmount: "849809.10",
        amountRate: "1.68",
        relatedStatus: false,
        buySaleCode: "91653101L111123188",
      ),
      DataList(
        sort: 8,
        queryType: "开具发票",
        buySaleName: "******",
        sumAmount: "662669.59",
        amountRate: "1.31",
        relatedStatus: false,
        buySaleCode: "91650100731796276R",
      ),
      DataList(
        sort: 9,
        queryType: "开具发票",
        buySaleName: "******",
        sumAmount: "659551.12",
        amountRate: "1.30",
        relatedStatus: false,
        buySaleCode: "52650104098173558D",
      ),
      DataList(
        sort: 10,
        queryType: "开具发票",
        buySaleName: "******",
        sumAmount: "631192.66",
        amountRate: "1.25",
        relatedStatus: false,
        buySaleCode: "91130729MA0E1B6F6E",
      ),
    ],
);