import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/branch/bank_contacts/bank_contacts_page.dart';
import 'provider/bank_branch_provider.dart';
import 'model/bank_branch_model.dart';

/// 银行网点列表组件
class BankBranchList extends ConsumerWidget {
  BankBranchList({super.key});

  ScrollDirection scrollDirection = ScrollDirection.idle;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(bankBranchNotifierProvider);
    final notifier = ref.watch(bankBranchNotifierProvider.notifier);

    // 如果没有数据，显示空视图
    if (state.branchList.list.isEmpty) {
      return _buildEmptyView(state, notifier);
    }

    return EasyRefresh(
      onRefresh: () async {
        final result = await notifier.loadFirstPage();
        return result ? IndicatorResult.success : IndicatorResult.fail;
      },
      onLoad: notifier.hasMorePage
          ? () async {
              final result = await notifier.loadMorePage();
              return result ? IndicatorResult.success : IndicatorResult.fail;
            }
          : null,
      child: NotificationListener(
        onNotification: (ScrollNotification notification) {
          if (notification is UserScrollNotification) {
            if (scrollDirection == ScrollDirection.idle &&
                notification.direction != scrollDirection) {
              print('列表开始滚动 ${notification.direction}');
              FocusManager.instance.primaryFocus?.unfocus();
            }
            scrollDirection = notification.direction;
          }
          return false;
        },
        child: ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          itemCount: state.branchList.list.length,
          itemBuilder: (context, index) {
            final branch = state.branchList.list[index];
            return _BankBranchCard(branch: branch);
          },
        ),
      ),
    );
  }

  Widget _buildEmptyView(BankBranchState state, BankBranchNotifier notifier) {
    if (state.isLoading) {
      return LoadingWidget();
    }
    if (state.hasError) {
      return Center(
        child: ErrorStatusWidget(
            text: state.errorMessage ?? '',
            onAttempt: () => notifier.loadFirstPage()),
      );
    }
    return Center(
      child: EmptyWidget(onAttempt: () => notifier.loadFirstPage()),
    );
  }
}

/// 银行网点卡片组件
class _BankBranchCard extends StatelessWidget {
  final BankBranch branch;

  const _BankBranchCard({
    required this.branch,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => BankContactsPage(
              bankId: branch.id,
              bankName: branch.name,
            ),
          ),
        );
      },
      child: Card(
        margin: const EdgeInsets.symmetric(vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        elevation: 0,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(5),
                child: CachedNetworkImage(
                    imageUrl: branch.pic ?? '', height: 44, width: 44),
              ),
              SizedBox(width: 10),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      branch.name,
                      style: const TextStyle(
                        color: AppColors.textColor3,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '全国服务热线:${branch.telephone}',
                      style: const TextStyle(
                        color: AppColors.textColor9,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              Spacer(),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Color(0xFFD8D8D8),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
