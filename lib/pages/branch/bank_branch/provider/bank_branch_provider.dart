import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:zrreport/common/models/pagination.dart';
import 'package:zrreport/common/api/credit_service.dart';
import '../../provider/location_provider.dart';
import '../model/bank_branch_model.dart';

part 'bank_branch_provider.g.dart';
part 'bank_branch_provider.freezed.dart';

/// 银行网点查询状态
@freezed
abstract class BankBranchState with _$BankBranchState {
  const factory BankBranchState({
    @Default(false) bool isLoading,
    @Default(false) bool hasError,
    String? errorMessage,
    @Default(
        Pagination(pageNum: 0, pageSize: 0, totalPage: 0, total: 0, list: []))
    Pagination<BankBranch> branchList,
  }) = _BankBranchState;
}

/// 银行网点查询 Provider
@Riverpod()
class BankBranchNotifier extends _$BankBranchNotifier {
  int get _page => state.branchList.pageNum;
  static const int _pageSize = 20;

  bool get hasMorePage => state.branchList.pageNum < state.branchList.totalPage;

  String? _keyword;
  String get keyword => _keyword ?? "";

  @override
  BankBranchState build() {
    // 初始化时加载第一页数据
    Future.microtask(() => loadFirstPage());

    return const BankBranchState();
  }

  BankBranchState? originState;

  void revert() {
    if (originState != null) {
      // state = originState!;
      state = state.copyWith(
          branchList: originState!.branchList,
          isLoading: false,
          errorMessage: null,
          hasError: false);
      originState = null;
    }
  }

  void update(Pagination<BankBranch> pagination, String keyword) {
    _keyword = keyword;
    if (originState == null && state.branchList.list.isNotEmpty) {
      originState = state;
    }

    state = state.copyWith(
        branchList: pagination,
        isLoading: false,
        errorMessage: null,
        hasError: false);

    // state = BankBranchState(
    //     branchList: pagination,
    //     isLoading: false,
    //     errorMessage: null,
    //     hasError: false);
  }

  /// 加载第一页
  Future<bool> loadFirstPage() async {
    return await loadBranches(1);
  }

  /// 加载下一页
  Future<bool> loadMorePage() async {
    if (!hasMorePage || state.isLoading) return false;
    return await loadBranches(_page + 1);
  }

  /// 加载网点数据
  Future<bool> loadBranches(int pageNum) async {
    if (state.isLoading) return false;

    try {
      state = state.copyWith(
        isLoading: true,
        hasError: false,
        errorMessage: null,
      );

      final response = await BranchApi.getBankBranch(
          pageNum: pageNum, pageSize: _pageSize, keyword: _keyword);

      final pagination = response.data;
      if (pagination != null) {
        final newList = pagination.list;
        final oldList = pageNum != 1 ? state.branchList.list : [];

        state = state.copyWith(
          branchList: Pagination(
            pageNum: pagination.pageNum,
            pageSize: pagination.pageSize,
            totalPage: pagination.totalPage,
            total: response.data!.total,
            list: [...oldList, ...newList],
          ),
        );
        return true;
      } else {
        state = state.copyWith(
          hasError: true,
          errorMessage: response.message,
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        hasError: true,
        errorMessage: '$e',
      );
      return false;
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }
}
