// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bank_branch_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$bankBranchNotifierHash() =>
    r'd3356d60bea038ea04b8102f073637a57b564778';

/// 银行网点查询 Provider
///
/// Copied from [BankBranchNotifier].
@ProviderFor(BankBranchNotifier)
final bankBranchNotifierProvider =
    AutoDisposeNotifierProvider<BankBranchNotifier, BankBranchState>.internal(
  BankBranchNotifier.new,
  name: r'bankBranchNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$bankBranchNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BankBranchNotifier = AutoDisposeNotifier<BankBranchState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
