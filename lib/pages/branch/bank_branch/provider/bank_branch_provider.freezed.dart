// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bank_branch_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BankBranchState {
  bool get isLoading;
  bool get hasError;
  String? get errorMessage;
  Pagination<BankBranch> get branchList;

  /// Create a copy of BankBranchState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $BankBranchStateCopyWith<BankBranchState> get copyWith =>
      _$BankBranchStateCopyWithImpl<BankBranchState>(
          this as BankBranchState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is BankBranchState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.hasError, hasError) ||
                other.hasError == hasError) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.branchList, branchList) ||
                other.branchList == branchList));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, isLoading, hasError, errorMessage, branchList);

  @override
  String toString() {
    return 'BankBranchState(isLoading: $isLoading, hasError: $hasError, errorMessage: $errorMessage, branchList: $branchList)';
  }
}

/// @nodoc
abstract mixin class $BankBranchStateCopyWith<$Res> {
  factory $BankBranchStateCopyWith(
          BankBranchState value, $Res Function(BankBranchState) _then) =
      _$BankBranchStateCopyWithImpl;
  @useResult
  $Res call(
      {bool isLoading,
      bool hasError,
      String? errorMessage,
      Pagination<BankBranch> branchList});
}

/// @nodoc
class _$BankBranchStateCopyWithImpl<$Res>
    implements $BankBranchStateCopyWith<$Res> {
  _$BankBranchStateCopyWithImpl(this._self, this._then);

  final BankBranchState _self;
  final $Res Function(BankBranchState) _then;

  /// Create a copy of BankBranchState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? hasError = null,
    Object? errorMessage = freezed,
    Object? branchList = null,
  }) {
    return _then(_self.copyWith(
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      hasError: null == hasError
          ? _self.hasError
          : hasError // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      branchList: null == branchList
          ? _self.branchList
          : branchList // ignore: cast_nullable_to_non_nullable
              as Pagination<BankBranch>,
    ));
  }
}

/// @nodoc

class _BankBranchState implements BankBranchState {
  const _BankBranchState(
      {this.isLoading = false,
      this.hasError = false,
      this.errorMessage,
      this.branchList = const Pagination(
          pageNum: 0, pageSize: 0, totalPage: 0, total: 0, list: [])});

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool hasError;
  @override
  final String? errorMessage;
  @override
  @JsonKey()
  final Pagination<BankBranch> branchList;

  /// Create a copy of BankBranchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$BankBranchStateCopyWith<_BankBranchState> get copyWith =>
      __$BankBranchStateCopyWithImpl<_BankBranchState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _BankBranchState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.hasError, hasError) ||
                other.hasError == hasError) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.branchList, branchList) ||
                other.branchList == branchList));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, isLoading, hasError, errorMessage, branchList);

  @override
  String toString() {
    return 'BankBranchState(isLoading: $isLoading, hasError: $hasError, errorMessage: $errorMessage, branchList: $branchList)';
  }
}

/// @nodoc
abstract mixin class _$BankBranchStateCopyWith<$Res>
    implements $BankBranchStateCopyWith<$Res> {
  factory _$BankBranchStateCopyWith(
          _BankBranchState value, $Res Function(_BankBranchState) _then) =
      __$BankBranchStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool hasError,
      String? errorMessage,
      Pagination<BankBranch> branchList});
}

/// @nodoc
class __$BankBranchStateCopyWithImpl<$Res>
    implements _$BankBranchStateCopyWith<$Res> {
  __$BankBranchStateCopyWithImpl(this._self, this._then);

  final _BankBranchState _self;
  final $Res Function(_BankBranchState) _then;

  /// Create a copy of BankBranchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isLoading = null,
    Object? hasError = null,
    Object? errorMessage = freezed,
    Object? branchList = null,
  }) {
    return _then(_BankBranchState(
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      hasError: null == hasError
          ? _self.hasError
          : hasError // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      branchList: null == branchList
          ? _self.branchList
          : branchList // ignore: cast_nullable_to_non_nullable
              as Pagination<BankBranch>,
    ));
  }
}

// dart format on
