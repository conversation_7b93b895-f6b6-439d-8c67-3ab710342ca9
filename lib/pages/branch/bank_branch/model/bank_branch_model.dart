import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:zrreport/pages/branch/widget/branch_search_bar.dart';
part 'bank_branch_model.freezed.dart';
part 'bank_branch_model.g.dart';

/// 银行网点数据模型
@freezed
abstract class BankBranch with _$BankBranch {
  const BankBranch._(); // 添加私有构造函数

  const factory BankBranch({
    required String id,
    required String code,
    required String name,
    required int type,
    String? pic,
    String? backdrop,
    required int status,
    required String telephone,
  }) = _BankBranch;

  factory BankBranch.fromJson(Map<String, dynamic> json) =>
      _$BankBranchFromJson(json);

  // @override
  // String get searchBarOptionName => bankName;
}
