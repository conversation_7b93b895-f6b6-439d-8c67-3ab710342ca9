// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bank_branch_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BankBranch {
  String get id;
  String get code;
  String get name;
  int get type;
  String? get pic;
  String? get backdrop;
  int get status;
  String get telephone;

  /// Create a copy of BankBranch
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $BankBranchCopyWith<BankBranch> get copyWith =>
      _$BankBranchCopyWithImpl<BankBranch>(this as BankBranch, _$identity);

  /// Serializes this BankBranch to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is BankBranch &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.pic, pic) || other.pic == pic) &&
            (identical(other.backdrop, backdrop) ||
                other.backdrop == backdrop) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.telephone, telephone) ||
                other.telephone == telephone));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, code, name, type, pic, backdrop, status, telephone);

  @override
  String toString() {
    return 'BankBranch(id: $id, code: $code, name: $name, type: $type, pic: $pic, backdrop: $backdrop, status: $status, telephone: $telephone)';
  }
}

/// @nodoc
abstract mixin class $BankBranchCopyWith<$Res> {
  factory $BankBranchCopyWith(
          BankBranch value, $Res Function(BankBranch) _then) =
      _$BankBranchCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String code,
      String name,
      int type,
      String? pic,
      String? backdrop,
      int status,
      String telephone});
}

/// @nodoc
class _$BankBranchCopyWithImpl<$Res> implements $BankBranchCopyWith<$Res> {
  _$BankBranchCopyWithImpl(this._self, this._then);

  final BankBranch _self;
  final $Res Function(BankBranch) _then;

  /// Create a copy of BankBranch
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? code = null,
    Object? name = null,
    Object? type = null,
    Object? pic = freezed,
    Object? backdrop = freezed,
    Object? status = null,
    Object? telephone = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      code: null == code
          ? _self.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      pic: freezed == pic
          ? _self.pic
          : pic // ignore: cast_nullable_to_non_nullable
              as String?,
      backdrop: freezed == backdrop
          ? _self.backdrop
          : backdrop // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
      telephone: null == telephone
          ? _self.telephone
          : telephone // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _BankBranch extends BankBranch {
  const _BankBranch(
      {required this.id,
      required this.code,
      required this.name,
      required this.type,
      this.pic,
      this.backdrop,
      required this.status,
      required this.telephone})
      : super._();
  factory _BankBranch.fromJson(Map<String, dynamic> json) =>
      _$BankBranchFromJson(json);

  @override
  final String id;
  @override
  final String code;
  @override
  final String name;
  @override
  final int type;
  @override
  final String? pic;
  @override
  final String? backdrop;
  @override
  final int status;
  @override
  final String telephone;

  /// Create a copy of BankBranch
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$BankBranchCopyWith<_BankBranch> get copyWith =>
      __$BankBranchCopyWithImpl<_BankBranch>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$BankBranchToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _BankBranch &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.pic, pic) || other.pic == pic) &&
            (identical(other.backdrop, backdrop) ||
                other.backdrop == backdrop) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.telephone, telephone) ||
                other.telephone == telephone));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, code, name, type, pic, backdrop, status, telephone);

  @override
  String toString() {
    return 'BankBranch(id: $id, code: $code, name: $name, type: $type, pic: $pic, backdrop: $backdrop, status: $status, telephone: $telephone)';
  }
}

/// @nodoc
abstract mixin class _$BankBranchCopyWith<$Res>
    implements $BankBranchCopyWith<$Res> {
  factory _$BankBranchCopyWith(
          _BankBranch value, $Res Function(_BankBranch) _then) =
      __$BankBranchCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String code,
      String name,
      int type,
      String? pic,
      String? backdrop,
      int status,
      String telephone});
}

/// @nodoc
class __$BankBranchCopyWithImpl<$Res> implements _$BankBranchCopyWith<$Res> {
  __$BankBranchCopyWithImpl(this._self, this._then);

  final _BankBranch _self;
  final $Res Function(_BankBranch) _then;

  /// Create a copy of BankBranch
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? code = null,
    Object? name = null,
    Object? type = null,
    Object? pic = freezed,
    Object? backdrop = freezed,
    Object? status = null,
    Object? telephone = null,
  }) {
    return _then(_BankBranch(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      code: null == code
          ? _self.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      pic: freezed == pic
          ? _self.pic
          : pic // ignore: cast_nullable_to_non_nullable
              as String?,
      backdrop: freezed == backdrop
          ? _self.backdrop
          : backdrop // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
      telephone: null == telephone
          ? _self.telephone
          : telephone // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
