import 'dart:async';

import 'package:dio/dio.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:zrreport/common/index.dart';

typedef SearchApiFunction<T extends Object> = Future<Pagination<T>> Function(
    String keyword, int pageNum, int pageSize, CancelToken? cancelToken);

class BranchSearchViewModel<T extends Object> {
  BranchSearchViewModel(this.searchApiFunction);

  // // 刷新控制器
  // EasyRefreshController easyRefreshController = EasyRefreshController(
  //   controlFinishRefresh: false,
  //   controlFinishLoad: true,
  // );

  String? keyword;

  Pagination<T> _pagination = Pagination.empty();
  Pagination<T> get pagination => _pagination;

  List<T> get items => _pagination.list;

  CancelToken? _cancelToken;
  int get pageIndex => _pagination.pageNum;
  int get pageSize => 10;

  bool get hasMorePage => _pagination.hasMorePage;

  var isDebounce = false;

  _DebounceTimer? _debounce;

  SearchApiFunction<T> searchApiFunction;

  Future<bool> firstLoad(String keyword) async {
    reset();
    // if (isDebounce) {
    //   return;
    // }

    // isDebounce = true;

    // await Future.delayed(Duration(seconds: 1));
    // await _firstLoad(keyword);
    // isDebounce = false;

    // 取消之前的计时器
    // if (_debounce != null && !_debounce!.isCompleted) _debounce?.cancel();

    // 设置新的计时器
    _debounce = _DebounceTimer();

    this.keyword = keyword;

    defaultLogger.warning("搜索公司keyword:$keyword");
    try {
      // await _debounce!.future;
      _cancelToken?.cancel();
      return await _load(1);
    } on _CancelException {
      return false;
    }
  }

  Future<bool> _load(int pageNum) async {
    if (keyword == null) {
      return false;
    }
    try {
      _cancelToken = CancelToken();
      final newPagination = await searchApiFunction.call(
          keyword!, pageNum, pageSize, _cancelToken);

      List<T> oldList = pageNum != 1 ? items : [];

      _pagination = newPagination.copyWith(
          pageNum: newPagination.pageNum,
          pageSize: newPagination.pageSize,
          total: newPagination.total,
          totalPage: newPagination.totalPage,
          list: oldList + newPagination.list);

      defaultLogger.warning("搜索公司成功, items:${newPagination.list.length}");

      return true;
    } catch (error) {
      defaultLogger.warning("搜索公司失败，error:$error");
      return false;
    }
  }

  loadMore() async {
    return _load(pageIndex + 1);
  }

  void reset() {
    _pagination = Pagination.empty();
    keyword = null;
  }
}

const Duration debounceDuration = Duration(milliseconds: 500);

class _DebounceTimer {
  _DebounceTimer() {
    _timer = Timer(debounceDuration, _onComplete);
  }

  late final Timer _timer;
  final Completer<void> _completer = Completer<void>();

  void _onComplete() {
    _completer.complete();
  }

  Future<void> get future => _completer.future;

  bool get isCompleted => _completer.isCompleted;

  void cancel() {
    _timer.cancel();
    _completer.completeError(const _CancelException());
  }
}

// An exception indicating that the timer was canceled.
class _CancelException implements Exception {
  const _CancelException();
}
