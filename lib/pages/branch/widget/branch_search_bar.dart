import 'dart:math';

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:zrreport/common/index.dart';
import 'branch_search_viewmodel.dart';

typedef OptionBuilder<T> = Widget Function(BuildContext context, T item);

typedef SuffixBuilder<T> = (Widget, Size) Function(
    BuildContext context, List<T> items);

typedef SearchButtonAction<T> = void Function(Pagination<T>, String);
// typedef SuffixAction<T> = void Function(Pagination<T>);


typedef SearchOptionSelectedAction<T> = void Function(T, String);

class BranchSearchBar<T extends Object> extends StatefulWidget {
  BranchSearchBar(
      {super.key,
      required this.viewModel,
      required this.optionItemBuilder,
      this.hint = "",
      this.text,
      // this.rightSuffixBuilder,
      this.onSearchButtonAction,
      this.onSelected});

  BranchSearchViewModel<T> viewModel;

  @override
  State<BranchSearchBar<T>> createState() => _BranchSearchBarState<T>();

  String hint;

  String? text;

  OptionBuilder<T> optionItemBuilder;

  SearchButtonAction<T>? onSearchButtonAction;

  SearchOptionSelectedAction<T>? onSelected;

  // SuffixBuilder<T>? rightSuffixBuilder;

  static Widget buildOptionItem(String name) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 20),
      child: Text(
        name,
        style: TextStyle(fontSize: 15, color: AppColors.textColor1),
      ),
    );
  }
}

class _BranchSearchBarState<T extends Object>
    extends State<BranchSearchBar<T>> {
  // 当前页码
  int currentPage = 1;
  bool hasMorePage = false;

  // 刷新控制器
  // EasyRefreshController easyRefreshController = EasyRefreshController(
  //   controlFinishRefresh: true,
  //   controlFinishLoad: true,
  // );

  @override
  void initState() {
    super.initState();
    // _debouncedSearch =
    //     _debounce<Iterable<SearchEnterprise>?, String>(_firstSearch);
  }

  TextEditingController? _textEditingController;

  FocusNode? _focusNode;

  List<SearchEnterprise> enterprises = [];

  BranchSearchViewModel<T> get viewModel => widget.viewModel;

  @override
  Widget build(BuildContext context) {
    return _buildAutocomplete();
  }

  MyAutocomplete<T> _buildAutocomplete() {
    return MyAutocomplete<T>(
      fieldViewBuilder:
          (context, textEditingController, focusNode, onFieldSubmitted) {
        _textEditingController = textEditingController;
        _focusNode = focusNode;

        focusNode.addListener(() => setState(() {}));
        if (widget.text?.isNotEmpty == true) {
          _focusNode?.requestFocus();
          _textEditingController?.text = widget.text ?? "";
          widget.text = null;
        }
        return _buildSearchField(
            context, textEditingController, focusNode, () {
          /// 点击键盘上的右下角按钮
          widget.onSearchButtonAction
              ?.call(viewModel.pagination, viewModel.keyword ?? '');
          _focusNode?.unfocus();
        });
      },
      optionsViewBuilder: (context, onSelected, options) {
        return _optionsList(onSelected);
      },
      optionsBuilder: (TextEditingValue textEditingValue) async {
        final text = textEditingValue.text;
        await viewModel.firstLoad(text);
        defaultLogger.info("optionsBuilder length:${viewModel.items.length}");
        return viewModel.items;
      },
      onSelected: (option) async {
        _focusNode?.unfocus();
        /// 这里需要等待下 _focusNode.hasFocus 比为false。不然 _textEditingController?.text
        /// 又会触发搜索
        await Future.delayed(Duration(milliseconds: 400));
        // _textEditingController?.text = "";
        widget.onSelected?.call(option, _textEditingController?.text ?? "");
      },
    );
  }

  _optionsList(AutocompleteOnSelected<T> onSelected) {
    defaultLogger.info("_optionsList 11111 length:${viewModel.items.length}");
    return StatefulBuilder(builder: (context, setListState) {
      defaultLogger.info("_optionsList length:${viewModel.items.length}");
      if (viewModel.items.isEmpty) {
        return SizedBox.shrink();
      }
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 20),
            decoration: BoxDecoration(
              color: Color(0xFFF7F7F7),
              // color: Colors.red,
              borderRadius: BorderRadius.circular(10),
            ),
            height: min(300, max(viewModel.items.length * 40, 100)),
            child: EasyRefresh(
              onLoad: viewModel.hasMorePage
                  ? () async {
                      try {
                        await viewModel.loadMore();
                        setListState(() {});
                      } catch (error) {}
                    }
                  : null,
              child: ListView(
                key: ValueKey(viewModel.keyword),
                padding: EdgeInsets.only(top: 8, bottom: 5),
                children: [
                  ...viewModel.items.map((a) {
                    return GestureDetector(
                      onTap: () {
                        onSelected(a);
                      },
                      child: widget.optionItemBuilder(context, a),
                    );
                  }),
                ],
              ),
            ),
          ),
          Spacer(),
        ],
      );
    });
  }

  _buildSearchField(
      BuildContext context,
      TextEditingController textEditingController,
      FocusNode focusNode,
      VoidCallback onFieldSubmitted) {
    // final rightSuffixInfo =
    //     widget.rightSuffixBuilder?.call(context, viewModel.items);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 17.0),
      child: TextFormField(
        controller: textEditingController,
        focusNode: focusNode,
        onFieldSubmitted: (String value) {
          onFieldSubmitted();
        },
        textInputAction: TextInputAction.search,
        style: TextStyle(color: Color(0xFF222222), fontSize: 14),
        decoration: InputDecoration(
          isDense: true,
          hintText: widget.hint,
          hintStyle: TextStyle(
              fontSize: 14,
              color: Color(0xFFE6E6E6),
              fontWeight: FontWeight.w600,
              height: 20 / 14),
          hintMaxLines: 1,
          fillColor: Colors.white,
          filled: true,
          prefixIcon:
              SvgPicture.asset(AssetsSvgs.searchIconSvg, width: 20, height: 20),
          prefixIconConstraints: BoxConstraints.tightFor(width: 50, height: 20),
          suffixIcon: //viewModel.items.isNotEmpty &&
              focusNode.hasFocus ? buildSearchSuffix() : null,
          suffixIconConstraints: BoxConstraints.tightFor(width: 70, height: 40),
          contentPadding:
              EdgeInsetsDirectional.symmetric(horizontal: 24, vertical: 15),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(
              color: Color(0xFFDDDDDD), // 边框颜色
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(
              color: Color(0xFFDDDDDD), // 边框颜色
            ),
          ),
        ),
      ),
    );
  }

  buildSearchSuffix() {
    return Row(
      children: [
        Expanded(
            child: TextButton(
          onPressed: () {
            widget.onSearchButtonAction
                ?.call(viewModel.pagination, viewModel.keyword ?? '');
            _focusNode?.unfocus();
          },
          style: ElevatedButton.styleFrom(
            disabledBackgroundColor: AppColors.primary,
            backgroundColor: AppColors.primary,
            padding: EdgeInsets.symmetric(vertical: 0),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            "搜索",
            style: TextStyle(
                color: Colors.white, fontSize: 17, fontWeight: FontWeight.bold),
          ),
        )),
        SizedBox(width: 10),
      ],
    );
  }
}
