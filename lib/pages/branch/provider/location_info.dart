import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:zrreport/common/index.dart';


part 'location_info.freezed.dart';

/// 位置信息模型，包含经纬度和地理编码信息
@freezed
abstract class LocationInfo with _$LocationInfo {
  const factory LocationInfo({
    double? longitude,
    double? latitude,
    @Default(false) bool hasLocationPermission,
  }) = _LocationInfo;


  // String get city => regeocode?.addressComponent.city ?? '';

  // String get adCode => regeocode?.addressComponent.adcode ?? '';

  // String get district => regeocode?.addressComponent.district ?? '';

  // String get township => regeocode?.addressComponent.township ?? '';
}
