import 'package:geolocator/geolocator.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/common/utils/location_utils.dart';
import 'location_info.dart';

part 'location_provider.g.dart';

/// 管理用户当前地理位置的Provider
@Riverpod()
class LocationNotifier extends _$LocationNotifier {
  @override
  LocationInfo build() {
    // 初始获取位置
    Future.microtask(
        () => getCurrentLocationAndResetLocation()); // 确保在widget初始化之后再尝试获取位置
    return const LocationInfo();
  }

  ///
  Future<Position?> getCurrentLocationAndResetLocation() async {
    final position = await getCurrentLocation();
    if (position != null) {
      updateLocation(position);
    }
    return null;
  }

  void updateLocation(Position position) {
    state = state.copyWith(
        latitude: position.latitude, longitude: position.longitude);
  }

  /// 尝试获取当前位置
  Future<Position?> getCurrentLocation() async {
    try {
      if (!state.hasLocationPermission) {
        final granted = await LocationUtils.requestLocationPermission();
        defaultLogger.debug("_getCurrentLocation grante:$granted");
        state = state.copyWith(hasLocationPermission: granted);
        defaultLogger.debug(
            "_getCurrentLocation state hasLocationPermission :${state.hasLocationPermission}");
      }
      if (state.hasLocationPermission) {
        final position = await LocationUtils.getCurrentLocation();
        if (position != null) {
          return position;
        }
        return null;
      } else {
        // 定位权限被拒绝
        return null;
      }
    } catch (e) {
      // 获取定位失败
      return null;
    }
  }

  /// 主动检查定位权限并根据变化更新state
  Future<void> checkAndUpdateLocationPermission() async {
    final granted = await LocationUtils.requestLocationPermission();
    if (granted && !state.hasLocationPermission) {
      state = state.copyWith(hasLocationPermission: granted);
      // 如果权限变为允许，自动尝试获取定位
      if (granted) {
        await getCurrentLocationAndResetLocation();
      }
    }
  }
}
