import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/branch/provider/location_provider.dart';
import '../model/credit_branch_model.dart';
part 'credit_branch_provider.g.dart';
part 'credit_branch_provider.freezed.dart';

/// 征信网点查询状态
@freezed
abstract class CreditBranchState with _$CreditBranchState {
  const factory CreditBranchState({
    @Default(false) bool isLoading,
    @Default(false) bool hasError,
    String? errorMessage,
    @Default(
        Pagination(pageNum: 0, pageSize: 0, totalPage: 0, total: 0, list: []))
    Pagination<CreditBranch> branchList,

    /// 详细的位置
    String? detailAddress,
    LocationDetail? address,
  }) = _CreditBranchState;


  // Location2D? get location2D {
  //   double? longitude = address?.longitude;
  //   double? latitude = address?.latitude;
  //   if (longitude == null || latitude == null) {
  //     return null;
  //   }
  //   return Location2D(longitude: longitude, latitude: latitude);
  // }
}

/// 征信网点查询 Provider
@Riverpod()
class CreditBranchNotifier extends _$CreditBranchNotifier {
  int get _page => state.branchList.pageNum;
  static const int _pageSize = 20;

  bool get hasMorePage => state.branchList.pageNum < state.branchList.totalPage;

  // LocationInfo? _position;
  // LocationInfo? get position => _position;

  String get locationDesc {
    bool hasPermission =
        ref.read(locationNotifierProvider).hasLocationPermission;

    defaultLogger.debug("locationDesc  hasPermission :$hasPermission");
    if (!hasPermission) {
      return "距离推荐需要您授权位置信息";
    } else if (state.address?.latitude == null ||
        state.address?.longitude == null) {
      return "正在获取地理位置";
    } else if (state.detailAddress != null) {
      return state.detailAddress!;
    }
    return "正在获取地理位置";
  }

  String? _keyword;
  String get keyword => _keyword ?? "";

  @override
  CreditBranchState build() {
    // 监听位置变化
    final latitude =
        ref.watch(locationNotifierProvider.select((state) => state.latitude));

    final longitude =
        ref.watch(locationNotifierProvider.select((state) => state.longitude));

    // 初始化时加载第一页数据
    Future.microtask(() {
      loadFirstPage();
      _getLocationInfo();
    });
    if (latitude != null && longitude != null) {
      return CreditBranchState(
          address: LocationDetail(longitude: longitude, latitude: latitude));
    } else {
      return const CreditBranchState();
    }
  }

  CreditBranchState? originState;

  void revert() {
    if (originState != null) {
      _keyword = null;
      // state = originState!;
      state = state.copyWith(
          branchList: originState!.branchList,
          isLoading: false,
          errorMessage: null,
          hasError: false);
      originState = null;

      originState = null;
    }
  }

  void update(Pagination<CreditBranch> pagination, String keyword) {
    _keyword = keyword;
    if (originState == null && state.branchList.list.isNotEmpty) {
      originState = state;
    }

    state = state.copyWith(
        branchList: pagination,
        isLoading: false,
        errorMessage: null,
        hasError: false);
  }

  /// 加载第一页
  Future<bool> loadFirstPage() async {
    defaultLogger.info(
        '征信网点 加载第一页, position:(${state.address?.latitude}, ${state.address?.longitude} )');
    if (state.address?.latitude != null && state.address?.longitude != null) {
      return await loadBranches(1);
    } else {
      return false;
    }
  }

  void updateLocation(LocationDetail newLocation) {
    state = state.copyWith(address: newLocation);
    loadFirstPage();
    _getLocationInfo();
  }

  /// 加载下一页
  Future<bool> loadMorePage() async {
    if (!hasMorePage || state.isLoading) return false;
    return await loadBranches(_page + 1);
  }

  /// 加载网点数据
  Future<bool> loadBranches(int pageNum) async {
    if (state.isLoading) return false;

    try {
      state = state.copyWith(
        isLoading: true,
        hasError: false,
        errorMessage: null,
      );

      final response = await BranchApi.getCreditBranch(
          pageNum: pageNum,
          pageSize: _pageSize,
          keyword: _keyword,
          lat: state.address?.latitude,
          lon: state.address?.longitude);

      final pagination = response.data;
      if (pagination != null) {
        final newList = pagination.list;
        // final oldList = state.branchList.list;

        final oldList = pageNum != 1 ? state.branchList.list : [];

        state = state.copyWith(
          branchList: Pagination(
            pageNum: pagination.pageNum,
            pageSize: pagination.pageSize,
            totalPage: pagination.totalPage,
            total: response.data!.total,
            list: [...oldList, ...newList],
          ),
        );
        return true;
      } else {
        state = state.copyWith(
          hasError: true,
          errorMessage: response.message,
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        hasError: true,
        errorMessage: '$e',
      );
      return false;
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  /// 获取地理位置信息
  Future<AmapLocationResponse?> _getLocationInfo() async {
    if (state.address?.latitude == null || state.address?.longitude == null) {
      return null;
    }
    try {
      final resp = await AmapService().regeocode(
          longitude: state.address!.longitude,
          latitude: state.address!.latitude);
      final newLocation = state.address?.copyWith(
          adCode: resp.regeocode.addressComponent.adcode,
          cityName: resp.regeocode.addressComponent.city,
          townName: resp.regeocode.address);
      state = state.copyWith(
          address: newLocation, detailAddress: resp.regeocode.formattedAddress);
    } catch (e) {
      defaultLogger.error('获取地理位置信息失败: $e');
      return null;
    }
    return null;
  }
}
