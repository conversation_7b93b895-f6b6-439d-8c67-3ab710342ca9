// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'credit_branch_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CreditBranchState {
  bool get isLoading;
  bool get hasError;
  String? get errorMessage;
  Pagination<CreditBranch> get branchList;

  /// 详细的位置
  String? get detailAddress;
  LocationDetail? get address;

  /// Create a copy of CreditBranchState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CreditBranchStateCopyWith<CreditBranchState> get copyWith =>
      _$CreditBranchStateCopyWithImpl<CreditBranchState>(
          this as CreditBranchState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CreditBranchState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.hasError, hasError) ||
                other.hasError == hasError) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.branchList, branchList) ||
                other.branchList == branchList) &&
            (identical(other.detailAddress, detailAddress) ||
                other.detailAddress == detailAddress) &&
            (identical(other.address, address) || other.address == address));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isLoading, hasError,
      errorMessage, branchList, detailAddress, address);

  @override
  String toString() {
    return 'CreditBranchState(isLoading: $isLoading, hasError: $hasError, errorMessage: $errorMessage, branchList: $branchList, detailAddress: $detailAddress, address: $address)';
  }
}

/// @nodoc
abstract mixin class $CreditBranchStateCopyWith<$Res> {
  factory $CreditBranchStateCopyWith(
          CreditBranchState value, $Res Function(CreditBranchState) _then) =
      _$CreditBranchStateCopyWithImpl;
  @useResult
  $Res call(
      {bool isLoading,
      bool hasError,
      String? errorMessage,
      Pagination<CreditBranch> branchList,
      String? detailAddress,
      LocationDetail? address});

  $LocationDetailCopyWith<$Res>? get address;
}

/// @nodoc
class _$CreditBranchStateCopyWithImpl<$Res>
    implements $CreditBranchStateCopyWith<$Res> {
  _$CreditBranchStateCopyWithImpl(this._self, this._then);

  final CreditBranchState _self;
  final $Res Function(CreditBranchState) _then;

  /// Create a copy of CreditBranchState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? hasError = null,
    Object? errorMessage = freezed,
    Object? branchList = null,
    Object? detailAddress = freezed,
    Object? address = freezed,
  }) {
    return _then(_self.copyWith(
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      hasError: null == hasError
          ? _self.hasError
          : hasError // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      branchList: null == branchList
          ? _self.branchList
          : branchList // ignore: cast_nullable_to_non_nullable
              as Pagination<CreditBranch>,
      detailAddress: freezed == detailAddress
          ? _self.detailAddress
          : detailAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _self.address
          : address // ignore: cast_nullable_to_non_nullable
              as LocationDetail?,
    ));
  }

  /// Create a copy of CreditBranchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationDetailCopyWith<$Res>? get address {
    if (_self.address == null) {
      return null;
    }

    return $LocationDetailCopyWith<$Res>(_self.address!, (value) {
      return _then(_self.copyWith(address: value));
    });
  }
}

/// @nodoc

class _CreditBranchState implements CreditBranchState {
  const _CreditBranchState(
      {this.isLoading = false,
      this.hasError = false,
      this.errorMessage,
      this.branchList = const Pagination(
          pageNum: 0, pageSize: 0, totalPage: 0, total: 0, list: []),
      this.detailAddress,
      this.address});

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool hasError;
  @override
  final String? errorMessage;
  @override
  @JsonKey()
  final Pagination<CreditBranch> branchList;

  /// 详细的位置
  @override
  final String? detailAddress;
  @override
  final LocationDetail? address;

  /// Create a copy of CreditBranchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CreditBranchStateCopyWith<_CreditBranchState> get copyWith =>
      __$CreditBranchStateCopyWithImpl<_CreditBranchState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CreditBranchState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.hasError, hasError) ||
                other.hasError == hasError) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.branchList, branchList) ||
                other.branchList == branchList) &&
            (identical(other.detailAddress, detailAddress) ||
                other.detailAddress == detailAddress) &&
            (identical(other.address, address) || other.address == address));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isLoading, hasError,
      errorMessage, branchList, detailAddress, address);

  @override
  String toString() {
    return 'CreditBranchState(isLoading: $isLoading, hasError: $hasError, errorMessage: $errorMessage, branchList: $branchList, detailAddress: $detailAddress, address: $address)';
  }
}

/// @nodoc
abstract mixin class _$CreditBranchStateCopyWith<$Res>
    implements $CreditBranchStateCopyWith<$Res> {
  factory _$CreditBranchStateCopyWith(
          _CreditBranchState value, $Res Function(_CreditBranchState) _then) =
      __$CreditBranchStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool hasError,
      String? errorMessage,
      Pagination<CreditBranch> branchList,
      String? detailAddress,
      LocationDetail? address});

  @override
  $LocationDetailCopyWith<$Res>? get address;
}

/// @nodoc
class __$CreditBranchStateCopyWithImpl<$Res>
    implements _$CreditBranchStateCopyWith<$Res> {
  __$CreditBranchStateCopyWithImpl(this._self, this._then);

  final _CreditBranchState _self;
  final $Res Function(_CreditBranchState) _then;

  /// Create a copy of CreditBranchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isLoading = null,
    Object? hasError = null,
    Object? errorMessage = freezed,
    Object? branchList = null,
    Object? detailAddress = freezed,
    Object? address = freezed,
  }) {
    return _then(_CreditBranchState(
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      hasError: null == hasError
          ? _self.hasError
          : hasError // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      branchList: null == branchList
          ? _self.branchList
          : branchList // ignore: cast_nullable_to_non_nullable
              as Pagination<CreditBranch>,
      detailAddress: freezed == detailAddress
          ? _self.detailAddress
          : detailAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _self.address
          : address // ignore: cast_nullable_to_non_nullable
              as LocationDetail?,
    ));
  }

  /// Create a copy of CreditBranchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationDetailCopyWith<$Res>? get address {
    if (_self.address == null) {
      return null;
    }

    return $LocationDetailCopyWith<$Res>(_self.address!, (value) {
      return _then(_self.copyWith(address: value));
    });
  }
}

// dart format on
