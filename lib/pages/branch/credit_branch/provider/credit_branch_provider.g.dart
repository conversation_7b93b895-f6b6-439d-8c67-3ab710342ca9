// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'credit_branch_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$creditBranchNotifierHash() =>
    r'99bbea30e8f0b4a4260d5ed1711b92155714fd7a';

/// 征信网点查询 Provider
///
/// Copied from [CreditBranchNotifier].
@ProviderFor(CreditBranchNotifier)
final creditBranchNotifierProvider = AutoDisposeNotifierProvider<
    CreditBranchNotifier, CreditBranchState>.internal(
  CreditBranchNotifier.new,
  name: r'creditBranchNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$creditBranchNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CreditBranchNotifier = AutoDisposeNotifier<CreditBranchState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
