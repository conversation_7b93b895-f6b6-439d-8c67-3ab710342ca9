// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'credit_branch_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CreditBranch _$CreditBranchFromJson(Map<String, dynamic> json) =>
    _CreditBranch(
      id: json['id'] as String,
      province: json['province'] as String,
      city: json['city'] as String,
      region: json['region'] as String,
      detail: json['detail'] as String,
      lon: json['lon'] as String,
      lat: json['lat'] as String,
      outletsName: json['outletsName'] as String,
      telephone: json['telephone'] as String? ?? '',
      procTime: json['procTime'] as String,
      procScope: json['procScope'] as String,
      procScopeDesc: json['procScopeDesc'] as String,
      distance: (json['distance'] as num).toDouble(),
    );

Map<String, dynamic> _$CreditBranchToJson(_CreditBranch instance) =>
    <String, dynamic>{
      'id': instance.id,
      'province': instance.province,
      'city': instance.city,
      'region': instance.region,
      'detail': instance.detail,
      'lon': instance.lon,
      'lat': instance.lat,
      'outletsName': instance.outletsName,
      'telephone': instance.telephone,
      'procTime': instance.procTime,
      'procScope': instance.procScope,
      'procScopeDesc': instance.procScopeDesc,
      'distance': instance.distance,
    };
