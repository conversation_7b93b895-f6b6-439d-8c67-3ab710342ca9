import 'package:freezed_annotation/freezed_annotation.dart';

part 'credit_branch_model.freezed.dart';
part 'credit_branch_model.g.dart';

/// 征信网点数据模型
@freezed
abstract class CreditBranch with _$CreditBranch {
  const factory CreditBranch({
    required String id,
    required String province,
    required String city,
    required String region,
    required String detail,
    required String lon,
    required String lat,
    required String outletsName,
    
    @Default('') String telephone,
    required String procTime,
    required String procScope,
    required String procScopeDesc,
    required double distance,
  }) = _CreditBranch;

  factory CreditBranch.fromJson(Map<String, dynamic> json) =>
      _$CreditBranchFromJson(json);
}
