// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'credit_branch_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CreditBranch {
  String get id;
  String get province;
  String get city;
  String get region;
  String get detail;
  String get lon;
  String get lat;
  String get outletsName;
  String get telephone;
  String get procTime;
  String get procScope;
  String get procScopeDesc;
  double get distance;

  /// Create a copy of CreditBranch
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CreditBranchCopyWith<CreditBranch> get copyWith =>
      _$CreditBranchCopyWithImpl<CreditBranch>(
          this as CreditBranch, _$identity);

  /// Serializes this CreditBranch to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CreditBranch &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.province, province) ||
                other.province == province) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.region, region) || other.region == region) &&
            (identical(other.detail, detail) || other.detail == detail) &&
            (identical(other.lon, lon) || other.lon == lon) &&
            (identical(other.lat, lat) || other.lat == lat) &&
            (identical(other.outletsName, outletsName) ||
                other.outletsName == outletsName) &&
            (identical(other.telephone, telephone) ||
                other.telephone == telephone) &&
            (identical(other.procTime, procTime) ||
                other.procTime == procTime) &&
            (identical(other.procScope, procScope) ||
                other.procScope == procScope) &&
            (identical(other.procScopeDesc, procScopeDesc) ||
                other.procScopeDesc == procScopeDesc) &&
            (identical(other.distance, distance) ||
                other.distance == distance));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      province,
      city,
      region,
      detail,
      lon,
      lat,
      outletsName,
      telephone,
      procTime,
      procScope,
      procScopeDesc,
      distance);

  @override
  String toString() {
    return 'CreditBranch(id: $id, province: $province, city: $city, region: $region, detail: $detail, lon: $lon, lat: $lat, outletsName: $outletsName, telephone: $telephone, procTime: $procTime, procScope: $procScope, procScopeDesc: $procScopeDesc, distance: $distance)';
  }
}

/// @nodoc
abstract mixin class $CreditBranchCopyWith<$Res> {
  factory $CreditBranchCopyWith(
          CreditBranch value, $Res Function(CreditBranch) _then) =
      _$CreditBranchCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String province,
      String city,
      String region,
      String detail,
      String lon,
      String lat,
      String outletsName,
      String telephone,
      String procTime,
      String procScope,
      String procScopeDesc,
      double distance});
}

/// @nodoc
class _$CreditBranchCopyWithImpl<$Res> implements $CreditBranchCopyWith<$Res> {
  _$CreditBranchCopyWithImpl(this._self, this._then);

  final CreditBranch _self;
  final $Res Function(CreditBranch) _then;

  /// Create a copy of CreditBranch
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? province = null,
    Object? city = null,
    Object? region = null,
    Object? detail = null,
    Object? lon = null,
    Object? lat = null,
    Object? outletsName = null,
    Object? telephone = null,
    Object? procTime = null,
    Object? procScope = null,
    Object? procScopeDesc = null,
    Object? distance = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      province: null == province
          ? _self.province
          : province // ignore: cast_nullable_to_non_nullable
              as String,
      city: null == city
          ? _self.city
          : city // ignore: cast_nullable_to_non_nullable
              as String,
      region: null == region
          ? _self.region
          : region // ignore: cast_nullable_to_non_nullable
              as String,
      detail: null == detail
          ? _self.detail
          : detail // ignore: cast_nullable_to_non_nullable
              as String,
      lon: null == lon
          ? _self.lon
          : lon // ignore: cast_nullable_to_non_nullable
              as String,
      lat: null == lat
          ? _self.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as String,
      outletsName: null == outletsName
          ? _self.outletsName
          : outletsName // ignore: cast_nullable_to_non_nullable
              as String,
      telephone: null == telephone
          ? _self.telephone
          : telephone // ignore: cast_nullable_to_non_nullable
              as String,
      procTime: null == procTime
          ? _self.procTime
          : procTime // ignore: cast_nullable_to_non_nullable
              as String,
      procScope: null == procScope
          ? _self.procScope
          : procScope // ignore: cast_nullable_to_non_nullable
              as String,
      procScopeDesc: null == procScopeDesc
          ? _self.procScopeDesc
          : procScopeDesc // ignore: cast_nullable_to_non_nullable
              as String,
      distance: null == distance
          ? _self.distance
          : distance // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _CreditBranch implements CreditBranch {
  const _CreditBranch(
      {required this.id,
      required this.province,
      required this.city,
      required this.region,
      required this.detail,
      required this.lon,
      required this.lat,
      required this.outletsName,
      this.telephone = '',
      required this.procTime,
      required this.procScope,
      required this.procScopeDesc,
      required this.distance});
  factory _CreditBranch.fromJson(Map<String, dynamic> json) =>
      _$CreditBranchFromJson(json);

  @override
  final String id;
  @override
  final String province;
  @override
  final String city;
  @override
  final String region;
  @override
  final String detail;
  @override
  final String lon;
  @override
  final String lat;
  @override
  final String outletsName;
  @override
  @JsonKey()
  final String telephone;
  @override
  final String procTime;
  @override
  final String procScope;
  @override
  final String procScopeDesc;
  @override
  final double distance;

  /// Create a copy of CreditBranch
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CreditBranchCopyWith<_CreditBranch> get copyWith =>
      __$CreditBranchCopyWithImpl<_CreditBranch>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CreditBranchToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CreditBranch &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.province, province) ||
                other.province == province) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.region, region) || other.region == region) &&
            (identical(other.detail, detail) || other.detail == detail) &&
            (identical(other.lon, lon) || other.lon == lon) &&
            (identical(other.lat, lat) || other.lat == lat) &&
            (identical(other.outletsName, outletsName) ||
                other.outletsName == outletsName) &&
            (identical(other.telephone, telephone) ||
                other.telephone == telephone) &&
            (identical(other.procTime, procTime) ||
                other.procTime == procTime) &&
            (identical(other.procScope, procScope) ||
                other.procScope == procScope) &&
            (identical(other.procScopeDesc, procScopeDesc) ||
                other.procScopeDesc == procScopeDesc) &&
            (identical(other.distance, distance) ||
                other.distance == distance));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      province,
      city,
      region,
      detail,
      lon,
      lat,
      outletsName,
      telephone,
      procTime,
      procScope,
      procScopeDesc,
      distance);

  @override
  String toString() {
    return 'CreditBranch(id: $id, province: $province, city: $city, region: $region, detail: $detail, lon: $lon, lat: $lat, outletsName: $outletsName, telephone: $telephone, procTime: $procTime, procScope: $procScope, procScopeDesc: $procScopeDesc, distance: $distance)';
  }
}

/// @nodoc
abstract mixin class _$CreditBranchCopyWith<$Res>
    implements $CreditBranchCopyWith<$Res> {
  factory _$CreditBranchCopyWith(
          _CreditBranch value, $Res Function(_CreditBranch) _then) =
      __$CreditBranchCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String province,
      String city,
      String region,
      String detail,
      String lon,
      String lat,
      String outletsName,
      String telephone,
      String procTime,
      String procScope,
      String procScopeDesc,
      double distance});
}

/// @nodoc
class __$CreditBranchCopyWithImpl<$Res>
    implements _$CreditBranchCopyWith<$Res> {
  __$CreditBranchCopyWithImpl(this._self, this._then);

  final _CreditBranch _self;
  final $Res Function(_CreditBranch) _then;

  /// Create a copy of CreditBranch
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? province = null,
    Object? city = null,
    Object? region = null,
    Object? detail = null,
    Object? lon = null,
    Object? lat = null,
    Object? outletsName = null,
    Object? telephone = null,
    Object? procTime = null,
    Object? procScope = null,
    Object? procScopeDesc = null,
    Object? distance = null,
  }) {
    return _then(_CreditBranch(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      province: null == province
          ? _self.province
          : province // ignore: cast_nullable_to_non_nullable
              as String,
      city: null == city
          ? _self.city
          : city // ignore: cast_nullable_to_non_nullable
              as String,
      region: null == region
          ? _self.region
          : region // ignore: cast_nullable_to_non_nullable
              as String,
      detail: null == detail
          ? _self.detail
          : detail // ignore: cast_nullable_to_non_nullable
              as String,
      lon: null == lon
          ? _self.lon
          : lon // ignore: cast_nullable_to_non_nullable
              as String,
      lat: null == lat
          ? _self.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as String,
      outletsName: null == outletsName
          ? _self.outletsName
          : outletsName // ignore: cast_nullable_to_non_nullable
              as String,
      telephone: null == telephone
          ? _self.telephone
          : telephone // ignore: cast_nullable_to_non_nullable
              as String,
      procTime: null == procTime
          ? _self.procTime
          : procTime // ignore: cast_nullable_to_non_nullable
              as String,
      procScope: null == procScope
          ? _self.procScope
          : procScope // ignore: cast_nullable_to_non_nullable
              as String,
      procScopeDesc: null == procScopeDesc
          ? _self.procScopeDesc
          : procScopeDesc // ignore: cast_nullable_to_non_nullable
              as String,
      distance: null == distance
          ? _self.distance
          : distance // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

// dart format on
