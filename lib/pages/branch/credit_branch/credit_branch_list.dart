import 'package:app_settings/app_settings.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/branch/provider/location_provider.dart';
import 'package:zrreport/pages/location/location_main/location_main_page.dart';
import 'provider/credit_branch_provider.dart';
import 'model/credit_branch_model.dart';

/// 征信网点列表组件
class CreditBranchList extends ConsumerStatefulWidget {
  const CreditBranchList({super.key});

  @override
  _CreditBranchListState createState() => _CreditBranchListState();
}

class _CreditBranchListState extends ConsumerState<CreditBranchList>
    with WidgetsBindingObserver {
  @override
  Widget build(BuildContext context) {
    final state = ref.watch(creditBranchNotifierProvider);
    final notifier = ref.watch(creditBranchNotifierProvider.notifier);

    // 如果没有数据，显示空视图
    return Column(
      children: [
        _buildLocationBar(context, ref),
        Expanded(child: Container(child: _buildContent(state, notifier))),
      ],
    );
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // 这里调用定位权限检查
      ref
          .read(locationNotifierProvider.notifier)
          .checkAndUpdateLocationPermission();
    }
  }

  _buildLocationBar(BuildContext context, WidgetRef ref) {
    final notifier = ref.watch(creditBranchNotifierProvider.notifier);

    bool hasPermission =
        ref.read(locationNotifierProvider).hasLocationPermission;
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () async {
        bool hasPermission =
            ref.read(locationNotifierProvider).hasLocationPermission;

        if (hasPermission) {
          final location = await Navigator.push<LocationDetail>(
            context,
            MaterialPageRoute(
              builder: (context) => LocationMainPage(
                currentLocation: ref.read(creditBranchNotifierProvider).address,
              ),
            ),
          );
          if (location != null) {
            notifier.updateLocation(location);
          }
        } else {
          AppSettings.openAppSettings(type: AppSettingsType.location);
        }
      },
      child: Container(
        color: Colors.white,
        height: 50,
        padding: EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            Image.asset(
              AssetsImages.locationPng,
            ),
            SizedBox(width: 5),
            Expanded(
                child: Text(
              notifier.locationDesc,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  fontSize: 14,
                  color:
                      hasPermission ? AppColors.textColor1 : AppColors.orange,
                  fontWeight: FontWeight.w600),
            )),
            if (hasPermission)
              SvgPicture.asset(AssetsSvgs.downArrowSvg)
            else
              Text(
                '去授权',
                style: TextStyle(color: AppColors.primary),
              )
          ],
        ),
      ),
    );
  }

  Widget _buildContent(CreditBranchState state, CreditBranchNotifier notifier) {
    // 如果没有数据，显示空视图
    if (state.branchList.list.isEmpty) {
      return _buildEmptyView(state, notifier);
    }

    // 有数据时，显示带 EasyRefresh 的列表
    return EasyRefresh(
      key: ValueKey(notifier.keyword +
          (notifier.state.address?.latitude ?? 0).toString() +
          (notifier.state.address?.latitude ?? 0).toString()),
      onRefresh: () async {
        final result = await notifier.loadFirstPage();
        return result ? IndicatorResult.success : IndicatorResult.fail;
      },
      onLoad: notifier.hasMorePage
          ? () async {
              final result = await notifier.loadMorePage();
              return result ? IndicatorResult.success : IndicatorResult.fail;
            }
          : null,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: state.branchList.list.length,
        itemBuilder: (context, index) {
          final branch = state.branchList.list[index];
          return _CreditBranchCard(branch: branch);
        },
      ),
    );
  }

  Widget _buildEmptyView(
      CreditBranchState state, CreditBranchNotifier notifier) {
    if (state.isLoading) {
      return const LoadingWidget();
    }
    if (state.hasError) {
      return Center(
        child: ErrorStatusWidget(
            text: state.errorMessage ?? "",
            onAttempt: () => notifier.loadFirstPage()),
      );
    }
    return Center(
      child: EmptyWidget(onAttempt: () => notifier.loadFirstPage()),
    );
  }
}

/// 征信网点卡片组件
class _CreditBranchCard extends StatelessWidget {
  final CreditBranch branch;

  const _CreditBranchCard({
    required this.branch,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final blueColor = Color(0xFF276FF7);
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 0,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              branch.outletsName,
              maxLines: 2,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _address(),
                      const SizedBox(height: 8),
                      _phone(blueColor),
                      SizedBox(height: 3),
                      _type(),
                    ],
                  ),
                ),
                SizedBox(width: 10),
                _distance(blueColor)
              ],
            ),
            // Divider(),

            if (serviceTime().isNotEmpty) ...serviceTime()
          ],
        ),
      ),
    );
  }

  Text _address() {
    return Text(
      branch.detail,
      style: const TextStyle(color: AppColors.textColor9, fontSize: 13),
    );
  }

  _type() {
    final color = Color(0xFFDADADA);
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3, vertical: 2),
      decoration: BoxDecoration(
          color: Colors.white, border: Border.all(color: color, width: 1)),
      child: Text(
        branch.procScopeDesc,
        style: TextStyle(color: color, fontSize: 10),
      ),
    );
  }

  _phone(Color blueColor) {
    if (branch.telephone.isEmpty) {
      return SizedBox();
    }
    return Builder(builder: (context) {
      return GestureDetector(
        onTap: () => launchPhone(context, branch.telephone),
        child: Row(
          children: [
            Icon(Icons.phone, color: blueColor, size: 18),
            const SizedBox(width: 4),
            Text(
              branch.telephone,
              style: TextStyle(
                  color: blueColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 17,
                  height: 24.0 / 17),
            ),
          ],
        ),
      );
    }
    );
  }

  _distance(Color blueColor) {
    return Builder(builder: (context) {
      return GestureDetector(
        onTap: () {
          double? latitude = double.tryParse(branch.lat);
          double? longitude = double.tryParse(branch.lon);
          if (latitude != null && longitude != null) {
            MapUtil.showMapSheet(context, latitude, longitude);
          }
        },
        child: Container(
          decoration: BoxDecoration(
              color: Color(0xFFECF3FE),
              borderRadius: BorderRadius.circular(10)),
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 5),
          child: Row(
            children: [
              Icon(Icons.navigation, color: blueColor, size: 18),
              const SizedBox(width: 4),
              Text(
                branch.distance >= 1000
                    ? "${(branch.distance / 1000).toStringAsFixed(1)}km"
                    : "${branch.distance.toInt()}m",
                style: TextStyle(color: blueColor, fontSize: 13),
              ),
            ],
          ),
        ),
      );
    });
  }

  List<Widget> serviceTime() {
    /// 营业时间
    if (branch.procTime.isNotEmpty) {
      return [
        Divider(),
        const SizedBox(height: 8),
        Text(
          branch.procTime,
          style: const TextStyle(color: Colors.black54, fontSize: 12),
        ),
      ];
    } else {
      return [];
    }
  }
}
