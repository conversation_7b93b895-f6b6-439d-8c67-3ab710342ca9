// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bank_book_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$bankBookNotifierHash() => r'e0a1638c3d8f080a4397a8f366507c7cf58eb395';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$BankBookNotifier
    extends BuildlessAutoDisposeNotifier<BankBookState> {
  late final String bookId;

  BankBookState build(
    String bookId,
  );
}

/// 银行网点查询 Provider
///
/// Copied from [BankBookNotifier].
@ProviderFor(BankBookNotifier)
const bankBookNotifierProvider = BankBookNotifierFamily();

/// 银行网点查询 Provider
///
/// Copied from [BankBookNotifier].
class BankBookNotifierFamily extends Family<BankBookState> {
  /// 银行网点查询 Provider
  ///
  /// Copied from [BankBookNotifier].
  const BankBookNotifierFamily();

  /// 银行网点查询 Provider
  ///
  /// Copied from [BankBookNotifier].
  BankBookNotifierProvider call(
    String bookId,
  ) {
    return BankBookNotifierProvider(
      bookId,
    );
  }

  @override
  BankBookNotifierProvider getProviderOverride(
    covariant BankBookNotifierProvider provider,
  ) {
    return call(
      provider.bookId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'bankBookNotifierProvider';
}

/// 银行网点查询 Provider
///
/// Copied from [BankBookNotifier].
class BankBookNotifierProvider
    extends AutoDisposeNotifierProviderImpl<BankBookNotifier, BankBookState> {
  /// 银行网点查询 Provider
  ///
  /// Copied from [BankBookNotifier].
  BankBookNotifierProvider(
    String bookId,
  ) : this._internal(
          () => BankBookNotifier()..bookId = bookId,
          from: bankBookNotifierProvider,
          name: r'bankBookNotifierProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$bankBookNotifierHash,
          dependencies: BankBookNotifierFamily._dependencies,
          allTransitiveDependencies:
              BankBookNotifierFamily._allTransitiveDependencies,
          bookId: bookId,
        );

  BankBookNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.bookId,
  }) : super.internal();

  final String bookId;

  @override
  BankBookState runNotifierBuild(
    covariant BankBookNotifier notifier,
  ) {
    return notifier.build(
      bookId,
    );
  }

  @override
  Override overrideWith(BankBookNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: BankBookNotifierProvider._internal(
        () => create()..bookId = bookId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        bookId: bookId,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<BankBookNotifier, BankBookState>
      createElement() {
    return _BankBookNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is BankBookNotifierProvider && other.bookId == bookId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, bookId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin BankBookNotifierRef on AutoDisposeNotifierProviderRef<BankBookState> {
  /// The parameter `bookId` of this provider.
  String get bookId;
}

class _BankBookNotifierProviderElement
    extends AutoDisposeNotifierProviderElement<BankBookNotifier, BankBookState>
    with BankBookNotifierRef {
  _BankBookNotifierProviderElement(super.provider);

  @override
  String get bookId => (origin as BankBookNotifierProvider).bookId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
