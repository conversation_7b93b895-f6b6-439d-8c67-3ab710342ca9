// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bank_book_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BankBookState {
  bool get isLoading;
  bool get hasError;
  String? get errorMessage;
  Pagination<BankBook> get branchList;

  /// 详细的位置
  String? get detailAddress;
  LocationDetail? get address;

  /// Create a copy of BankBookState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $BankBookStateCopyWith<BankBookState> get copyWith =>
      _$BankBookStateCopyWithImpl<BankBookState>(
          this as BankBookState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is BankBookState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.hasError, hasError) ||
                other.hasError == hasError) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.branchList, branchList) ||
                other.branchList == branchList) &&
            (identical(other.detailAddress, detailAddress) ||
                other.detailAddress == detailAddress) &&
            (identical(other.address, address) || other.address == address));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isLoading, hasError,
      errorMessage, branchList, detailAddress, address);

  @override
  String toString() {
    return 'BankBookState(isLoading: $isLoading, hasError: $hasError, errorMessage: $errorMessage, branchList: $branchList, detailAddress: $detailAddress, address: $address)';
  }
}

/// @nodoc
abstract mixin class $BankBookStateCopyWith<$Res> {
  factory $BankBookStateCopyWith(
          BankBookState value, $Res Function(BankBookState) _then) =
      _$BankBookStateCopyWithImpl;
  @useResult
  $Res call(
      {bool isLoading,
      bool hasError,
      String? errorMessage,
      Pagination<BankBook> branchList,
      String? detailAddress,
      LocationDetail? address});

  $LocationDetailCopyWith<$Res>? get address;
}

/// @nodoc
class _$BankBookStateCopyWithImpl<$Res>
    implements $BankBookStateCopyWith<$Res> {
  _$BankBookStateCopyWithImpl(this._self, this._then);

  final BankBookState _self;
  final $Res Function(BankBookState) _then;

  /// Create a copy of BankBookState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? hasError = null,
    Object? errorMessage = freezed,
    Object? branchList = null,
    Object? detailAddress = freezed,
    Object? address = freezed,
  }) {
    return _then(_self.copyWith(
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      hasError: null == hasError
          ? _self.hasError
          : hasError // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      branchList: null == branchList
          ? _self.branchList
          : branchList // ignore: cast_nullable_to_non_nullable
              as Pagination<BankBook>,
      detailAddress: freezed == detailAddress
          ? _self.detailAddress
          : detailAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _self.address
          : address // ignore: cast_nullable_to_non_nullable
              as LocationDetail?,
    ));
  }

  /// Create a copy of BankBookState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationDetailCopyWith<$Res>? get address {
    if (_self.address == null) {
      return null;
    }

    return $LocationDetailCopyWith<$Res>(_self.address!, (value) {
      return _then(_self.copyWith(address: value));
    });
  }
}

/// @nodoc

class _BankBookState implements BankBookState {
  const _BankBookState(
      {this.isLoading = false,
      this.hasError = false,
      this.errorMessage,
      this.branchList = const Pagination(
          pageNum: 0, pageSize: 0, totalPage: 0, total: 0, list: []),
      this.detailAddress,
      this.address});

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool hasError;
  @override
  final String? errorMessage;
  @override
  @JsonKey()
  final Pagination<BankBook> branchList;

  /// 详细的位置
  @override
  final String? detailAddress;
  @override
  final LocationDetail? address;

  /// Create a copy of BankBookState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$BankBookStateCopyWith<_BankBookState> get copyWith =>
      __$BankBookStateCopyWithImpl<_BankBookState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _BankBookState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.hasError, hasError) ||
                other.hasError == hasError) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.branchList, branchList) ||
                other.branchList == branchList) &&
            (identical(other.detailAddress, detailAddress) ||
                other.detailAddress == detailAddress) &&
            (identical(other.address, address) || other.address == address));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isLoading, hasError,
      errorMessage, branchList, detailAddress, address);

  @override
  String toString() {
    return 'BankBookState(isLoading: $isLoading, hasError: $hasError, errorMessage: $errorMessage, branchList: $branchList, detailAddress: $detailAddress, address: $address)';
  }
}

/// @nodoc
abstract mixin class _$BankBookStateCopyWith<$Res>
    implements $BankBookStateCopyWith<$Res> {
  factory _$BankBookStateCopyWith(
          _BankBookState value, $Res Function(_BankBookState) _then) =
      __$BankBookStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool hasError,
      String? errorMessage,
      Pagination<BankBook> branchList,
      String? detailAddress,
      LocationDetail? address});

  @override
  $LocationDetailCopyWith<$Res>? get address;
}

/// @nodoc
class __$BankBookStateCopyWithImpl<$Res>
    implements _$BankBookStateCopyWith<$Res> {
  __$BankBookStateCopyWithImpl(this._self, this._then);

  final _BankBookState _self;
  final $Res Function(_BankBookState) _then;

  /// Create a copy of BankBookState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isLoading = null,
    Object? hasError = null,
    Object? errorMessage = freezed,
    Object? branchList = null,
    Object? detailAddress = freezed,
    Object? address = freezed,
  }) {
    return _then(_BankBookState(
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      hasError: null == hasError
          ? _self.hasError
          : hasError // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      branchList: null == branchList
          ? _self.branchList
          : branchList // ignore: cast_nullable_to_non_nullable
              as Pagination<BankBook>,
      detailAddress: freezed == detailAddress
          ? _self.detailAddress
          : detailAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _self.address
          : address // ignore: cast_nullable_to_non_nullable
              as LocationDetail?,
    ));
  }

  /// Create a copy of BankBookState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationDetailCopyWith<$Res>? get address {
    if (_self.address == null) {
      return null;
    }

    return $LocationDetailCopyWith<$Res>(_self.address!, (value) {
      return _then(_self.copyWith(address: value));
    });
  }
}

// dart format on
