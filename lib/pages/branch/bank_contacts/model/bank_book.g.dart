// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bank_book.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_BankBook _$BankBookFromJson(Map<String, dynamic> json) => _BankBook(
      id: json['id'] as String,
      bankId: json['bankId'] as String,
      bankName: json['bankName'] as String,
      bankIcon: json['bankIcon'] as String,
      platformType: json['platformType'] as String,
      platformTypeDesc: json['platformTypeDesc'] as String,
      name: json['name'] as String,
      telephone: json['telephone'] as String,
      lon: json['lon'] as String,
      lat: json['lat'] as String,
      address: json['address'] as String,
      visitCount: (json['visitCount'] as num).toInt(),
      distance: json['distance'] as String,
      enableStatus: (json['enableStatus'] as num).toInt(),
      createTime: json['createTime'] as String,
      updateTime: json['updateTime'] as String,
    );

Map<String, dynamic> _$BankBookToJson(_BankBook instance) => <String, dynamic>{
      'id': instance.id,
      'bankId': instance.bankId,
      'bankName': instance.bankName,
      'bankIcon': instance.bankIcon,
      'platformType': instance.platformType,
      'platformTypeDesc': instance.platformTypeDesc,
      'name': instance.name,
      'telephone': instance.telephone,
      'lon': instance.lon,
      'lat': instance.lat,
      'address': instance.address,
      'visitCount': instance.visitCount,
      'distance': instance.distance,
      'enableStatus': instance.enableStatus,
      'createTime': instance.createTime,
      'updateTime': instance.updateTime,
    };
