import 'package:freezed_annotation/freezed_annotation.dart';

part 'bank_book.freezed.dart';
part 'bank_book.g.dart';

/// 银行网点信息数据模型
@freezed
abstract class BankBook with _$BankBook {
  const factory BankBook({
    required String id,
    required String bankId,
    required String bankName,
    required String bankIcon,
    required String platformType,
    required String platformTypeDesc,
    required String name,
    required String telephone,
    required String lon,
    required String lat,
    required String address,
    required int visitCount,
    required String distance,
    required int enableStatus,
    required String createTime,
    required String updateTime,
  }) = _BankBook;

  factory BankBook.fromJson(Map<String, dynamic> json) =>
      _$BankBookFromJson(json);
}
