// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bank_book.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BankBook {
  String get id;
  String get bankId;
  String get bankName;
  String get bankIcon;
  String get platformType;
  String get platformTypeDesc;
  String get name;
  String get telephone;
  String get lon;
  String get lat;
  String get address;
  int get visitCount;
  String get distance;
  int get enableStatus;
  String get createTime;
  String get updateTime;

  /// Create a copy of BankBook
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $BankBookCopyWith<BankBook> get copyWith =>
      _$BankBookCopyWithImpl<BankBook>(this as BankBook, _$identity);

  /// Serializes this BankBook to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is BankBook &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.bankId, bankId) || other.bankId == bankId) &&
            (identical(other.bankName, bankName) ||
                other.bankName == bankName) &&
            (identical(other.bankIcon, bankIcon) ||
                other.bankIcon == bankIcon) &&
            (identical(other.platformType, platformType) ||
                other.platformType == platformType) &&
            (identical(other.platformTypeDesc, platformTypeDesc) ||
                other.platformTypeDesc == platformTypeDesc) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.telephone, telephone) ||
                other.telephone == telephone) &&
            (identical(other.lon, lon) || other.lon == lon) &&
            (identical(other.lat, lat) || other.lat == lat) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.visitCount, visitCount) ||
                other.visitCount == visitCount) &&
            (identical(other.distance, distance) ||
                other.distance == distance) &&
            (identical(other.enableStatus, enableStatus) ||
                other.enableStatus == enableStatus) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      bankId,
      bankName,
      bankIcon,
      platformType,
      platformTypeDesc,
      name,
      telephone,
      lon,
      lat,
      address,
      visitCount,
      distance,
      enableStatus,
      createTime,
      updateTime);

  @override
  String toString() {
    return 'BankBook(id: $id, bankId: $bankId, bankName: $bankName, bankIcon: $bankIcon, platformType: $platformType, platformTypeDesc: $platformTypeDesc, name: $name, telephone: $telephone, lon: $lon, lat: $lat, address: $address, visitCount: $visitCount, distance: $distance, enableStatus: $enableStatus, createTime: $createTime, updateTime: $updateTime)';
  }
}

/// @nodoc
abstract mixin class $BankBookCopyWith<$Res> {
  factory $BankBookCopyWith(BankBook value, $Res Function(BankBook) _then) =
      _$BankBookCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String bankId,
      String bankName,
      String bankIcon,
      String platformType,
      String platformTypeDesc,
      String name,
      String telephone,
      String lon,
      String lat,
      String address,
      int visitCount,
      String distance,
      int enableStatus,
      String createTime,
      String updateTime});
}

/// @nodoc
class _$BankBookCopyWithImpl<$Res> implements $BankBookCopyWith<$Res> {
  _$BankBookCopyWithImpl(this._self, this._then);

  final BankBook _self;
  final $Res Function(BankBook) _then;

  /// Create a copy of BankBook
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? bankId = null,
    Object? bankName = null,
    Object? bankIcon = null,
    Object? platformType = null,
    Object? platformTypeDesc = null,
    Object? name = null,
    Object? telephone = null,
    Object? lon = null,
    Object? lat = null,
    Object? address = null,
    Object? visitCount = null,
    Object? distance = null,
    Object? enableStatus = null,
    Object? createTime = null,
    Object? updateTime = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      bankId: null == bankId
          ? _self.bankId
          : bankId // ignore: cast_nullable_to_non_nullable
              as String,
      bankName: null == bankName
          ? _self.bankName
          : bankName // ignore: cast_nullable_to_non_nullable
              as String,
      bankIcon: null == bankIcon
          ? _self.bankIcon
          : bankIcon // ignore: cast_nullable_to_non_nullable
              as String,
      platformType: null == platformType
          ? _self.platformType
          : platformType // ignore: cast_nullable_to_non_nullable
              as String,
      platformTypeDesc: null == platformTypeDesc
          ? _self.platformTypeDesc
          : platformTypeDesc // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      telephone: null == telephone
          ? _self.telephone
          : telephone // ignore: cast_nullable_to_non_nullable
              as String,
      lon: null == lon
          ? _self.lon
          : lon // ignore: cast_nullable_to_non_nullable
              as String,
      lat: null == lat
          ? _self.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as String,
      address: null == address
          ? _self.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      visitCount: null == visitCount
          ? _self.visitCount
          : visitCount // ignore: cast_nullable_to_non_nullable
              as int,
      distance: null == distance
          ? _self.distance
          : distance // ignore: cast_nullable_to_non_nullable
              as String,
      enableStatus: null == enableStatus
          ? _self.enableStatus
          : enableStatus // ignore: cast_nullable_to_non_nullable
              as int,
      createTime: null == createTime
          ? _self.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String,
      updateTime: null == updateTime
          ? _self.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _BankBook implements BankBook {
  const _BankBook(
      {required this.id,
      required this.bankId,
      required this.bankName,
      required this.bankIcon,
      required this.platformType,
      required this.platformTypeDesc,
      required this.name,
      required this.telephone,
      required this.lon,
      required this.lat,
      required this.address,
      required this.visitCount,
      required this.distance,
      required this.enableStatus,
      required this.createTime,
      required this.updateTime});
  factory _BankBook.fromJson(Map<String, dynamic> json) =>
      _$BankBookFromJson(json);

  @override
  final String id;
  @override
  final String bankId;
  @override
  final String bankName;
  @override
  final String bankIcon;
  @override
  final String platformType;
  @override
  final String platformTypeDesc;
  @override
  final String name;
  @override
  final String telephone;
  @override
  final String lon;
  @override
  final String lat;
  @override
  final String address;
  @override
  final int visitCount;
  @override
  final String distance;
  @override
  final int enableStatus;
  @override
  final String createTime;
  @override
  final String updateTime;

  /// Create a copy of BankBook
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$BankBookCopyWith<_BankBook> get copyWith =>
      __$BankBookCopyWithImpl<_BankBook>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$BankBookToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _BankBook &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.bankId, bankId) || other.bankId == bankId) &&
            (identical(other.bankName, bankName) ||
                other.bankName == bankName) &&
            (identical(other.bankIcon, bankIcon) ||
                other.bankIcon == bankIcon) &&
            (identical(other.platformType, platformType) ||
                other.platformType == platformType) &&
            (identical(other.platformTypeDesc, platformTypeDesc) ||
                other.platformTypeDesc == platformTypeDesc) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.telephone, telephone) ||
                other.telephone == telephone) &&
            (identical(other.lon, lon) || other.lon == lon) &&
            (identical(other.lat, lat) || other.lat == lat) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.visitCount, visitCount) ||
                other.visitCount == visitCount) &&
            (identical(other.distance, distance) ||
                other.distance == distance) &&
            (identical(other.enableStatus, enableStatus) ||
                other.enableStatus == enableStatus) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      bankId,
      bankName,
      bankIcon,
      platformType,
      platformTypeDesc,
      name,
      telephone,
      lon,
      lat,
      address,
      visitCount,
      distance,
      enableStatus,
      createTime,
      updateTime);

  @override
  String toString() {
    return 'BankBook(id: $id, bankId: $bankId, bankName: $bankName, bankIcon: $bankIcon, platformType: $platformType, platformTypeDesc: $platformTypeDesc, name: $name, telephone: $telephone, lon: $lon, lat: $lat, address: $address, visitCount: $visitCount, distance: $distance, enableStatus: $enableStatus, createTime: $createTime, updateTime: $updateTime)';
  }
}

/// @nodoc
abstract mixin class _$BankBookCopyWith<$Res>
    implements $BankBookCopyWith<$Res> {
  factory _$BankBookCopyWith(_BankBook value, $Res Function(_BankBook) _then) =
      __$BankBookCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String bankId,
      String bankName,
      String bankIcon,
      String platformType,
      String platformTypeDesc,
      String name,
      String telephone,
      String lon,
      String lat,
      String address,
      int visitCount,
      String distance,
      int enableStatus,
      String createTime,
      String updateTime});
}

/// @nodoc
class __$BankBookCopyWithImpl<$Res> implements _$BankBookCopyWith<$Res> {
  __$BankBookCopyWithImpl(this._self, this._then);

  final _BankBook _self;
  final $Res Function(_BankBook) _then;

  /// Create a copy of BankBook
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? bankId = null,
    Object? bankName = null,
    Object? bankIcon = null,
    Object? platformType = null,
    Object? platformTypeDesc = null,
    Object? name = null,
    Object? telephone = null,
    Object? lon = null,
    Object? lat = null,
    Object? address = null,
    Object? visitCount = null,
    Object? distance = null,
    Object? enableStatus = null,
    Object? createTime = null,
    Object? updateTime = null,
  }) {
    return _then(_BankBook(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      bankId: null == bankId
          ? _self.bankId
          : bankId // ignore: cast_nullable_to_non_nullable
              as String,
      bankName: null == bankName
          ? _self.bankName
          : bankName // ignore: cast_nullable_to_non_nullable
              as String,
      bankIcon: null == bankIcon
          ? _self.bankIcon
          : bankIcon // ignore: cast_nullable_to_non_nullable
              as String,
      platformType: null == platformType
          ? _self.platformType
          : platformType // ignore: cast_nullable_to_non_nullable
              as String,
      platformTypeDesc: null == platformTypeDesc
          ? _self.platformTypeDesc
          : platformTypeDesc // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      telephone: null == telephone
          ? _self.telephone
          : telephone // ignore: cast_nullable_to_non_nullable
              as String,
      lon: null == lon
          ? _self.lon
          : lon // ignore: cast_nullable_to_non_nullable
              as String,
      lat: null == lat
          ? _self.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as String,
      address: null == address
          ? _self.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      visitCount: null == visitCount
          ? _self.visitCount
          : visitCount // ignore: cast_nullable_to_non_nullable
              as int,
      distance: null == distance
          ? _self.distance
          : distance // ignore: cast_nullable_to_non_nullable
              as String,
      enableStatus: null == enableStatus
          ? _self.enableStatus
          : enableStatus // ignore: cast_nullable_to_non_nullable
              as int,
      createTime: null == createTime
          ? _self.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String,
      updateTime: null == updateTime
          ? _self.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
