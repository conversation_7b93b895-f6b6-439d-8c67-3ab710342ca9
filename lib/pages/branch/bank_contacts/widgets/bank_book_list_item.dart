import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';
import '../model/bank_book.dart';

/// 银行网点卡片组件，展示网点名称、电话、距离、拨号按钮等
class BankBookListItem extends StatelessWidget {
  final BankBook book;
  final VoidCallback? onCall;
  final VoidCallback? onNavigate;

  BankBookListItem({
    Key? key,
    required this.book,
    this.onCall,
    this.onNavigate,
  }) : super(key: key);

  final blueColor = Color(0xFF276FF7);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(book.name,
              style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textColor3)),
          const SizedBox(height: 4),
          // Text(book.address, style: const TextStyle(color: Colors.grey)),
          const SizedBox(height: 8),
          Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: _phone(),
              ),
              SizedBox(width: 8),
              _distance(),
              SizedBox(width: 8),
              GestureDetector(
                onTap: () {
                  launchPhone(context, book.telephone);
                },
                child: Container(
                  width: 45,
                  height: 23,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(13),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xFF51ABFF),
                        Color(0xFF2872FD),
                      ],
                    ),
                  ),
                  child: const Icon(
                    Icons.phone_enabled,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  _phone() {
    return Text(
      book.telephone,
      style: TextStyle(
          color: AppColors.textColor9, fontSize: 14, height: 20.0 / 14),
    );
  }

  _distance() {
    double distance = double.tryParse(book.distance) ?? 0;

    String distanceStr = distance >= 1000
        ? "${(distance / 1000).toStringAsFixed(1)}km"
        : "${distance.toInt()}m";

    return Builder(builder: (context) {
      return GestureDetector(
        onTap: () {
          double? latitude = double.tryParse(book.lat);
          double? longitude = double.tryParse(book.lon);
          if (latitude != null && longitude != null) {
            MapUtil.showMapSheet(context, latitude, longitude);
          }
        },
        child: Container(
          decoration: BoxDecoration(
              color: Color(0xFFECF3FE),
              borderRadius: BorderRadius.circular(10)),
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 5),
          child: Row(
            children: [
              Icon(Icons.navigation, color: blueColor, size: 18),
              const SizedBox(width: 4),
              Text(
                // book.distance >= 1000
                //     ? "${(branch.distance / 1000).toStringAsFixed(1)}km"
                //     : "${branch.distance.toInt()}m",
                distanceStr,
                style: TextStyle(color: blueColor, fontSize: 13),
              ),
            ],
          ),
        ),
      );
    });
  }
}
