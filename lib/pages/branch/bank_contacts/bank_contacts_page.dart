import 'package:app_settings/app_settings.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/branch/bank_contacts/provider/bank_book_provider.dart';
import 'package:zrreport/pages/branch/credit_branch/provider/credit_branch_provider.dart';
import 'package:zrreport/pages/branch/provider/location_provider.dart';
import 'package:zrreport/pages/location/location_main/location_main_page.dart';
import 'widgets/bank_book_list_item.dart';

/// 银行网点通讯录主页面
class BankContactsPage extends ConsumerStatefulWidget {
  final String bankId;
  final String bankName;
  BankContactsPage({super.key, required this.bankId, required this.bankName});

  @override
  _BankContactsPageState createState() => _BankContactsPageState();
}

class _BankContactsPageState extends ConsumerState<BankContactsPage>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // 这里调用定位权限检查
      ref
          .read(locationNotifierProvider.notifier)
          .checkAndUpdateLocationPermission();
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(bankBookNotifierProvider(widget.bankId));
    final notifier =
        ref.watch(bankBookNotifierProvider(widget.bankId).notifier);

    if (!ref.read(locationNotifierProvider).hasLocationPermission) {
      ref
          .read(locationNotifierProvider.notifier)
          .getCurrentLocationAndResetLocation();
    }

    // 如果没有数据，显示空视图
    var color = Color(0xFFFAF8F9);
    return Scaffold(
      appBar: AppBar(
        backgroundColor: color,
        title: Text(widget.bankName),
        elevation: 0,
      ),
      backgroundColor: color,
      body: Column(
        children: [
          _buildLocationBar(context),
          Expanded(child: Container(child: _buildContent(state, notifier))),
        ],
      ),
    );
  }

  Widget _buildLocationBar(BuildContext context) {
    final notifier =
        ref.watch(bankBookNotifierProvider(widget.bankId).notifier);

    bool hasPermission =
        ref.read(locationNotifierProvider).hasLocationPermission;

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () async {
        bool hasPermission =
            ref.read(locationNotifierProvider).hasLocationPermission;

        if (hasPermission) {
          final location = await Navigator.push<LocationDetail>(
            context,
            MaterialPageRoute(
              builder: (context) => LocationMainPage(
                currentLocation: ref.read(creditBranchNotifierProvider).address,
              ),
            ),
          );
          if (location != null) {
            notifier.updateLocation(location);
          }
        } else {
          AppSettings.openAppSettings(type: AppSettingsType.location);
        }
      },
      child: Container(
        color: Colors.white,
        height: 50,
        padding: EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            Image.asset(
              AssetsImages.locationPng,
            ),
            SizedBox(width: 5),
            Expanded(
                child: Text(
              notifier.locationDesc,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  fontSize: 14,
                  color:
                      hasPermission ? AppColors.textColor1 : AppColors.orange,
                  fontWeight: FontWeight.w600),
            )),
            if (hasPermission)
              SvgPicture.asset(AssetsSvgs.downArrowSvg)
            else
              Text(
                '去授权',
                style: TextStyle(color: AppColors.primary),
              )
          ],
        ),
      ),
    );
  }

  Widget _buildContent(BankBookState state, BankBookNotifier notifier) {
    // 如果没有数据，显示空视图
    if (state.branchList.list.isEmpty) {
      return _buildEmptyView(state, notifier);
    }

    // 有数据时，显示带 EasyRefresh 的列表
    return EasyRefresh(
      key: ValueKey(notifier.keyword +
          (notifier.state.address?.latitude ?? 0).toString() +
          (notifier.state.address?.latitude ?? 0).toString()),
      onRefresh: () async {
        final result = await notifier.loadFirstPage();
        return result ? IndicatorResult.success : IndicatorResult.fail;
      },
      onLoad: notifier.hasMorePage
          ? () async {
              final result = await notifier.loadMorePage();
              return result ? IndicatorResult.success : IndicatorResult.fail;
            }
          : null,
      child: ListView.separated(
        separatorBuilder: (BuildContext context, int index) =>
            Divider(height: 1.0, color: AppColors.dividerColor),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: state.branchList.list.length,
        itemBuilder: (context, index) {
          final branch = state.branchList.list[index];
          return BankBookListItem(
            book: branch,
          );
        },
      ),
    );
  }

  Widget _buildEmptyView(BankBookState state, BankBookNotifier notifier) {
    if (state.isLoading) {
      return const LoadingWidget();
    }
    if (state.hasError) {
      return Center(
        child: ErrorStatusWidget(
            text: state.errorMessage ?? "",
            onAttempt: () => notifier.loadFirstPage()),
      );
    }
    return Center(
      child: EmptyWidget(onAttempt: () => notifier.loadFirstPage()),
    );
  }
}
