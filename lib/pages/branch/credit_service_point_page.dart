import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/branch/bank_branch/provider/bank_branch_provider.dart';
import 'package:zrreport/pages/branch/credit_branch/model/credit_branch_model.dart';
import 'package:zrreport/pages/branch/credit_branch/provider/credit_branch_provider.dart';
import 'package:zrreport/pages/branch/widget/branch_search_bar.dart';
import 'package:zrreport/pages/branch/widget/branch_search_viewmodel.dart';
import 'bank_branch/bank_branch_list.dart';
import 'bank_branch/model/bank_branch_model.dart';
import 'credit_branch/credit_branch_list.dart';

enum ShowType { credit, bank }

/// 征信网点/银行通讯录查询页面
class CreditServicePointPage extends ConsumerStatefulWidget {
  const CreditServicePointPage({super.key});

  @override
  ConsumerState createState() => _CreditServicePointPageState();
}

class _CreditServicePointPageState
    extends ConsumerState<CreditServicePointPage> {
  ShowType type = ShowType.credit;

  String? currentKeyword;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(bankBranchNotifierProvider, (previous, next) {});
    ref.listen(creditBranchNotifierProvider, (previous, next) {});

    return DefaultTabController(
      length: 2,
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Scaffold(
          appBar: AppBar(
            iconTheme: IconThemeData(
              color: Colors.white, //修改颜色
            ),
            title: const Text(
              '征信网点查询',
              style: TextStyle(color: Colors.white),
            ),
            surfaceTintColor: Colors.white,
            backgroundColor: const Color(0xFF488AFD),
            elevation: 0,
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(140),
              child: Column(
                children: [
                  type == ShowType.credit
                      ? _buildCreditSearchBar()
                      : _buildBankSearchBar(),
                  SizedBox(height: 16),
                  _buildMenu(),
                ],
              ),
            ),
          ),
          backgroundColor: Color(0xFFFAF8F9),
          body: type == ShowType.credit ? CreditBranchList() : BankBranchList(),
        ),
      ),
    );
  }

  _buildMenu() {
    return Container(
      height: 69,
      alignment: Alignment.center,
      margin: EdgeInsets.only(left: 16, right: 16, bottom: 16),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () => setState(() {
                type = ShowType.credit;
              }),
              child: Container(
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: type == ShowType.credit
                        ? Border.all(color: AppColors.orange, width: 2)
                        : null),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(AssetsImages.creditBranchIconPng),
                    SizedBox(width: 12),
                    Text('征信网点查询',
                        style: TextStyle(
                            fontSize: 15,
                            color: AppColors.textColor1,
                            fontWeight: FontWeight.w600)),
                  ],
                ),
              ),
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () => setState(() {
                type = ShowType.bank;
              }),
              child: Container(
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: type == ShowType.bank
                        ? Border.all(color: AppColors.orange, width: 1)
                        : null),
                child: Container(
                  alignment: Alignment.center,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(AssetsImages.bankContactsIconPng),
                      SizedBox(width: 12),
                      Text('银行通讯录',
                          style: TextStyle(
                              fontSize: 15,
                              color: AppColors.textColor1,
                              fontWeight: FontWeight.w600)),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  BranchSearchBar<BankBranch> _buildBankSearchBar() {
    return BranchSearchBar<BankBranch>(
      text: currentKeyword,
      viewModel: BranchSearchViewModel<BankBranch>(
        (keyword, pageNum, pageSize, cancelToken) async {
          currentKeyword = keyword;
          if (keyword.isEmpty) {
            return Pagination.empty();
          }
          final result = await BranchApi.getBankBranch(
              keyword: keyword, pageNum: pageNum, pageSize: pageSize);
          if (result.data != null) {
            return result.data!;
          } else {
            return Pagination.empty();
          }
        },
      ),
      hint: '请输入征信网点/银行名称',
      optionItemBuilder: (context, item) =>
          BranchSearchBar.buildOptionItem(item.name),
      onSearchButtonAction: (pagination, keyword) {
        if (keyword.isEmpty) {
          revertAllProvider();
        } else {
          ref
              .read(bankBranchNotifierProvider.notifier)
              .update(pagination, keyword);
        }
      },
      onSelected: (option, keyword) {
        final pagination = Pagination(
            pageNum: 1, pageSize: 1, totalPage: 1, total: 1, list: [option]);
        ref
            .read(bankBranchNotifierProvider.notifier)
            .update(pagination, keyword);
      },
    );
  }

  BranchSearchBar<CreditBranch> _buildCreditSearchBar() {
    return BranchSearchBar<CreditBranch>(
      text: currentKeyword,
      viewModel: BranchSearchViewModel<CreditBranch>(
        (keyword, pageNum, pageSize, cancelToken) async {
          currentKeyword = keyword;
          final location = ref.read(creditBranchNotifierProvider).address;
          if (location?.latitude == null || location?.longitude == null) {
            return Pagination.empty();
          }

          if (keyword.isEmpty) {
            return Pagination.empty();
          }

          final result = await BranchApi.getCreditBranch(
              pageNum: pageNum,
              pageSize: pageSize,
              keyword: keyword,
              lon: location!.longitude,
              lat: location.latitude,
              cancelToken: cancelToken);
          if (result.data != null) {
            return result.data!;
          } else {
            return Pagination.empty();
          }
        },
      ),
      hint: '请输入征信网点/银行名称',
      optionItemBuilder: (context, item) =>
          BranchSearchBar.buildOptionItem(item.outletsName),
      onSearchButtonAction: (p0, keyword) {
        if (keyword.isEmpty) {
          revertAllProvider();
        } else {
          ref.read(creditBranchNotifierProvider.notifier).update(p0, keyword);
        }
      },
      onSelected: (option, keyword) {
        final pagination = Pagination(
            pageNum: 1, pageSize: 1, totalPage: 1, total: 1, list: [option]);
        ref
            .read(creditBranchNotifierProvider.notifier)
            .update(pagination, keyword);
        FocusManager.instance.primaryFocus?.unfocus();
      },
    );
  }

  void revertAllProvider() {
    ref.read(creditBranchNotifierProvider.notifier).revert();
    ref.read(bankBranchNotifierProvider.notifier).revert();
  }
}
