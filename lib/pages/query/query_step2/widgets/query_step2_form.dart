import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/query/widgets/index.dart';

import 'VerificationCodeButton.dart';

typedef QueryStep2Action = void Function(
    BuildContext context, String phone, String password, String smsCode);

typedef SendSmsFutureCallback = Future<void> Function(
    BuildContext context, String phone);

final agreeTermsForQueryProvider = StateProvider.autoDispose(((ref) => false));

/// 查询第一步内容
// ignore: must_be_immutable
class QueryStep2Form extends ConsumerWidget {
  QueryStep2Form({
    Key? key,
    required this.info,
    required this.isNeedSmsCode,
    required this.onQuery,
    required this.onSendSmsCodeCallback,
  }) : super(key: key);

  QueryStep2Action onQuery;

  CompanyInfoModel info;

  SendSmsFutureCallback onSendSmsCodeCallback;

  bool isNeedSmsCode = false;

  final TextEditingController phoneEditingController =
      TextEditingController(text: defaultCompany.phone);
  final TextEditingController passwordEditingController =
      TextEditingController(text: defaultCompany.password);

  final TextEditingController smsCodeEditingController =
      TextEditingController();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          // 纳税人识别号
          buildFormHeader(title: '纳税人识别号：'),
          SizedBox(height: 8),
          buildDisableTextFiled(
              text: info.creditCodeOrTaxPayerId, isEnable: false),
          SizedBox(height: 12),

          //企业名称：
          buildFormHeader(title: '企业名称：'),
          SizedBox(height: 8),
          buildDisableTextFiled(text: info.taxpayerName, isEnable: false),

          SizedBox(height: 12),

          //电子税务局账号：：
          buildFormHeader(title: '电子税务局账号：'),
          SizedBox(height: 8),
          buildTextFiled(
              hint: '请输入手机号',
              controller: phoneEditingController,
              textInputAction: TextInputAction.next),

          SizedBox(height: 12),

          //登录密码：
          buildFormHeader(title: '登录密码：'),
          SizedBox(height: 8),
          buildTextFiled(
              hint: '请输入税务密码',
              controller: passwordEditingController,
              textInputAction:
                  isNeedSmsCode ? TextInputAction.next : TextInputAction.done),

          SizedBox(height: 12),

          if (isNeedSmsCode) ...[
            buildFormHeader(title: '手机验证码：'),
            SizedBox(height: 8),
            buildSmsCodeTextFiled(context,
                hint: '请输入手机验证码',
                controller: smsCodeEditingController,
                textInputAction: TextInputAction.done),
          ],
          SizedBox(height: 12),
          buildFilledButton("查询", onPressed: () {
            final isAgree = ref.watch(agreeTermsForQueryProvider);
            if (!isAgree) {
              Loading.error('请先同意税务授权协议');
              return;
            }
            onQuery(context, phoneEditingController.text,
                passwordEditingController.text, smsCodeEditingController.text);
          }, fontWeight: FontWeight.bold),

          SizedBox(height: 12),
          buildTermView(ref),
        ]));
  }

  buildFormHeader({required String title}) {
    return Text(
      title,
      textAlign: TextAlign.start,
      style: TextStyle(
          fontSize: 15, fontWeight: FontWeight.bold, color: Colors.black),
    ).paddingLeft(12);
  }

  TextField buildDisableTextFiled({
    String? text,
    bool isEnable = true,
  }) {
    return TextField(
      controller: TextEditingController(),
      enabled: isEnable,
      decoration: InputDecoration(
        isDense: true,
        hintText: text,
        hintStyle: TextStyle(
            fontSize: 14,
            color: AppColors.textColor6.withAlpha(200),
            height: 20 / 14),
        hintMaxLines: 1,
        fillColor: Color(0xFFF1F5F9),
        filled: true,
        contentPadding:
            EdgeInsetsDirectional.symmetric(horizontal: 24, vertical: 10),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(20),
          borderSide: BorderSide.none,
        ),
      ),
    );
  }

  TextField buildTextFiled({
    String? hint,
    required TextInputAction textInputAction,
    required TextEditingController controller,
  }) {
    return TextField(
      controller: controller,
      textInputAction: textInputAction,
      style: TextStyle(color: Color(0xFF222222), fontSize: 14),
      decoration: InputDecoration(
        isDense: true,
        hintText: hint,
        hintStyle: TextStyle(
            fontSize: 14,
            color: Color(0xFFCCCCCC).withAlpha(200),
            height: 20 / 14),
        hintMaxLines: 1,
        contentPadding:
            EdgeInsetsDirectional.symmetric(horizontal: 24, vertical: 10),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(20),
          borderSide: BorderSide(
            color: Color(0xFFDDDDDD), // 边框颜色
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(20),
          borderSide: BorderSide(
            color: Color(0xFFDDDDDD), // 边框颜色
          ),
        ),
      ),
    );
  }

  TextField buildSmsCodeTextFiled(
    BuildContext context, {
    String? hint,
    required TextInputAction textInputAction,
    required TextEditingController controller,
  }) {
    return TextField(
      controller: controller,
      textInputAction: textInputAction,
      style: TextStyle(color: Color(0xFF222222), fontSize: 14),
      decoration: InputDecoration(
        isDense: true,
        hintText: hint,
        hintStyle: TextStyle(
            fontSize: 14,
            color: Color(0xFFCCCCCC).withAlpha(200),
            height: 20 / 14),
        hintMaxLines: 1,
        contentPadding:
            EdgeInsetsDirectional.symmetric(horizontal: 24, vertical: 10),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(20),
          borderSide: BorderSide(
            color: Color(0xFFDDDDDD), // 边框颜色
          ),
        ),
        suffixIcon: VerificationCodeButton(
          sendAction: () async {
            await onSendSmsCodeCallback(context, phoneEditingController.text);
          },
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(20),
          borderSide: BorderSide(
            color: Color(0xFFDDDDDD), // 边框颜色
          ),
        ),
      ),
    );
  }

  Widget buildTermView(WidgetRef ref) {
    final isAgree = ref.watch(agreeTermsForQueryProvider);
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Checkbox(
          value: isAgree,
          shape: CircleBorder(),
          onChanged: (value) {
            ref.read(agreeTermsForQueryProvider.notifier).state =
                value ?? false;
          },
        ).tightSize(30),
        RichText(
          maxLines: 2,
          text: TextSpan(
            style: const TextStyle(color: Colors.grey),
            children: [
              const TextSpan(text: '阅读并同意'),
              TextSpan(
                  text: '《税务授权协议》',
                  style: TextStyle(color: AppColors.primary, fontSize: 14),
                  recognizer: TapGestureRecognizer()
                    ..onTap =
                        () => gotoWeb(UrlPath.userAgreement, title: '用户协议')),
            ],
          ),
        ).expanded(),
      ],
    );
  }
}
