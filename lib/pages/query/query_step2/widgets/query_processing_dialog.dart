import 'package:flutter/material.dart';

/// 授权处理中弹窗
class QueryProcessingDialog extends StatelessWidget {
  const QueryProcessingDialog({Key? key}) : super(key: key);

  /// 显示弹窗的静态方法
  static Future<void> show(BuildContext context) async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => const QueryProcessingDialog(),
    );
  }

  static void dismiss(BuildContext context) {
    Navigator.of(context).maybePop();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      insetPadding: const EdgeInsets.symmetric(horizontal: 32),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        padding:
            const EdgeInsets.only(left: 24, right: 24, top: 20, bottom: 79),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '温馨提示',
              style: TextStyle(
                fontSize: 20,
                color: Color(0xFF222222),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 28),
            // 加载动画
            const SizedBox(
              height: 70,
              width: 70,
              child: Center(
                child: CircularProgressIndicator(
                  strokeWidth: 4,
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFBDBDBD)),
                ),
              ),
            ),
            const SizedBox(height: 28),
            const Text(
              '授权中，请耐心等待…',
              style: TextStyle(
                fontSize: 16,
                color: Color(0xFF4D8CFF),
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
