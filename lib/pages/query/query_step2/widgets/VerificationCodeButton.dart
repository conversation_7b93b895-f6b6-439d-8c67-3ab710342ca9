import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';

class VerificationCodeState {
  final bool isCountingDown;
  final int remainingSeconds;
  final bool canResend;

  VerificationCodeState({
    required this.isCountingDown,
    required this.remainingSeconds,
    required this.canResend,
  });

  VerificationCodeState copyWith({
    bool? isCountingDown,
    int? remainingSeconds,
    bool? canResend,
  }) {
    return VerificationCodeState(
      isCountingDown: isCountingDown ?? this.isCountingDown,
      remainingSeconds: remainingSeconds ?? this.remainingSeconds,
      canResend: canResend ?? this.canResend,
    );
  }
}

class VerificationCodeNotifier extends StateNotifier<VerificationCodeState> {
  VerificationCodeNotifier()
      : super(VerificationCodeState(
          isCountingDown: false,
          remainingSeconds: 0,
          canResend: true,
        ));

  sendVerificationCodeSuccess() {
    if (state.isCountingDown || !state.canResend) return;

    state = state.copyWith(
      isCountingDown: true,
      remainingSeconds: 10,
      canResend: false,
    );

    // 启动倒计时
    _startCountdown();
  }

  // 开始倒计时
  void _startCountdown() {
    Future.delayed(const Duration(seconds: 1), () {
      if (state.remainingSeconds <= 1) {
        // 倒计时结束
        state = state.copyWith(
          isCountingDown: false,
          remainingSeconds: 0,
          canResend: true,
        );
      } else {
        // 更新剩余时间
        state = state.copyWith(
          remainingSeconds: state.remainingSeconds - 1,
        );
        // 继续倒计时
        _startCountdown();
      }
    });
  }

  // 重置状态
  void reset() {
    state = VerificationCodeState(
      isCountingDown: false,
      remainingSeconds: 0,
      canResend: true,
    );
  }
}

// 创建提供者
final verificationCodeProvider =
    StateNotifierProvider<VerificationCodeNotifier, VerificationCodeState>(
  (ref) => VerificationCodeNotifier(),
);

typedef FutureCallback = Future<void> Function();

class VerificationCodeButton extends ConsumerWidget {
  VerificationCodeButton({super.key, required this.sendAction});

  FutureCallback sendAction;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(verificationCodeProvider);
    final notifier = ref.read(verificationCodeProvider.notifier);

    return GestureDetector(
      onTap: state.canResend
          ? () async {
              await sendAction();
              notifier.sendVerificationCodeSuccess();
            }
          : null,
      child: Container(
        width: 110,
        margin: EdgeInsets.all(0.5),
        decoration: BoxDecoration(
          color: state.canResend ? AppColors.primary : Colors.grey,
          borderRadius: BorderRadius.horizontal(right: Radius.circular(20)),
        ),
        alignment: Alignment.center,
        child: Text(
          state.isCountingDown ? '${state.remainingSeconds}s后重发' : '获取验证码',
          style: TextStyle(
              color: state.canResend ? Colors.white : Colors.white,
              fontWeight: FontWeight.w600,
              fontSize: 13.5),
        ),
      ),
    );
  }
}
