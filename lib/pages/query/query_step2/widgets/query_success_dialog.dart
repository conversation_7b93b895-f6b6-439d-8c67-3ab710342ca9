import 'dart:async';
import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';

class QuerySuccessDialog extends StatefulWidget {
  const QuerySuccessDialog({Key? key}) : super(key: key);

  @override
  State<QuerySuccessDialog> createState() => _QuerySuccessDialogState();

  /// 显示弹窗的静态方法
  static Future<void> show(BuildContext context) async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => const QuerySuccessDialog(),
    );
  }
}

class _QuerySuccessDialogState extends State<QuerySuccessDialog> {
  int _seconds = 5;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _startCountdown();
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_seconds == 1) {
        timer.cancel();
        Navigator.of(context).maybePop();
      } else {
        setState(() {
          _seconds--;
        });
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      insetPadding: const EdgeInsets.symmetric(horizontal: 32),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '授权成功',
              style: TextStyle(
                fontSize: 20,
                color: Color(0xFF222222),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 26),
            // 图标
            SizedBox(
              height: 64,
              width: 64,
              child: Image.asset(
                AssetsImages.querySuccessPng,
                fit: BoxFit.contain,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              '请勿在5分钟内登录税局系统！',
              style: const TextStyle(
                fontSize: 16,
                color: Color(0xFF222222),
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4D8CFF),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                  elevation: 0,
                ),
                onPressed: () {
                  Navigator.of(context).maybePop();
                },
                child: Text(
                  _seconds > 0 ? '我知道了 ($_seconds)' : '我知道了',
                  style: const TextStyle(
                    fontSize: 18,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
