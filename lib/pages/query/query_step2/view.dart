// export 'view.dart';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/query/query_step2/widgets/query_processing_dialog.dart';
import 'package:zrreport/pages/query/query_step2/widgets/query_success_dialog.dart';

import '../widgets/index.dart';
import 'widgets/query_step2_form.dart';

/// 需要验证码的地区
final noNeedSmsCodeAreas = ["zhejiang", "hubei", "guangdong"];

final showSmsCodeTextFieldProvider =
    StateProvider.autoDispose(((ref) => false));

class QueryStep2Page extends StatelessWidget {
  QueryStep2Page({super.key, required this.info});

  CompanyInfoModel info;

  TaxLoginModel? _smsCodeLoginModel;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(title: Text('助融报告')),
        backgroundColor: Color(0xFFF4F8FB),
        body: _buildView(context));
  }

  _buildView(context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            alignment: Alignment.topCenter,
            image: AssetImage(AssetsImages.queryCompanyBackgroundPng),
            fit: BoxFit.fitWidth,
          ),
        ),
        padding: EdgeInsets.symmetric(horizontal: 16),
        child: ListView(children: [
          SizedBox(height: 16),
          QueryHeaderWidget(currentStep: 2),
          SizedBox(height: 12),
          Consumer(builder: (context, ref, _) {
            final showSmsCode = ref.watch(showSmsCodeTextFieldProvider);
            return QueryStep2Form(
              info: info,
              onQuery: (BuildContext context, String phone, String password,
                  String smsCode) {
                onQuery(context, ref, phone, password, smsCode);
              },
              onSendSmsCodeCallback: (context, phone) async {
                await sendSmsCode(context, phone);
              },
              isNeedSmsCode: showSmsCode,
            );
          }),
        ]),
      ),
    );
  }

  Future<void> onQuery(BuildContext context, WidgetRef ref, String phone,
      String password, String smsCode) async {

    if (phone.isEmpty) {
      Loading.error('请输入手机号');
      return;
    }

    if (password.isEmpty) {
      Loading.error('请输入税务密码');
      return;
    }

    bool isNeedSmsCode = !noNeedSmsCodeAreas.contains(info.provinceName);
    if (!isNeedSmsCode) {
      await _onlyAccountLogin(context, phone, password);
    } else {
      if (_smsCodeLoginModel == null) {
        _login(context, ref, phone, password);
      } else {
        _captchaLogin(context, ref, phone, password, smsCode);
      }
    }
  }

  Future<void> _login(BuildContext context, WidgetRef ref, String phone,
      String password) async {
    final entity = TaxNeedSmsCOdeLoginEntity(
      queryChannel: '1',
      creditCodeOrTaxPayerId: info.creditCodeOrTaxPayerId,
      phoneNumber: phone,
      provinceName: info.provinceName,
      provinceId: info.provinceId,
      taxpayerName: info.taxpayerName,
      password: password,
      source: 'query',
      channelType: 'app',
      appid: 'wx3843eed9287d8daa',
    );

    QueryProcessingDialog.show(context);
    try {
      final result = await QueryApi.taxLogin(entity: entity);
      defaultLogger.debug('tax login success, result:$result');
      _smsCodeLoginModel = result.data;
      ref.read(showSmsCodeTextFieldProvider.notifier).state = true;
      QueryProcessingDialog.dismiss(context);
    } catch (e) {
      QueryProcessingDialog.dismiss(context);
      Loading.error('$e');
    }
  }

  Future<void> sendSmsCode(BuildContext context, String phone) async {
    if (_smsCodeLoginModel == null) {
      Loading.error('无登录信息');
      return;
    }
    final entity = TaxSendSmsCodeEntity(
      creditCodeOrTaxPayerId: info.creditCodeOrTaxPayerId,
      uuid: _smsCodeLoginModel!.uuid,
      phone: phone,
    );

    QueryProcessingDialog.show(context);
    try {
      final result = await QueryApi.taxSendSmsCode(entity: entity);
      defaultLogger.debug('tax login success, result:$result');
      QueryProcessingDialog.dismiss(context);
    } catch (e) {
      Loading.error('$e');
      rethrow;
    }
  }

  Future<void> _captchaLogin(BuildContext context, WidgetRef ref, String phone,
      String password, String smsCode) async {
    if (smsCode.isEmpty) {
      Loading.error('请输入验证码');
      return;
    }
    final entity = TaxCaptchaLoginEntity(
        provinceName: info.provinceName,
        creditCodeOrTaxPayerId: info.creditCodeOrTaxPayerId,
        phoneNumber: phone,
        password: password,
        code: smsCode,
        enterpriseId: _smsCodeLoginModel!.enterpriseId,
        uuid: _smsCodeLoginModel!.uuid,
        source: 'app');

    QueryProcessingDialog.show(context);
    try {
      final result = await QueryApi.taxCaptchaLogin(entity: entity);
      defaultLogger.debug('tax login success, result:$result');
      QueryProcessingDialog.dismiss(context);

      onSuccess(context);
    } catch (e) {
      QueryProcessingDialog.dismiss(context);
      Loading.error('$e');
    }
  }

  Future<void> onSuccess(BuildContext context) async {
    await QuerySuccessDialog.show(context);
    Navigator.of(context).popUntil(ModalRoute.withName(RouteNames.systemMain));
  }

  Future<void> _onlyAccountLogin(
      BuildContext context, String phone, String password) async {
    QueryProcessingDialog.show(context);
    try {
      final entity = TaxOnlyAccountLoginEntity(
          queryChannel: '1',
          provinceName: info.provinceName,
          creditCode: info.creditCodeOrTaxPayerId,
          phone: phone,
          password: password,
          provinceId: info.provinceId,
          taxpayerName: info.taxpayerName,
          source: 'query',
          channelType: 'app',
          appid: 'wx3843eed9287d8daa');
      final result = await QueryApi.taxOnlyAccountLogin(entity: entity);
      QueryProcessingDialog.dismiss(context);
      defaultLogger.debug('tax login success, result:$result');
      onSuccess(context);
    } catch (e) {
      defaultLogger.debug('tax login failed, error:$e');
      QueryProcessingDialog.dismiss(context);
      Loading.error('$e');
    } finally {}
  }
}
