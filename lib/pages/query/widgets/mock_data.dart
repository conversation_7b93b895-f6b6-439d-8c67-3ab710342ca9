import 'package:flutter/foundation.dart';

class TaxMockCompany {
  String provice;
  String name;
  String creditCodeOrTaxPayerId;
  String phone;
  String password;

  TaxMockCompany(this.provice, this.name, this.creditCodeOrTaxPayerId,
      this.phone, this.password);
}

List mockDatas = [
  TaxMockCompany(
      '广东', '广东公司', '91442000MA4UJL5D0Y', '13960292007', 'zmy183600'),
  TaxMockCompany(
      '浙江', '浙江公司', '91330110MA2GLF8W3C', '15540848041', 'a12345678'),
  TaxMockCompany(
      '湖北', '湖北公司', '91420107768067296N', '13045924976', 'HNtax168168'),

  // 下面的需要验证码
  TaxMockCompany('河南', '河南省医药超市有限公司郑州郑密路店', '91410102397788464P', '13251434889',
      '123456789aA'),
  TaxMockCompany(
      '陕西', '榆林汇跑商贸有限公司', '91610800MA70ANJWX1', '13018740814', 'Aa12345678.'),
  TaxMockCompany('青海', '青海北交新能源科技有限公司', '91632500MA757KAHXR', '15501204284',
      'zx06061745.'),
  TaxMockCompany(
      '山东', '济南福东临珠宝金行有限公司', '913701027797236824', '18513784004', '283766ka'),
  TaxMockCompany(
      '青岛', '青岛姐弟俩餐饮管理有限公司', '91370211MAD90N8105', '18559028460', 'Aa111111'),
];

TaxMockCompany get defaultCompany {
  if (kDebugMode) {
    return mockDatas[2];
  }
  return TaxMockCompany("", "", "", "", "");
}
// TaxMockCompany defaultCompany = mockDatas[2];
