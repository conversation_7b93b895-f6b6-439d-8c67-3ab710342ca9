import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';

class QueryHeaderWidget extends StatelessWidget {
  /// 当前高亮步骤（1、2、3）
  final int currentStep;
  final List<String> stepTitles = ['公司信息', '税务信息', '获取数据'];

  QueryHeaderWidget({
    Key? key,
    required this.currentStep,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
      child: Column(
        children: [
          Text('税务授权登录',
              style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black)),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // 1
              _StepCircle(number: '1', isHilight: currentStep >= 1),
              // 2
              CustomPaint(painter: _DashedLinePainter(currentStep >= 2))
                  .expanded(),

              _StepCircle(number: '2', isHilight: currentStep >= 2),
              // 3
              Expanded(
                  child: CustomPaint(
                      painter: _DashedLinePainter(currentStep >= 3))),

              _StepCircle(number: '3', isHilight: currentStep > 3),
            ],
          ).padding(horizontal: 16),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: List.generate(3, (index) {
              final isPass = currentStep >= index + 1;
              return Center(
                child: Text(
                  stepTitles[index],
                  style: TextStyle(
                    fontSize: 13,
                    color: isPass ? Color(0xFF222222) : Color(0xFF999999),
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
}

class _StepCircle extends StatelessWidget {
  final String number;
  final bool isHilight;

  const _StepCircle({
    required this.number,
    required this.isHilight,
  });

  @override
  Widget build(BuildContext context) {
    Color bgColor;
    Color textColor;
    if (isHilight) {
      bgColor = Color(0xFF409EFF); // 蓝色
      textColor = Colors.white;
    } else {
      bgColor = Colors.transparent;
      textColor = Color(0xFFcccccc);
    }
    return Container(
      width: 16,
      height: 16,
      decoration: BoxDecoration(
          color: bgColor,
          shape: BoxShape.circle,
          border: isHilight ? null : Border.all(color: textColor)),
      alignment: Alignment.center,
      child: Text(
        number,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}

class _DashedLinePainter extends CustomPainter {
  _DashedLinePainter(this.isHilight) : super();
  bool isHilight = false;

  @override
  void paint(Canvas canvas, Size size) {
    const dashWidth = 2.0;
    const dashSpace = 1.0;
    double startX = 0;
    final paint = Paint()
      ..color = isHilight ? AppColors.primary : Color(0xFFCCCCCC)
      ..strokeWidth = 1;
    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, 0),
        Offset(startX + dashWidth, 0),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
