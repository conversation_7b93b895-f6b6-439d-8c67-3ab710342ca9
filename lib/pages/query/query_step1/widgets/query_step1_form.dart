import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/query/widgets/index.dart';

/// 查询第一步内容
// ignore: must_be_immutable
class QueryStep1Form extends StatelessWidget {
  QueryStep1Form(
      {Key? key, required this.onQuery, String? creditCodeOrTaxPayerId})
      : super(key: key) {
    creditCodeEditingController.text = creditCodeOrTaxPayerId ?? '';
  }

  ValueChanged<String> onQuery;


  final creditCodeEditingController =
      TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text(
            '公司名称',
            textAlign: TextAlign.start,
            style: TextStyle(
                fontSize: 15, fontWeight: FontWeight.bold, color: Colors.black),
          ).paddingLeft(12),
          SizedBox(height: 12),
          buildFiled().height(49),
          SizedBox(height: 32),
          buildFilledButton("查询",
              onPressed: () => onQuery(creditCodeEditingController.text),
              fontWeight: FontWeight.bold)
        ]));
  }

  TextField buildFiled() {
    return TextField(
      controller: creditCodeEditingController,
      style: TextStyle(color: Color(0xFF222222), fontSize: 14),
      decoration: InputDecoration(
        isDense: true,
        hintText: '请输入公司名称或税号',
        hintStyle: TextStyle(fontSize: 14, color: Color(0xFFCCCCCC)),
        hintMaxLines: 1,
        fillColor: Color(0xFFF1F5F9),
        filled: true,
        contentPadding: EdgeInsets.symmetric(horizontal: 24, vertical: 10),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(20),
          borderSide: BorderSide.none,
        ),
        // contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
        // constraints: const BoxConstraints(maxHeight: 49),
      ),
    );
  }
}
