// export 'view.dart';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get/get.dart';
import 'package:zrreport/common/index.dart';

import '../widgets/index.dart';
import 'widgets/query_step1_form.dart';

// ignore: must_be_immutable
class QueryStep1Page extends ConsumerWidget {
  QueryStep1Page({super.key, this.creditCodeOrTaxPayerId});

  String? creditCodeOrTaxPayerId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
        appBar: AppBar(title: Text('助融报告')),
        backgroundColor: Color(0xFFF4F8FB),
        body: _buildView(context, ref));
  }

  _buildView(context, WidgetRef ref) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            alignment: Alignment.topCenter,
            image: AssetImage(AssetsImages.queryCompanyBackgroundPng),
            fit: BoxFit.fitWidth,
          ),
        ),
        padding: EdgeInsets.symmetric(horizontal: 16),
        child: ListView(children: [
          SizedBox(height: 16),
          _buildUserWidget(ref),
          SizedBox(height: 12),
          QueryHeaderWidget(currentStep: 1),
          SizedBox(height: 12),
          QueryStep1Form(
              onQuery: onQuery, creditCodeOrTaxPayerId: creditCodeOrTaxPayerId),
        ]),
      ),
    );
  }

  _buildUserWidget(ref) {
    final profile = ref.read(userProfileNotifierProvider);
    final nickStyle = TextStyle(
        color: Colors.white,
        fontSize: 14,
        fontWeight: FontWeight.w600,
        height: 20 / 14);

    final phoneStyle =
        TextStyle(color: Colors.white, fontSize: 11, height: 15 / 11);
    return Row(
      children: [
        AvatarWidget(
          size: 44,
          imageUrl: profile?.icon,
        ),
        SizedBox(width: 12),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(profile?.nickname ?? '', style: nickStyle),
            Text('手机号:${profile?.phone ?? ''}', style: phoneStyle),
          ],
        )
      ],
    );
  }

  Future<void> onQuery(String creditCode) async {
    if (creditCode.isEmpty) {
      Loading.error('请输入公司名称或税号');
      return;
    }
    Loading.show();
    try {
      final result = await QueryApi.queryCompanyInfo(creditCode);
      defaultLogger.debug('queryCompanyInfo success, result:$result');
      if (result.data != null) {
        Get.toNamed(RouteNames.queryStep2, arguments: {'company': result.data});
      } else {
        Loading.error('查询公司信息失败');
      }
    } catch (e) {
      defaultLogger.debug('queryCompanyInfo failed,error:$e');
      Loading.error('$e');
    } finally {
      Loading.dismiss();
    }
  }
}
