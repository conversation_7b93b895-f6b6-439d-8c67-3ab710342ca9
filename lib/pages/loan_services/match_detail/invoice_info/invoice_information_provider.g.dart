// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'invoice_information_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$invoiceInformationShowButtonHash() =>
    r'e81dc80a15071110d8ed1984468e02c6c2330652';

/// See also [InvoiceInformationShowButton].
@ProviderFor(InvoiceInformationShowButton)
final invoiceInformationShowButtonProvider =
    AutoDisposeNotifierProvider<InvoiceInformationShowButton, bool>.internal(
  InvoiceInformationShowButton.new,
  name: r'invoiceInformationShowButtonProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$invoiceInformationShowButtonHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InvoiceInformationShowButton = AutoDisposeNotifier<bool>;
String _$invoiceInformationHash() =>
    r'859499cd281a4e44f2e417de595d4a925fb985fe';

/// See also [InvoiceInformation].
@ProviderFor(InvoiceInformation)
final invoiceInformationProvider = AutoDisposeNotifierProvider<
    InvoiceInformation, InvoiceInformationViewModel>.internal(
  InvoiceInformation.new,
  name: r'invoiceInformationProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$invoiceInformationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InvoiceInformation = AutoDisposeNotifier<InvoiceInformationViewModel>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
