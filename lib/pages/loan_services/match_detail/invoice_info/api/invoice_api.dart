import 'package:zrreport/common/index.dart';

import '../model/match_invoice_info.dart';

class InvoiceApi {
  /// 供应商
  static Future<BaseResponse<SupplierInformation>> invoiceTopTen(
      String matchid) async {
    final response = await SXHttpService.to.get(
      '/cmsMatchEnterprise/getEnterpriseInvoiceTopTen/$matchid',
    );
    return BaseResponse.fromJson(
      response.data,
      (data) => SupplierInformation.fromJson(data as Map<String, dynamic>),
    );
  }

    /// 开具发票
  static Future<BaseResponse<MatchInvoiceInfo>> issueInvoice(
      String matchid) async {
    final response = await SXHttpService.to.get(
        '/cmsMatchEnterprise/getEnterpriseApplyAmount/$matchid',
        );
    return BaseResponse.fromJson(
      response.data,
      (data) => MatchInvoiceInfo.fromJson(data as Map<String, dynamic>),
    );
  }

}
