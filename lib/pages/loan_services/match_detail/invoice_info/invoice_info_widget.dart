import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:zrreport/common/index.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../report_detail/widget/left_right_cell.dart';
import 'invoice_information_provider.dart';
import 'supplier_information_provider.dart';

class InvoiceInfoWidget extends StatelessWidget {
  const InvoiceInfoWidget({super.key, required this.matchId});

  final String matchId;

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ProviderScope.containerOf(context)
          .read(supplierInfoProvider.notifier)
          .loadData(matchId);

      ProviderScope.containerOf(context)
          .read(invoiceInformationProvider.notifier)
          .setType(matchId);
    });

    return Consumer(
      builder: (context, ref, child) {
        final invoiceInformation = ref.watch(invoiceInformationProvider);
        switch (invoiceInformation.pageStatus) {
          case PageStatus.initial:
          case PageStatus.loading:
            return Center(
              child: LoadingWidget(),
            );
          case PageStatus.success:
            return Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
              child: Column(
                children: [

                  // 头部列表
                  ...invoiceInformation.getInvoiceHeadList.map((e) {
                    return LeftRightCell(cellData: e);
                  }),

                  CustomDataGrid(
                      title: '开票对比分析',
                      header: invoiceInformation.getInvoiceTableHeadData2,
                      tableData: invoiceInformation.getInvoiceTableData2),
                  SizedBox(height: 16),

                  CustomDataGrid(
                      title: '近三年开票总金额',
                      header: invoiceInformation.latest3YearsDataHeader,
                      tableData: invoiceInformation.latest3YearsData),
                  SizedBox(height: 16),

                  CustomDataGrid(
                      title: '近三年无票收入',
                      header:
                          invoiceInformation.latest3YearsNoInvoiceDataHeader,
                      tableData: invoiceInformation.latest3YearsNoInvoiceData),
                  SizedBox(height: 16),

                  CustomDataGrid(
                      title: '红冲金额',
                      header: invoiceInformation.readDataHeader,
                      tableData: invoiceInformation.readData),
                  SizedBox(height: 16),

                  CustomDataGrid(
                      title: '作废金额',
                      header: invoiceInformation.invalidateDataHeader,
                      tableData: invoiceInformation.invalidateData),
                  SizedBox(height: 16),

                  SupplierInformationBodyView(matchId: matchId),
                ],
              ),
            );
          case PageStatus.empty:
            return Center(
              child: Text(''),
            );
          case PageStatus.error:
            return Center(
              child: Text(''),
            );
        }
      },
    );
  }
}

class SupplierInformationBodyView extends HookWidget {
  final String matchId;
  const SupplierInformationBodyView({super.key, required this.matchId});

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      final response = ref.read(supplierInfoProvider);
      return Column(
        children: [
          CustomDataGrid(
              title: '前十供应商信息（近三年）',
              header: response.tableHeadData1,
              tableData: response.tableData1),
          SizedBox(height: 16),
          CustomDataGrid(
              title: '前十销售客户信息（近三年）',
              header: response.tableHeadData1,
              tableData: response.tableData2),
        ],
      );
    });
  }
}
