
import 'package:zrreport/common/index.dart';

class MatchInvoiceInfo {
    bool? billingRecords;
    String? billingDaysSum;
    String? taxVoidRateMonth12;
    int? breakMonthsSum12;
    int? breakMonthsSum;
    String? taxAmountGrowthRateMonth12;
    String? taxOutputNotInvoicedMonth12;
    String? earliestTime;
    String? latestTime;
    String? recentBillingGapdays;

    /// 近3年开票金额信息
    List<InvoiceInfoDataList>? invoiceInfoDataList;

    /// 开票数据
    List<EnterpriseMonthApplyList>? enterpriseMonthApplyList;

    /// 近3年红冲金额信息
    List<InvoiceInfoDataList>? redInvoiceInfoDataList;

    /// 近3年作废金额信息
    List<InvoiceInfoDataList>? invoiceCountDataList;

    /// 近3年无票金额信息
    List<InvoiceInfoDataList>? applySalesDataList;

    MatchInvoiceInfo({
        this.billingRecords,
        this.billingDaysSum,
        this.taxVoidRateMonth12,
        this.breakMonthsSum12,
        this.breakMonthsSum,
        this.taxAmountGrowthRateMonth12,
        this.taxOutputNotInvoicedMonth12,
        this.earliestTime,
        this.latestTime,
        this.recentBillingGapdays,
        this.invoiceInfoDataList,
        this.enterpriseMonthApplyList,
        this.redInvoiceInfoDataList,
        this.invoiceCountDataList,
        this.applySalesDataList,
    });

    factory MatchInvoiceInfo.fromJson(Map<String, dynamic> json) => MatchInvoiceInfo(
        billingRecords: json["billingRecords"],
        billingDaysSum: json["billingDaysSum"],
        taxVoidRateMonth12: json["taxVoidRateMonth12"],
        breakMonthsSum12: json["breakMonthsSum12"],
        breakMonthsSum: json["breakMonthsSum"],
        taxAmountGrowthRateMonth12: json["taxAmountGrowthRateMonth12"],
        taxOutputNotInvoicedMonth12: json["taxOutputNotInvoicedMonth12"],
        earliestTime: json["earliestTime"],
        latestTime: json["latestTime"],
        recentBillingGapdays: json["recentBillingGapdays"],
        invoiceInfoDataList: json["invoiceInfoDataList"] == null ? [] : List<InvoiceInfoDataList>.from(json["invoiceInfoDataList"]!.map((x) => InvoiceInfoDataList.fromJson(x))),
        enterpriseMonthApplyList: json["enterpriseMonthApplyList"] == null ? [] : List<EnterpriseMonthApplyList>.from(json["enterpriseMonthApplyList"]!.map((x) => EnterpriseMonthApplyList.fromJson(x))),
        redInvoiceInfoDataList: json["redInvoiceInfoDataList"] == null ? [] : List<InvoiceInfoDataList>.from(json["redInvoiceInfoDataList"]!.map((x) => InvoiceInfoDataList.fromJson(x))),
        invoiceCountDataList: json["invoiceCountDataList"] == null ? [] : List<InvoiceInfoDataList>.from(json["invoiceCountDataList"]!.map((x) => InvoiceInfoDataList.fromJson(x))),
        applySalesDataList: json["applySalesDataList"] == null ? [] : List<InvoiceInfoDataList>.from(json["applySalesDataList"]!.map((x) => InvoiceInfoDataList.fromJson(x))),
    );
}