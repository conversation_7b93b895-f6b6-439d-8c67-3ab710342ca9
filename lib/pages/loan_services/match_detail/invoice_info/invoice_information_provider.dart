import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/loan_services/match_detail/invoice_info/api/invoice_api.dart';
import 'package:zrreport/pages/report_detail/widget/listItem.dart';

import 'model/match_invoice_info.dart';
part 'invoice_information_provider.g.dart';

enum InvoiceInformationViewType { issueInvoice, declarationData, getInvoice }

class TopBottomCellData {
  final String top;
  final String bottom;
  const TopBottomCellData({required this.top, required this.bottom});
}

class InvoiceInformationViewModel {
  var pageStatus = PageStatus.initial;
  var type = InvoiceInformationViewType.issueInvoice;
  MatchInvoiceInfo? invoiceInfo;

  InvoiceInformationViewModel copyWith({
    required PageStatus pageStatus,
    MatchInvoiceInfo? invoiceInfo,
  }) {
    return InvoiceInformationViewModel()
      ..pageStatus = pageStatus
      ..type = type ?? this.type
      ..invoiceInfo = invoiceInfo ?? this.invoiceInfo;
  }

  bool get hasInvoiceInfoDataList =>
      invoiceInfo!.invoiceInfoDataList != null &&
      invoiceInfo!.invoiceInfoDataList!.isNotEmpty;

  /// 近3年开票金额信息 : 表头
  List<String> get latest3YearsDataHeader =>
      getCommonHeader(invoiceInfo?.invoiceInfoDataList ?? []);
  // 近3年开票金额信息 : 表内容
  List<List<String>> get latest3YearsData =>
      getCommonGridValues(invoiceInfo?.invoiceInfoDataList ?? []);

  // List<String> get declarationDataTableHeadData2 =>
  //     getCommonHeader(invoiceInfo?.invoiceInfoDataList ?? []);

  // // 近三年申报总金额: 表内容
  // List<List<String>> get declarationDataTableData2 =>
  //     getCommonGridValues(invoiceInfo?.invoiceInfoDataList ?? []);

  /// 近三年无票收入: 表头
  List<String> get latest3YearsNoInvoiceDataHeader =>
      getCommonHeader(invoiceInfo?.applySalesDataList ?? []);

  // 近三年无票收入: 表内容
  List<List<String>> get latest3YearsNoInvoiceData =>
      getCommonGridValues(invoiceInfo?.applySalesDataList ?? []);


    /// 红冲金额: 表头
  List<String> get readDataHeader =>
      getCommonHeader(invoiceInfo?.redInvoiceInfoDataList ?? []);

  // 红冲金额: 表内容
  List<List<String>> get readData {
    return getCommonGridValues(invoiceInfo?.redInvoiceInfoDataList ?? []);
  }

    /// 作废金额: 表头
  List<String> get invalidateDataHeader =>
      getCommonHeader(invoiceInfo?.redInvoiceInfoDataList ?? []);

  // 作废金额: 表内容
  List<List<String>> get invalidateData  {
    return getCommonGridValues(invoiceInfo?.redInvoiceInfoDataList ?? []);
  }

  /// ---------------取得发票逻辑处理---------------
  List<ListItemViewData> get getInvoiceHeadList {
    List<ListItemViewData> res = [
      ListItemViewData(
        dark: true,
        title: '近45日是否有取得发票记录',
        value: invoiceInfo!.billingRecords != null
            ? (invoiceInfo!.billingRecords! ? '有' : '无')
            : '_',
      ),
      ListItemViewData(
        dark: false,
        title: '近30天取得发票金额',
        value: invoiceInfo!.billingDaysSum != null
            ? formatLargeNumber(invoiceInfo!.billingDaysSum!.toDouble())
            : '_',
      ),
      ListItemViewData(
        dark: true,
        title: '近12月作废发票数量占比',
        value: invoiceInfo!.taxVoidRateMonth12 != null
            ? invoiceInfo!.taxVoidRateMonth12!
            : '_',
      ),
      ListItemViewData(
        dark: false,
        title: '近12个月断票月数',
        value: invoiceInfo!.breakMonthsSum12 != null
            ? '${invoiceInfo!.breakMonthsSum12!}'
            : '_',
      ),
      ListItemViewData(
        dark: true,
        title: '近12个月月均取得发票金额同比增长率',
        value: invoiceInfo!.taxAmountGrowthRateMonth12 != null
            ? '${invoiceInfo!.taxAmountGrowthRateMonth12!}%'
            : '_',
      ),
      ListItemViewData(
        dark: false,
        title: '最早取得发票时间',
        value: invoiceInfo!.earliestTime != null
            ? invoiceInfo!.earliestTime!
            : '_',
      ),
      ListItemViewData(
        dark: true,
        title: '最晚取得发票时间',
        value: invoiceInfo!.latestTime != null ? invoiceInfo!.latestTime! : '_',
      ),
    ];
    return res;
  }

  bool get hasGetInvoiceEnterpriseMonthApplyList =>
      invoiceInfo!.enterpriseMonthApplyList != null &&
      invoiceInfo!.enterpriseMonthApplyList!.isNotEmpty;

  /// 表头
  List<String> get getInvoiceTableHeadData2 {
    List<String> res = ['开票对比分析'];
    for (var value in invoiceInfo!.enterpriseMonthApplyList!) {
      res.add('近${value.month ?? '_'}个月');
    }
    return res;
  }

  // 表内容
  List<List<String>> get getInvoiceTableData2 {
    List<List<String>> res = [];
    int rows = 9;
    int cols = invoiceInfo!.enterpriseMonthApplyList!.length;
    const titles = [
      '取票金额',
      '取票环比增长率',
      '取票同比增长率',
      '取票张数',
      '取票月数',
      '红冲发票张数占比',
      '红冲金额占比',
      '上游客户数量',
      '下游客户数量'
    ];
    for (var i = 0; i < rows; i++) {
      List<String> inner = [];
      for (var j = 0; j < cols + 1; j++) {
        if (j == 0) {
          inner.add(titles[i]);
        } else {
          final itemData = invoiceInfo!.enterpriseMonthApplyList![j - 1];
          if (i == 0) {
            // 开票金额
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(itemData.taxEnterpriseApplyDate!.invoiceAmount != null
                  ? formatLargeNumber(itemData
                      .taxEnterpriseApplyDate!.invoiceAmount!
                      .toDouble())
                  : '_');
            } else {
              inner.add('_');
            }
          } else if (i == 1) {
            // 开票环比增长率
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(
                  '${itemData.taxEnterpriseApplyDate!.invoiceGrowthRate ?? '-'}%');
            } else {
              inner.add('-%');
            }
          } else if (i == 2) {
            // 开票同比增长率
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(
                  '${itemData.taxEnterpriseApplyDate!.invoiceOverGrowthRate ?? '-'}%');
            } else {
              inner.add('-%');
            }
          } else if (i == 3) {
            // 开票张数
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(
                  '${itemData.taxEnterpriseApplyDate!.invoiceCount ?? '_'}');
            } else {
              inner.add('_');
            }
          } else if (i == 4) {
            // 开票月数
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(
                  itemData.taxEnterpriseApplyDate!.invoiceMonthsNum ?? '_');
            } else {
              inner.add('_');
            }
          } else if (i == 5) {
            // 红冲发票张数占比
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(
                  '${itemData.taxEnterpriseApplyDate!.invoiceRedCountRate ?? '-'}%');
            } else {
              inner.add('-%');
            }
          } else if (i == 6) {
            // 红冲金额占比
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(
                  '${itemData.taxEnterpriseApplyDate!.invoiceRedAmountRate ?? '-'}%');
            } else {
              inner.add('-%');
            }
          } else if (i == 7) {
            // 上游客户数量
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(
                  '${itemData.taxEnterpriseApplyDate!.customerUpNum ?? '_'}');
            } else {
              inner.add('_');
            }
          } else {
            // 下游客户数量
            if (itemData.taxEnterpriseApplyDate != null) {
              inner.add(
                  '${itemData.taxEnterpriseApplyDate!.customerDownNum ?? '_'}');
            } else {
              inner.add('_');
            }
          }
        }
      }
      res.add(inner);
    }
    return res;
  }


}

List<String> getCommonHeader(List<InvoiceInfoDataList> list) {
  List<String> res = ['月份'];
  for (var value in list) {
    res.add('${value.year ?? '_'}年');
  }
  return res;
}


List<List<String>> getCommonGridValues(List<InvoiceInfoDataList> list) {
  List<List<String>> res = [];
  if (list.first.monthData != null && list.first.monthData!.isNotEmpty) {
    int rows = list.first.monthData!.length;
    int cols = list.length;
    var totalList = List<double>.filled(cols, 0, growable: false);
    for (var i = 0; i < rows + 1; i++) {
      List<String> inner = [];
      for (var j = 0; j < cols + 1; j++) {
        if (j == 0) {
          if (i == rows) {
            // 最后一行是合计信息
            inner.add('合计');
          } else {
            inner.add('${list[0].monthData![i].name ?? '_'}月');
          }
        } else {
          if (i == rows) {
            // 最后一行是合计信息
            inner.add(formatLargeNumber(totalList[j - 1]));
          } else {
            if (list[j - 1].monthData![i].value == null) {
              inner.add('_');
            } else {
              final num = list[j - 1].monthData![i].value!.toDouble();
              inner.add(formatLargeNumber(num));
              totalList[j - 1] += num;
            }
          }
        }
      }
      res.add(inner);
    }
  }
  return res;
}

@riverpod
class InvoiceInformationShowButton extends _$InvoiceInformationShowButton {
  @override
  bool build() {
    return false;
  }

  void hidden() {
    if (state != false) {
      state = false;
    }
  }

  void show() {
    if (state != true) {
      state = true;
    }
  }
}

@riverpod
class InvoiceInformation extends _$InvoiceInformation {
  @override
  InvoiceInformationViewModel build() {
    return InvoiceInformationViewModel();
  }

  Future<void> setType(String matchId) async {
    try {
      state.pageStatus = PageStatus.loading;
      final response = await InvoiceApi.issueInvoice(matchId);
      if (response.data != null) {
        state = state.copyWith(
          pageStatus: PageStatus.success,
          invoiceInfo: response.data,
        );
      } else {
        state = state.copyWith(pageStatus: PageStatus.empty);
      }
    } catch (e) {
      state = state.copyWith(pageStatus: PageStatus.error);
      print(e);
    }
  }
}
