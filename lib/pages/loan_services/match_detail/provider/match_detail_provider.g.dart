// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'match_detail_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$matchDetailNotifierHash() =>
    r'442480d29478d5ef2b31749bdb9d95b43eceb9e6';

/// 匹配详情页面状态管理
///
/// Copied from [MatchDetailNotifier].
@ProviderFor(MatchDetailNotifier)
final matchDetailNotifierProvider =
    AutoDisposeNotifierProvider<MatchDetailNotifier, MatchDetailState>.internal(
  MatchDetailNotifier.new,
  name: r'matchDetailNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$matchDetailNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MatchDetailNotifier = AutoDisposeNotifier<MatchDetailState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
