import 'package:freezed_annotation/freezed_annotation.dart';

part 'match_detail_models.freezed.dart';
part 'match_detail_models.g.dart';

/// 匹配详情页面参数
@freezed
abstract class MatchDetailParams with _$MatchDetailParams {
  const factory MatchDetailParams({
    required String id,
    required int type,
  }) = _MatchDetailParams;

  factory MatchDetailParams.fromJson(Map<String, dynamic> json) =>
      _$MatchDetailParamsFromJson(json);
}

/// 匹配摘要数据模型
@freezed
abstract class MatchSummaryData with _$MatchSummaryData {
  const factory MatchSummaryData({
    required String enterpriseName,
    required int matchStatus,
    required int matchStep,
    required String reportCreateTime,
    required int productNum,
    required String totalQuota,
    @Default('') String enterpriseId,
    required String creditCode,
    required bool question,
    required String memberId,
  }) = _MatchSummaryData;

  factory MatchSummaryData.fromJson(Map<String, dynamic> json) =>
      _$MatchSummaryDataFromJson(json);
}

/// 匹配详情页面状态
@freezed
abstract class MatchDetailState with _$MatchDetailState {
  const factory MatchDetailState({
    @Default(0) int selectedTabIndex,
    MatchSummaryData? summaryData,
    @Default(false) bool isLoading,
    String? errorMessage,
  }) = _MatchDetailState;

  factory MatchDetailState.fromJson(Map<String, dynamic> json) =>
      _$MatchDetailStateFromJson(json);
}

/// 标签页枚举
enum MatchDetailTab {
  enterpriseInfo('企业信息'),
  taxInfo('纳税信息'),
  invoiceInfo('开票信息'),
  matchResult('匹配结果');

  const MatchDetailTab(this.title);
  final String title;
}
