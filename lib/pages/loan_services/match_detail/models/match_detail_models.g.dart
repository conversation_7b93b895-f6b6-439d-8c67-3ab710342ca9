// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'match_detail_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MatchDetailParams _$MatchDetailParamsFromJson(Map<String, dynamic> json) =>
    _MatchDetailParams(
      id: json['id'] as String,
      type: (json['type'] as num).toInt(),
    );

Map<String, dynamic> _$MatchDetailParamsToJson(_MatchDetailParams instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
    };

_MatchSummaryData _$MatchSummaryDataFromJson(Map<String, dynamic> json) =>
    _MatchSummaryData(
      enterpriseName: json['enterpriseName'] as String,
      matchStatus: (json['matchStatus'] as num).toInt(),
      matchStep: (json['matchStep'] as num).toInt(),
      reportCreateTime: json['reportCreateTime'] as String,
      productNum: (json['productNum'] as num).toInt(),
      totalQuota: json['totalQuota'] as String,
      enterpriseId: json['enterpriseId'] as String? ?? '',
      creditCode: json['creditCode'] as String,
      question: json['question'] as bool,
      memberId: json['memberId'] as String,
    );

Map<String, dynamic> _$MatchSummaryDataToJson(_MatchSummaryData instance) =>
    <String, dynamic>{
      'enterpriseName': instance.enterpriseName,
      'matchStatus': instance.matchStatus,
      'matchStep': instance.matchStep,
      'reportCreateTime': instance.reportCreateTime,
      'productNum': instance.productNum,
      'totalQuota': instance.totalQuota,
      'enterpriseId': instance.enterpriseId,
      'creditCode': instance.creditCode,
      'question': instance.question,
      'memberId': instance.memberId,
    };

_MatchDetailState _$MatchDetailStateFromJson(Map<String, dynamic> json) =>
    _MatchDetailState(
      selectedTabIndex: (json['selectedTabIndex'] as num?)?.toInt() ?? 0,
      summaryData: json['summaryData'] == null
          ? null
          : MatchSummaryData.fromJson(
              json['summaryData'] as Map<String, dynamic>),
      isLoading: json['isLoading'] as bool? ?? false,
      errorMessage: json['errorMessage'] as String?,
    );

Map<String, dynamic> _$MatchDetailStateToJson(_MatchDetailState instance) =>
    <String, dynamic>{
      'selectedTabIndex': instance.selectedTabIndex,
      'summaryData': instance.summaryData,
      'isLoading': instance.isLoading,
      'errorMessage': instance.errorMessage,
    };
