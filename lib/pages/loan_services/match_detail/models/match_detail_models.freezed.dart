// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'match_detail_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MatchDetailParams {
  String get id;
  int get type;

  /// Create a copy of MatchDetailParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MatchDetailParamsCopyWith<MatchDetailParams> get copyWith =>
      _$MatchDetailParamsCopyWithImpl<MatchDetailParams>(
          this as MatchDetailParams, _$identity);

  /// Serializes this MatchDetailParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MatchDetailParams &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, type);

  @override
  String toString() {
    return 'MatchDetailParams(id: $id, type: $type)';
  }
}

/// @nodoc
abstract mixin class $MatchDetailParamsCopyWith<$Res> {
  factory $MatchDetailParamsCopyWith(
          MatchDetailParams value, $Res Function(MatchDetailParams) _then) =
      _$MatchDetailParamsCopyWithImpl;
  @useResult
  $Res call({String id, int type});
}

/// @nodoc
class _$MatchDetailParamsCopyWithImpl<$Res>
    implements $MatchDetailParamsCopyWith<$Res> {
  _$MatchDetailParamsCopyWithImpl(this._self, this._then);

  final MatchDetailParams _self;
  final $Res Function(MatchDetailParams) _then;

  /// Create a copy of MatchDetailParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _MatchDetailParams implements MatchDetailParams {
  const _MatchDetailParams({required this.id, required this.type});
  factory _MatchDetailParams.fromJson(Map<String, dynamic> json) =>
      _$MatchDetailParamsFromJson(json);

  @override
  final String id;
  @override
  final int type;

  /// Create a copy of MatchDetailParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MatchDetailParamsCopyWith<_MatchDetailParams> get copyWith =>
      __$MatchDetailParamsCopyWithImpl<_MatchDetailParams>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MatchDetailParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MatchDetailParams &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, type);

  @override
  String toString() {
    return 'MatchDetailParams(id: $id, type: $type)';
  }
}

/// @nodoc
abstract mixin class _$MatchDetailParamsCopyWith<$Res>
    implements $MatchDetailParamsCopyWith<$Res> {
  factory _$MatchDetailParamsCopyWith(
          _MatchDetailParams value, $Res Function(_MatchDetailParams) _then) =
      __$MatchDetailParamsCopyWithImpl;
  @override
  @useResult
  $Res call({String id, int type});
}

/// @nodoc
class __$MatchDetailParamsCopyWithImpl<$Res>
    implements _$MatchDetailParamsCopyWith<$Res> {
  __$MatchDetailParamsCopyWithImpl(this._self, this._then);

  final _MatchDetailParams _self;
  final $Res Function(_MatchDetailParams) _then;

  /// Create a copy of MatchDetailParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? type = null,
  }) {
    return _then(_MatchDetailParams(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
mixin _$MatchSummaryData {
  String get enterpriseName;
  int get matchStatus;
  int get matchStep;
  String get reportCreateTime;
  int get productNum;
  String get totalQuota;
  String get enterpriseId;
  String get creditCode;
  bool get question;
  String get memberId;

  /// Create a copy of MatchSummaryData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MatchSummaryDataCopyWith<MatchSummaryData> get copyWith =>
      _$MatchSummaryDataCopyWithImpl<MatchSummaryData>(
          this as MatchSummaryData, _$identity);

  /// Serializes this MatchSummaryData to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MatchSummaryData &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.matchStatus, matchStatus) ||
                other.matchStatus == matchStatus) &&
            (identical(other.matchStep, matchStep) ||
                other.matchStep == matchStep) &&
            (identical(other.reportCreateTime, reportCreateTime) ||
                other.reportCreateTime == reportCreateTime) &&
            (identical(other.productNum, productNum) ||
                other.productNum == productNum) &&
            (identical(other.totalQuota, totalQuota) ||
                other.totalQuota == totalQuota) &&
            (identical(other.enterpriseId, enterpriseId) ||
                other.enterpriseId == enterpriseId) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.question, question) ||
                other.question == question) &&
            (identical(other.memberId, memberId) ||
                other.memberId == memberId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      enterpriseName,
      matchStatus,
      matchStep,
      reportCreateTime,
      productNum,
      totalQuota,
      enterpriseId,
      creditCode,
      question,
      memberId);

  @override
  String toString() {
    return 'MatchSummaryData(enterpriseName: $enterpriseName, matchStatus: $matchStatus, matchStep: $matchStep, reportCreateTime: $reportCreateTime, productNum: $productNum, totalQuota: $totalQuota, enterpriseId: $enterpriseId, creditCode: $creditCode, question: $question, memberId: $memberId)';
  }
}

/// @nodoc
abstract mixin class $MatchSummaryDataCopyWith<$Res> {
  factory $MatchSummaryDataCopyWith(
          MatchSummaryData value, $Res Function(MatchSummaryData) _then) =
      _$MatchSummaryDataCopyWithImpl;
  @useResult
  $Res call(
      {String enterpriseName,
      int matchStatus,
      int matchStep,
      String reportCreateTime,
      int productNum,
      String totalQuota,
      String enterpriseId,
      String creditCode,
      bool question,
      String memberId});
}

/// @nodoc
class _$MatchSummaryDataCopyWithImpl<$Res>
    implements $MatchSummaryDataCopyWith<$Res> {
  _$MatchSummaryDataCopyWithImpl(this._self, this._then);

  final MatchSummaryData _self;
  final $Res Function(MatchSummaryData) _then;

  /// Create a copy of MatchSummaryData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enterpriseName = null,
    Object? matchStatus = null,
    Object? matchStep = null,
    Object? reportCreateTime = null,
    Object? productNum = null,
    Object? totalQuota = null,
    Object? enterpriseId = null,
    Object? creditCode = null,
    Object? question = null,
    Object? memberId = null,
  }) {
    return _then(_self.copyWith(
      enterpriseName: null == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String,
      matchStatus: null == matchStatus
          ? _self.matchStatus
          : matchStatus // ignore: cast_nullable_to_non_nullable
              as int,
      matchStep: null == matchStep
          ? _self.matchStep
          : matchStep // ignore: cast_nullable_to_non_nullable
              as int,
      reportCreateTime: null == reportCreateTime
          ? _self.reportCreateTime
          : reportCreateTime // ignore: cast_nullable_to_non_nullable
              as String,
      productNum: null == productNum
          ? _self.productNum
          : productNum // ignore: cast_nullable_to_non_nullable
              as int,
      totalQuota: null == totalQuota
          ? _self.totalQuota
          : totalQuota // ignore: cast_nullable_to_non_nullable
              as String,
      enterpriseId: null == enterpriseId
          ? _self.enterpriseId
          : enterpriseId // ignore: cast_nullable_to_non_nullable
              as String,
      creditCode: null == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String,
      question: null == question
          ? _self.question
          : question // ignore: cast_nullable_to_non_nullable
              as bool,
      memberId: null == memberId
          ? _self.memberId
          : memberId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _MatchSummaryData implements MatchSummaryData {
  const _MatchSummaryData(
      {required this.enterpriseName,
      required this.matchStatus,
      required this.matchStep,
      required this.reportCreateTime,
      required this.productNum,
      required this.totalQuota,
      this.enterpriseId = '',
      required this.creditCode,
      required this.question,
      required this.memberId});
  factory _MatchSummaryData.fromJson(Map<String, dynamic> json) =>
      _$MatchSummaryDataFromJson(json);

  @override
  final String enterpriseName;
  @override
  final int matchStatus;
  @override
  final int matchStep;
  @override
  final String reportCreateTime;
  @override
  final int productNum;
  @override
  final String totalQuota;
  @override
  @JsonKey()
  final String enterpriseId;
  @override
  final String creditCode;
  @override
  final bool question;
  @override
  final String memberId;

  /// Create a copy of MatchSummaryData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MatchSummaryDataCopyWith<_MatchSummaryData> get copyWith =>
      __$MatchSummaryDataCopyWithImpl<_MatchSummaryData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MatchSummaryDataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MatchSummaryData &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.matchStatus, matchStatus) ||
                other.matchStatus == matchStatus) &&
            (identical(other.matchStep, matchStep) ||
                other.matchStep == matchStep) &&
            (identical(other.reportCreateTime, reportCreateTime) ||
                other.reportCreateTime == reportCreateTime) &&
            (identical(other.productNum, productNum) ||
                other.productNum == productNum) &&
            (identical(other.totalQuota, totalQuota) ||
                other.totalQuota == totalQuota) &&
            (identical(other.enterpriseId, enterpriseId) ||
                other.enterpriseId == enterpriseId) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.question, question) ||
                other.question == question) &&
            (identical(other.memberId, memberId) ||
                other.memberId == memberId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      enterpriseName,
      matchStatus,
      matchStep,
      reportCreateTime,
      productNum,
      totalQuota,
      enterpriseId,
      creditCode,
      question,
      memberId);

  @override
  String toString() {
    return 'MatchSummaryData(enterpriseName: $enterpriseName, matchStatus: $matchStatus, matchStep: $matchStep, reportCreateTime: $reportCreateTime, productNum: $productNum, totalQuota: $totalQuota, enterpriseId: $enterpriseId, creditCode: $creditCode, question: $question, memberId: $memberId)';
  }
}

/// @nodoc
abstract mixin class _$MatchSummaryDataCopyWith<$Res>
    implements $MatchSummaryDataCopyWith<$Res> {
  factory _$MatchSummaryDataCopyWith(
          _MatchSummaryData value, $Res Function(_MatchSummaryData) _then) =
      __$MatchSummaryDataCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String enterpriseName,
      int matchStatus,
      int matchStep,
      String reportCreateTime,
      int productNum,
      String totalQuota,
      String enterpriseId,
      String creditCode,
      bool question,
      String memberId});
}

/// @nodoc
class __$MatchSummaryDataCopyWithImpl<$Res>
    implements _$MatchSummaryDataCopyWith<$Res> {
  __$MatchSummaryDataCopyWithImpl(this._self, this._then);

  final _MatchSummaryData _self;
  final $Res Function(_MatchSummaryData) _then;

  /// Create a copy of MatchSummaryData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? enterpriseName = null,
    Object? matchStatus = null,
    Object? matchStep = null,
    Object? reportCreateTime = null,
    Object? productNum = null,
    Object? totalQuota = null,
    Object? enterpriseId = null,
    Object? creditCode = null,
    Object? question = null,
    Object? memberId = null,
  }) {
    return _then(_MatchSummaryData(
      enterpriseName: null == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String,
      matchStatus: null == matchStatus
          ? _self.matchStatus
          : matchStatus // ignore: cast_nullable_to_non_nullable
              as int,
      matchStep: null == matchStep
          ? _self.matchStep
          : matchStep // ignore: cast_nullable_to_non_nullable
              as int,
      reportCreateTime: null == reportCreateTime
          ? _self.reportCreateTime
          : reportCreateTime // ignore: cast_nullable_to_non_nullable
              as String,
      productNum: null == productNum
          ? _self.productNum
          : productNum // ignore: cast_nullable_to_non_nullable
              as int,
      totalQuota: null == totalQuota
          ? _self.totalQuota
          : totalQuota // ignore: cast_nullable_to_non_nullable
              as String,
      enterpriseId: null == enterpriseId
          ? _self.enterpriseId
          : enterpriseId // ignore: cast_nullable_to_non_nullable
              as String,
      creditCode: null == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String,
      question: null == question
          ? _self.question
          : question // ignore: cast_nullable_to_non_nullable
              as bool,
      memberId: null == memberId
          ? _self.memberId
          : memberId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$MatchDetailState {
  int get selectedTabIndex;
  MatchSummaryData? get summaryData;
  bool get isLoading;
  String? get errorMessage;

  /// Create a copy of MatchDetailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MatchDetailStateCopyWith<MatchDetailState> get copyWith =>
      _$MatchDetailStateCopyWithImpl<MatchDetailState>(
          this as MatchDetailState, _$identity);

  /// Serializes this MatchDetailState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MatchDetailState &&
            (identical(other.selectedTabIndex, selectedTabIndex) ||
                other.selectedTabIndex == selectedTabIndex) &&
            (identical(other.summaryData, summaryData) ||
                other.summaryData == summaryData) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, selectedTabIndex, summaryData, isLoading, errorMessage);

  @override
  String toString() {
    return 'MatchDetailState(selectedTabIndex: $selectedTabIndex, summaryData: $summaryData, isLoading: $isLoading, errorMessage: $errorMessage)';
  }
}

/// @nodoc
abstract mixin class $MatchDetailStateCopyWith<$Res> {
  factory $MatchDetailStateCopyWith(
          MatchDetailState value, $Res Function(MatchDetailState) _then) =
      _$MatchDetailStateCopyWithImpl;
  @useResult
  $Res call(
      {int selectedTabIndex,
      MatchSummaryData? summaryData,
      bool isLoading,
      String? errorMessage});

  $MatchSummaryDataCopyWith<$Res>? get summaryData;
}

/// @nodoc
class _$MatchDetailStateCopyWithImpl<$Res>
    implements $MatchDetailStateCopyWith<$Res> {
  _$MatchDetailStateCopyWithImpl(this._self, this._then);

  final MatchDetailState _self;
  final $Res Function(MatchDetailState) _then;

  /// Create a copy of MatchDetailState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedTabIndex = null,
    Object? summaryData = freezed,
    Object? isLoading = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_self.copyWith(
      selectedTabIndex: null == selectedTabIndex
          ? _self.selectedTabIndex
          : selectedTabIndex // ignore: cast_nullable_to_non_nullable
              as int,
      summaryData: freezed == summaryData
          ? _self.summaryData
          : summaryData // ignore: cast_nullable_to_non_nullable
              as MatchSummaryData?,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of MatchDetailState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MatchSummaryDataCopyWith<$Res>? get summaryData {
    if (_self.summaryData == null) {
      return null;
    }

    return $MatchSummaryDataCopyWith<$Res>(_self.summaryData!, (value) {
      return _then(_self.copyWith(summaryData: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _MatchDetailState implements MatchDetailState {
  const _MatchDetailState(
      {this.selectedTabIndex = 0,
      this.summaryData,
      this.isLoading = false,
      this.errorMessage});
  factory _MatchDetailState.fromJson(Map<String, dynamic> json) =>
      _$MatchDetailStateFromJson(json);

  @override
  @JsonKey()
  final int selectedTabIndex;
  @override
  final MatchSummaryData? summaryData;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? errorMessage;

  /// Create a copy of MatchDetailState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MatchDetailStateCopyWith<_MatchDetailState> get copyWith =>
      __$MatchDetailStateCopyWithImpl<_MatchDetailState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MatchDetailStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MatchDetailState &&
            (identical(other.selectedTabIndex, selectedTabIndex) ||
                other.selectedTabIndex == selectedTabIndex) &&
            (identical(other.summaryData, summaryData) ||
                other.summaryData == summaryData) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, selectedTabIndex, summaryData, isLoading, errorMessage);

  @override
  String toString() {
    return 'MatchDetailState(selectedTabIndex: $selectedTabIndex, summaryData: $summaryData, isLoading: $isLoading, errorMessage: $errorMessage)';
  }
}

/// @nodoc
abstract mixin class _$MatchDetailStateCopyWith<$Res>
    implements $MatchDetailStateCopyWith<$Res> {
  factory _$MatchDetailStateCopyWith(
          _MatchDetailState value, $Res Function(_MatchDetailState) _then) =
      __$MatchDetailStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int selectedTabIndex,
      MatchSummaryData? summaryData,
      bool isLoading,
      String? errorMessage});

  @override
  $MatchSummaryDataCopyWith<$Res>? get summaryData;
}

/// @nodoc
class __$MatchDetailStateCopyWithImpl<$Res>
    implements _$MatchDetailStateCopyWith<$Res> {
  __$MatchDetailStateCopyWithImpl(this._self, this._then);

  final _MatchDetailState _self;
  final $Res Function(_MatchDetailState) _then;

  /// Create a copy of MatchDetailState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? selectedTabIndex = null,
    Object? summaryData = freezed,
    Object? isLoading = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_MatchDetailState(
      selectedTabIndex: null == selectedTabIndex
          ? _self.selectedTabIndex
          : selectedTabIndex // ignore: cast_nullable_to_non_nullable
              as int,
      summaryData: freezed == summaryData
          ? _self.summaryData
          : summaryData // ignore: cast_nullable_to_non_nullable
              as MatchSummaryData?,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of MatchDetailState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MatchSummaryDataCopyWith<$Res>? get summaryData {
    if (_self.summaryData == null) {
      return null;
    }

    return $MatchSummaryDataCopyWith<$Res>(_self.summaryData!, (value) {
      return _then(_self.copyWith(summaryData: value));
    });
  }
}

// dart format on
