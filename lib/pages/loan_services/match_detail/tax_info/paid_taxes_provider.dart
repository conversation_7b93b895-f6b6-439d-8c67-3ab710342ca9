import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';

import 'api/paid_tax_api.dart';

part 'paid_taxes_provider.g.dart';


enum TaxType {
  normalTax(1),    // 1: 正税
  additionalTax(2); // 2: 附加税

  final int value;  

  const TaxType(this.value); 
}


class PaidTaxesInfoState {
  var taxType = TaxType.normalTax;

 List<Datum>? datumsInfos;

  PaidTaxesInfoState copyWith(
      {TaxType? taxType, List<Datum>? datumsInfos}) {
    return PaidTaxesInfoState()
    //  ..pageStatus = pageStatus
     ..taxType = taxType ?? this.taxType
    ..datumsInfos = datumsInfos ?? this.datumsInfos;
  }

/// 近三年纳税信息数据
  bool get hasPaidTaxInfo  => datumsInfos != null && datumsInfos!.isNotEmpty;

  String taxTipContent() {
    switch (this.taxType) {
      case TaxType.normalTax:
        return '正税主要包含增值税、企业所得税、车辆税的税费之和；';
      case TaxType.additionalTax:
        return '附加税主要包含城市维护建设税、教育费附加、地方教育费附加,以及消费税、资源税、房产税、城镇土地使用税、车船税、印花税等';
    }
  }

/// 表头
List<String> get padiTaxesListTableHeadData {
    List<String> res = ['月份'];
    if (datumsInfos == null) {
      return res;
    }
    for (var value in datumsInfos!) {
      res.add('${value.year ?? '_'}年');
    }
    return res;
  }

// 表内容
  List<List<String>> get padiTaxesListTableData {
    List<List<String>> res = [];
    if (datumsInfos == null) {
      return res;
    }
    int cols = datumsInfos!.length;
    const titles = [
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
      '合计'
    ];
    int rows = titles.length;
    for (var i = 0; i < rows; i++) {
      List<String> inner = [];
      for (var j = 0; j < cols + 1; j++) {
        if (j == 0) {
          inner.add(titles[i]);
        } else {
          final itemData = datumsInfos![j - 1].monthData;
          if (i < titles.length - 1) {
            inner.add(formateLargeStringNumber(itemData?[i].value));
          } else {
            inner.add(formateLargeStringNumber(datumsInfos![j - 1].total));
          }
        }
      }
      res.add(inner);
    }
    return res;
  }
}

@riverpod
 class PaidTaxInfo extends _$PaidTaxInfo {
  @override
  PaidTaxesInfoState build() {
    return PaidTaxesInfoState();
  }

  Future<void> getPaidTaxesDetail(String macthId, TaxType taxType) async {

      try {
      final response = await PaidTaxApi.paidTaxesDatDetail(macthId, taxType.value);
      if (response.data != null) {
        state = state.copyWith(datumsInfos: response.data, taxType: taxType);
      } else {
        state = state.copyWith(taxType: taxType);
      }
    } catch (e) {
      state = state.copyWith(taxType: taxType);
      debugPrint('${e}');
    }
  }
}