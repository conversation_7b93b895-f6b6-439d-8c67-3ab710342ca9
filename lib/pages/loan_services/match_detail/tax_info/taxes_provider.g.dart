// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'taxes_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$taxInfoHash() => r'7a85592c42106220c9b925cfd8dba2b8ad233a0b';

/// See also [TaxInfo].
@ProviderFor(TaxInfo)
final taxInfoProvider =
    AutoDisposeNotifierProvider<TaxInfo, TaxInfoInfoState>.internal(
  TaxInfo.new,
  name: r'taxInfoProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$taxInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TaxInfo = AutoDisposeNotifier<TaxInfoInfoState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
