

import 'package:zrreport/common/index.dart';

class PaidTaxApi {
  /// 正税/附加税信息
  static Future<BaseResponse<List<Datum>>> paidTaxesDatDetail(
      String macthId, int taxType) async {
    final response = await SXHttpService.to.get(
        '/cmsMatchEnterprise/getEnterprisePaidTaxesDataList',
        params: {'macthId': macthId, 'taxType': taxType});
    return BaseResponse.fromJson(
      response.data,
      (data) {
        if (data is List) {
          return data
              .map((item) => Datum.fromJson(item as Map<String, dynamic>))
              .toList();
        } else {
          throw Exception('Expected List but got ${data.runtimeType}');
        }
      },
    );
  }


    /// 纳税信息
  static Future<BaseResponse<TaxesInfo>> taxesDetail(String macthId) async {
    final response = await SXHttpService.to.get(
        '/cmsMatchEnterprise/getEnterprisePaidTaxesAmount/${macthId}',
        );
    return BaseResponse.fromJson(
      response.data,
      (data) => TaxesInfo.fromJson(data as Map<String, dynamic>),
    );
  }

}
