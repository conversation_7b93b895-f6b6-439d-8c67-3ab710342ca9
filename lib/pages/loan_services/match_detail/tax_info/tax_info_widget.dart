import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:zrreport/common/index.dart';

import 'paid_taxes_provider.dart';
import 'taxes_provider.dart';

class TaxInfoWidget extends BasePage {
  final String macthId;
  const TaxInfoWidget(
      {super.key, required this.macthId});

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ProviderScope.containerOf(context)
          .read(taxInfoProvider.notifier)
          .getTaxesDetail(macthId);
    });

    return Padding(
      padding: EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: Column(
          children: [
            Consumer(
              builder: (context, ref, child) {
                final response = ref.watch(taxInfoProvider);
                switch (response.pageStatus) {
                  case PageStatus.initial:
                  case PageStatus.loading:
                    return Center(
                      child: LoadingWidget(),
                    );
                  case PageStatus.success:
                    return _buildContentView();
                  case PageStatus.empty:
                    return Container(
                      child: Text(''),
                    );
                  case PageStatus.error:
                    return ErrorStatusWidget(onAttempt: () {
                      ref
                          .read(taxInfoProvider.notifier)
                          .getTaxesDetail(macthId);
                    });
                }
              },
            )
          ],
        ),
      ),
    );
  }

  Widget _buildContentView() {
    return Consumer(builder: (context, ref, child) {
      final response = ref.read(taxInfoProvider);
      return Column(
        children: [
          // header list
          ...response.taxInfoHeadList.asMap().entries.map((e) {
            return AdaptiveTextRow(
                label: e.value.title,
                value: e.value.value,
                isOdd: !e.key.isOdd);
          }),

          SizedBox(height: 24),

          CustomDataGrid(
              title: '纳税数据分析',
              header: response.enterpriseMonthPaidListTableHeadData,
              tableData: response.enterpriseMonthPaidListTableData,
              horizalScrollEnable: true,
              frozenColumnsCount: 1,
              firstColumnWidth: 140),

          CustomDataGrid(
            title: '滞纳金情况',
            header: response.taxLateFeesListTableHeadData,
            tableData: response.taxLateFeesListTableData(),
          ),

          /// 近两年纳税信用评级表
          CustomDataGrid(
            title: '近两年纳税信用评级',
            header: response.creditEvaluationListTableHeadData,
            tableData: response.creditEvaluationListTableData(),
          ),

          /// 财务情况
          CustomDataGrid(
              title: '财务情况(元)',
              header: response.spiderFinanceInfosYearListTableHeadData,
              tableData: response.spiderFinanceInfosYearListTableData(),
              horizalScrollEnable: true,
              frozenColumnsCount: 1,
              firstColumnWidth: 140),

          /// 近三年纳税信息
          _buildLastThreeTaxesTable(),

          /// 欠税信息
          CustomDataGrid(
            title: '欠税信息',
            header: ['欠税税种', '欠税金额', '欠税所属期'],
            tableData: [],
          ),
        ],
      );
    });
  }

  /// 近三年纳税信息
  Widget _buildLastThreeTaxesTable() {
    return Consumer(builder: (context, ref, child) {
      final response = ref.watch(paidTaxInfoProvider);
      if (!response.hasPaidTaxInfo) {
        ref.read(paidTaxInfoProvider.notifier).getPaidTaxesDetail(
              macthId,
              response.taxType,
            );
      }
      return Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 16,
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(child: SectionHeader(title: '近三年纳税信息')),
                  switchWidget(),
                  SizedBox(width: 15)
                ],
              ),
              SizedBox(height: 12),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '温馨提示:',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xff999999),
                    ),
                    textAlign: TextAlign.left,
                  ),
                  Expanded(
                    child: Text(
                      response.taxTipContent(),
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xff999999),
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Color(0xffE5E7EB),
                    width: 1,
                  ),
                ),
                clipBehavior: Clip.antiAlias,
                child: SfDataGrid(
                  source: DynamicDataSource(
                    data: response.padiTaxesListTableData,
                    columns: response.padiTaxesListTableHeadData,
                    cellBuilder: (context, cell, index) {
                      // 在这里处理table body
                      return Container(
                        alignment: Alignment.center,
                        padding: EdgeInsets.symmetric(
                          vertical: 8.0,
                          horizontal: index == 0 ? 8.0 : 0,
                        ),
                        color: Colors.white,
                        child: Text(
                          cell.value ?? '',
                        ),
                      );
                    },
                  ),
                  // frozenColumnsCount: 1, // 第一列固定
                  gridLinesVisibility: GridLinesVisibility.both,
                  headerGridLinesVisibility: GridLinesVisibility.both,
                  showHorizontalScrollbar: false,
                  shrinkWrapRows: true, // 自动撑开高度
                  columnWidthMode: ColumnWidthMode.fill,
                  // shrinkWrapColumns: true,
                  verticalScrollPhysics:
                      NeverScrollableScrollPhysics(), // 关闭滚动，让外层控制
                  columns: response.padiTaxesListTableHeadData
                      .asMap()
                      .entries
                      .map((entry) {
                    // 在这里处理table head
                    final index = entry.key;
                    final e = entry.value;
                    return GridColumn(
                      columnName: e,
                      // width: index == 0
                      //     ? 60
                      //     : (MediaQuery.of(context).size.width - 60 - 30) /
                      //         3, // 第一列宽度不同
                      label: Container(
                        color: Color(0xffF1F5F9),
                        padding: EdgeInsets.symmetric(
                            vertical: 16, horizontal: index == 0 ? 8 : 0),
                        alignment: Alignment.center,
                        child: Text(
                          e,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              )
            ],
          ));
    });
  }

  Widget switchWidget() {
    return Consumer(
      builder: (context, ref, child) {
        final response = ref.watch(paidTaxInfoProvider);
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: Color(0xffd3e3fe),
          ),
          height: 24,
          width: 110,
          child: Row(
            children: [
              Expanded(
                child: _buildItemButton(
                  title: '正税',
                  selected: response.taxType == TaxType.normalTax,
                  onPressed: () {
                    if (response.taxType != TaxType.normalTax) {
                      ref
                          .read(paidTaxInfoProvider.notifier)
                          .getPaidTaxesDetail(macthId, TaxType.normalTax);
                    }
                  },
                ),
              ),
              Expanded(
                child: _buildItemButton(
                  title: '附加税',
                  selected: response.taxType == TaxType.additionalTax,
                  onPressed: () {
                    if (response.taxType != TaxType.additionalTax) {
                      ref
                          .read(paidTaxInfoProvider.notifier)
                          .getPaidTaxesDetail(macthId, TaxType.additionalTax);
                    }
                  },
                ),
              )
            ],
          ),
        );
      },
    );
  }

  Widget _buildItemButton(
      {required String title,
      required bool selected,
      required VoidCallback? onPressed}) {
    return GestureDetector(
        onTap: onPressed,
        child: Container(
            height: 24,
            decoration: BoxDecoration(
              color: selected ? Color(0xff488afd) : Color(0xffd3e3fe),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Text(
                title,
                style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: selected ? Colors.white : Colors.black),
                textAlign: TextAlign.center,
              ),
            )));
  }
}
