// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'match_result_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MatchResultState _$MatchResultStateFromJson(Map<String, dynamic> json) =>
    _MatchResultState(
      selectedTabIndex: (json['selectedTabIndex'] as num?)?.toInt() ?? 0,
      recommendedProducts: (json['recommendedProducts'] as List<dynamic>?)
              ?.map((e) => BankProduct.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      notMatchedProducts: (json['notMatchedProducts'] as List<dynamic>?)
              ?.map((e) => BankProduct.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      isLoading: json['isLoading'] as bool? ?? false,
      hasError: json['hasError'] as bool? ?? false,
      errorMessage: json['errorMessage'] as String?,
    );

Map<String, dynamic> _$MatchResultStateToJson(_MatchResultState instance) =>
    <String, dynamic>{
      'selectedTabIndex': instance.selectedTabIndex,
      'recommendedProducts': instance.recommendedProducts,
      'notMatchedProducts': instance.notMatchedProducts,
      'isLoading': instance.isLoading,
      'hasError': instance.hasError,
      'errorMessage': instance.errorMessage,
    };

_BankProduct _$BankProductFromJson(Map<String, dynamic> json) => _BankProduct(
      id: json['id'] as String,
      bankId: json['bankId'] as String,
      bankName: json['bankName'] as String,
      bankCode: json['bankCode'] as String,
      productId: json['productId'] as String,
      productPic: json['productPic'] as String?,
      advantageData: json['advantageData'] as String?,
      repaymentType: json['repaymentType'] as String?,
      loanTerm: json['loanTerm'] as String,
      loanType: json['loanType'] as String?,
      productName: json['productName'] as String,
      productMinQuota: (json['productMinQuota'] as num?)?.toInt() ?? 0,
      productMaxQuota: (json['productMaxQuota'] as num?)?.toInt() ?? 0,
      minAnnualRate: json['minAnnualRate'] as String?,
      maxAnnualRate: json['maxAnnualRate'] as String?,
      publishStatus: (json['publishStatus'] as num?)?.toInt() ?? 1,
      matchId: json['matchId'] as String?,
      matchType: (json['matchType'] as num?)?.toInt() ?? 1,
      recommendType: (json['recommendType'] as num?)?.toInt() ?? 1,
      reason: json['reason'] as String?,
      updateBy: json['updateBy'] as String?,
      quota: json['quota'] as String?,
      sort: (json['sort'] as num?)?.toInt(),
      matchStep: (json['matchStep'] as num?)?.toInt() ?? 1,
      note: json['note'] as String?,
      createTime: json['createTime'] as String?,
      updateTime: json['updateTime'] as String?,
      collect: json['collect'] as bool? ?? false,
      enableTop: (json['enableTop'] as num?)?.toInt() ?? 0,
      rankSort: (json['rankSort'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$BankProductToJson(_BankProduct instance) =>
    <String, dynamic>{
      'id': instance.id,
      'bankId': instance.bankId,
      'bankName': instance.bankName,
      'bankCode': instance.bankCode,
      'productId': instance.productId,
      'productPic': instance.productPic,
      'advantageData': instance.advantageData,
      'repaymentType': instance.repaymentType,
      'loanTerm': instance.loanTerm,
      'loanType': instance.loanType,
      'productName': instance.productName,
      'productMinQuota': instance.productMinQuota,
      'productMaxQuota': instance.productMaxQuota,
      'minAnnualRate': instance.minAnnualRate,
      'maxAnnualRate': instance.maxAnnualRate,
      'publishStatus': instance.publishStatus,
      'matchId': instance.matchId,
      'matchType': instance.matchType,
      'recommendType': instance.recommendType,
      'reason': instance.reason,
      'updateBy': instance.updateBy,
      'quota': instance.quota,
      'sort': instance.sort,
      'matchStep': instance.matchStep,
      'note': instance.note,
      'createTime': instance.createTime,
      'updateTime': instance.updateTime,
      'collect': instance.collect,
      'enableTop': instance.enableTop,
      'rankSort': instance.rankSort,
    };

_MatchResultResponse _$MatchResultResponseFromJson(Map<String, dynamic> json) =>
    _MatchResultResponse(
      products: (json['products'] as List<dynamic>)
          .map((e) => BankProduct.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$MatchResultResponseToJson(
        _MatchResultResponse instance) =>
    <String, dynamic>{
      'products': instance.products,
    };
