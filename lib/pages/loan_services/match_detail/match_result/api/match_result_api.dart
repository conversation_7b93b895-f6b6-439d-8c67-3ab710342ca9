import 'package:zrreport/common/index.dart';
import '../models/match_result_models.dart';

/// 匹配结果API服务
class MatchResultApi {
  /// 获取匹配产品列表
  /// [id] 匹配ID
  /// [recommendType] 推荐类型：1=推荐申请产品, 2=暂不匹配产品
  static Future<BaseResponse<List<BankProduct>>> getMatchProducts({
    required String id,
    required int recommendType,
  }) async {
    final response = await SXHttpService.to.get(
      '/match/summary/listProduct',
      params: {
        'id': id,
        'type': 1,
        'matchStep': 1,
        'recommendType': recommendType,
      },
    );

    return BaseResponse.fromJsonList(
      response.data,
      (data) => BankProduct.fromJson(data as Map<String, dynamic>),
    );
  }
}
