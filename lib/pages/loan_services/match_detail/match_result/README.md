# 匹配结果组件 (MatchResultWidget)

## 概述

MatchResultWidget 是一个专门用于展示银行产品匹配结果的组件，包含两个标签页：
- 推荐申请产品
- 暂不匹配产品

## 功能特性

- ✅ 双标签页切换（推荐申请产品 / 暂不匹配产品）
- ✅ 产品卡片展示（银行logo、产品名称、额度、期限、利率等）
- ✅ 产品标签（税贷、热门等）
- ✅ 加载状态、错误状态、空状态处理
- ✅ 响应式设计，适配不同屏幕尺寸

## 文件结构

```
lib/pages/product/match_detail/match_result/
├── match_result_widget.dart          # 主组件
├── models/
│   ├── match_result_models.dart       # 数据模型
│   ├── match_result_models.freezed.dart  # 生成的freezed文件
│   └── match_result_models.g.dart     # 生成的json序列化文件
├── provider/
│   ├── match_result_provider.dart     # 状态管理
│   └── match_result_provider.g.dart   # 生成的riverpod文件
├── api/
│   └── match_result_api.dart          # API服务
├── index.dart                         # 导出文件
└── README.md                          # 说明文档
```

## 数据模型

### MatchResultState
页面状态模型：
- `selectedTabIndex`: 当前选中的标签页索引
- `recommendedProducts`: 推荐申请产品列表
- `notMatchedProducts`: 暂不匹配产品列表
- `isLoading`: 加载状态
- `hasError`: 错误状态
- `errorMessage`: 错误信息

### BankProduct
银行产品模型（根据真实API数据结构定义）：
- `id`: 产品ID
- `bankId`: 银行ID
- `bankName`: 银行名称
- `bankCode`: 银行代码
- `productId`: 产品ID
- `productName`: 产品名称
- `productMaxQuota`: 最大额度
- `minAnnualRate`: 最小年利率
- `maxAnnualRate`: 最大年利率
- `loanTerm`: 贷款期限
- `recommendType`: 推荐类型 (1=推荐申请, 2=暂不匹配)
- `enableTop`: 是否置顶（热门产品）
- `advantageData`: 优势数据（JSON字符串）
- `reason`: 不匹配原因
- `note`: 备注

### BankProduct扩展方法
- `formattedMaxAmount`: 格式化的最高额度
- `formattedInterestRate`: 格式化的年利率
- `isRecommended`: 是否为推荐产品
- `isTaxProduct`: 是否为税贷产品（根据产品名称判断）
- `isHotProduct`: 是否为热门产品（根据置顶状态判断）
- `specialNote`: 获取不匹配原因或特殊说明
- `advantages`: 获取优势列表

## API接口

### 获取匹配产品列表
- **接口**: `/portal/match/summary/listProduct`
- **方法**: GET
- **参数**:
  - `id`: 匹配ID
  - `type`: 固定值 1
  - `matchStep`: 固定值 1
  - `recommendType`: 推荐类型
    - `1`: 推荐申请产品
    - `2`: 暂不匹配产品
- **返回**: List<BankProduct>

### API调用策略
组件会并行调用两次API：
1. 调用 `recommendType=1` 获取推荐申请产品
2. 调用 `recommendType=2` 获取暂不匹配产品

这样可以确保两个标签页的数据完全独立，提高数据准确性。

## 使用方法

```dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'lib/pages/product/match_detail/match_result/index.dart';

// 在父组件中使用
class ParentWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // 其他内容...
          
          // 匹配结果组件
          Expanded(
            child: MatchResultWidget(
              matchId: 'your_match_id',
              matchType: 1,
              createTime: '2024-01-01 12:00:00',
            ),
          ),
        ],
      ),
    );
  }
}
```

## 状态管理

使用Riverpod进行状态管理：

```dart
// 获取状态
final state = ref.watch(matchResultNotifierProvider);

// 获取notifier
final notifier = ref.read(matchResultNotifierProvider.notifier);

// 初始化数据
notifier.initialize(matchId: 'match_id');

// 切换标签页
notifier.selectTab(index);

// 重新加载
notifier.reload(matchId: 'match_id');
```

## 样式定制

组件使用了项目中的AppColors颜色系统：
- `AppColors.primary`: 主色调（标签页选中状态）
- `AppColors.orange`: 橙色（税贷标签）
- `AppColors.textColor1/3/6`: 文本颜色
- 银行logo颜色根据银行名称自动匹配

### 尺寸系统
- 使用标准Flutter尺寸单位（double类型）
- 移除了ducafe_ui_core的响应式尺寸扩展（.w、.h、.r、.sp）
- 所有尺寸都是固定值，适合大多数设备

## 错误处理

- 网络请求失败时显示ErrorStatusWidget
- 数据为空时显示EmptyWidget
- 加载中显示LoadingWidget

## 注意事项

1. 需要运行 `dart run build_runner build` 生成freezed和riverpod相关文件
2. 确保项目中已正确配置网络请求服务
3. 组件依赖项目的通用组件（ErrorStatusWidget、EmptyWidget、LoadingWidget）
4. **已移除ducafe_ui_core依赖**，使用标准Flutter尺寸单位

## 产品卡片特性

- 银行logo自动根据银行名称生成颜色和简称
- 支持税贷和热门产品标签
- 显示最高额度、贷款期限、年利率等关键信息
- 支持特殊说明文本展示
- 卡片阴影效果，提升视觉层次

## 扩展功能

如需添加更多功能，可以考虑：
- 产品详情页跳转
- 产品收藏功能
- 产品分享功能
- 筛选和排序功能
- 产品对比功能
