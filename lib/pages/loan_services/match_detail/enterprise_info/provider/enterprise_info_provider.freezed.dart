// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'enterprise_info_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$EnterpriseInfoState {
  /// 企业信息数据
  EnterpriseInfo? get enterpriseInfo;

  /// 是否正在加载
  bool get isLoading;

  /// 错误信息
  String? get error;

  /// Create a copy of EnterpriseInfoState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $EnterpriseInfoStateCopyWith<EnterpriseInfoState> get copyWith =>
      _$EnterpriseInfoStateCopyWithImpl<EnterpriseInfoState>(
          this as EnterpriseInfoState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is EnterpriseInfoState &&
            (identical(other.enterpriseInfo, enterpriseInfo) ||
                other.enterpriseInfo == enterpriseInfo) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, enterpriseInfo, isLoading, error);

  @override
  String toString() {
    return 'EnterpriseInfoState(enterpriseInfo: $enterpriseInfo, isLoading: $isLoading, error: $error)';
  }
}

/// @nodoc
abstract mixin class $EnterpriseInfoStateCopyWith<$Res> {
  factory $EnterpriseInfoStateCopyWith(
          EnterpriseInfoState value, $Res Function(EnterpriseInfoState) _then) =
      _$EnterpriseInfoStateCopyWithImpl;
  @useResult
  $Res call({EnterpriseInfo? enterpriseInfo, bool isLoading, String? error});

  $EnterpriseInfoCopyWith<$Res>? get enterpriseInfo;
}

/// @nodoc
class _$EnterpriseInfoStateCopyWithImpl<$Res>
    implements $EnterpriseInfoStateCopyWith<$Res> {
  _$EnterpriseInfoStateCopyWithImpl(this._self, this._then);

  final EnterpriseInfoState _self;
  final $Res Function(EnterpriseInfoState) _then;

  /// Create a copy of EnterpriseInfoState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enterpriseInfo = freezed,
    Object? isLoading = null,
    Object? error = freezed,
  }) {
    return _then(_self.copyWith(
      enterpriseInfo: freezed == enterpriseInfo
          ? _self.enterpriseInfo
          : enterpriseInfo // ignore: cast_nullable_to_non_nullable
              as EnterpriseInfo?,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of EnterpriseInfoState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EnterpriseInfoCopyWith<$Res>? get enterpriseInfo {
    if (_self.enterpriseInfo == null) {
      return null;
    }

    return $EnterpriseInfoCopyWith<$Res>(_self.enterpriseInfo!, (value) {
      return _then(_self.copyWith(enterpriseInfo: value));
    });
  }
}

/// @nodoc

class _EnterpriseInfoState implements EnterpriseInfoState {
  const _EnterpriseInfoState(
      {this.enterpriseInfo, this.isLoading = false, this.error});

  /// 企业信息数据
  @override
  final EnterpriseInfo? enterpriseInfo;

  /// 是否正在加载
  @override
  @JsonKey()
  final bool isLoading;

  /// 错误信息
  @override
  final String? error;

  /// Create a copy of EnterpriseInfoState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$EnterpriseInfoStateCopyWith<_EnterpriseInfoState> get copyWith =>
      __$EnterpriseInfoStateCopyWithImpl<_EnterpriseInfoState>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _EnterpriseInfoState &&
            (identical(other.enterpriseInfo, enterpriseInfo) ||
                other.enterpriseInfo == enterpriseInfo) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, enterpriseInfo, isLoading, error);

  @override
  String toString() {
    return 'EnterpriseInfoState(enterpriseInfo: $enterpriseInfo, isLoading: $isLoading, error: $error)';
  }
}

/// @nodoc
abstract mixin class _$EnterpriseInfoStateCopyWith<$Res>
    implements $EnterpriseInfoStateCopyWith<$Res> {
  factory _$EnterpriseInfoStateCopyWith(_EnterpriseInfoState value,
          $Res Function(_EnterpriseInfoState) _then) =
      __$EnterpriseInfoStateCopyWithImpl;
  @override
  @useResult
  $Res call({EnterpriseInfo? enterpriseInfo, bool isLoading, String? error});

  @override
  $EnterpriseInfoCopyWith<$Res>? get enterpriseInfo;
}

/// @nodoc
class __$EnterpriseInfoStateCopyWithImpl<$Res>
    implements _$EnterpriseInfoStateCopyWith<$Res> {
  __$EnterpriseInfoStateCopyWithImpl(this._self, this._then);

  final _EnterpriseInfoState _self;
  final $Res Function(_EnterpriseInfoState) _then;

  /// Create a copy of EnterpriseInfoState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? enterpriseInfo = freezed,
    Object? isLoading = null,
    Object? error = freezed,
  }) {
    return _then(_EnterpriseInfoState(
      enterpriseInfo: freezed == enterpriseInfo
          ? _self.enterpriseInfo
          : enterpriseInfo // ignore: cast_nullable_to_non_nullable
              as EnterpriseInfo?,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of EnterpriseInfoState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EnterpriseInfoCopyWith<$Res>? get enterpriseInfo {
    if (_self.enterpriseInfo == null) {
      return null;
    }

    return $EnterpriseInfoCopyWith<$Res>(_self.enterpriseInfo!, (value) {
      return _then(_self.copyWith(enterpriseInfo: value));
    });
  }
}

// dart format on
