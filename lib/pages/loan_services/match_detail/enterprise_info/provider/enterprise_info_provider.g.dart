// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'enterprise_info_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$enterpriseInfoNotifierHash() =>
    r'bb0f3d1059e5ee4dbd398e07ffc2e006ea31c0aa';

/// 企业信息Provider
///
/// Copied from [EnterpriseInfoNotifier].
@ProviderFor(EnterpriseInfoNotifier)
final enterpriseInfoNotifierProvider = AutoDisposeNotifierProvider<
    EnterpriseInfoNotifier, EnterpriseInfoState>.internal(
  EnterpriseInfoNotifier.new,
  name: r'enterpriseInfoNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$enterpriseInfoNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$EnterpriseInfoNotifier = AutoDisposeNotifier<EnterpriseInfoState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
