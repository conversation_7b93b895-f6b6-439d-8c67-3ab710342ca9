import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../api/enterprise_info_api.dart';
import '../model/enterprise_info.dart';

part 'enterprise_info_provider.g.dart';
part 'enterprise_info_provider.freezed.dart';

/// 企业信息状态
@freezed
abstract class EnterpriseInfoState with _$EnterpriseInfoState {
  const factory EnterpriseInfoState({
    /// 企业信息数据
    EnterpriseInfo? enterpriseInfo,
    /// 是否正在加载
    @Default(false) bool isLoading,
    /// 错误信息
    String? error,
  }) = _EnterpriseInfoState;
}

/// 企业信息Provider
@riverpod
class EnterpriseInfoNotifier extends _$EnterpriseInfoNotifier {
  @override
  EnterpriseInfoState build() {
    return const EnterpriseInfoState();
  }

  /// 获取企业信息
  Future<void> fetchEnterpriseInfo(String id) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final response = await EnterpriseInfoApi.getEnterpriseInfo(id);
      state = state.copyWith(
        enterpriseInfo: response.data,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 重新加载
  Future<void> reload(String id) async {
    await fetchEnterpriseInfo(id);
  }
}
