// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'enterprise_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_EnterpriseInfo _$EnterpriseInfoFromJson(Map<String, dynamic> json) =>
    _EnterpriseInfo(
      id: json['id'] as String?,
      enterpriseId: json['enterpriseId'] as String?,
      creditCode: json['creditCode'] as String?,
      legalPerson: json['legalPerson'] as String?,
      industry: json['industry'] as String?,
      enterpriseName: json['enterpriseName'] as String?,
      loginRegisterType: json['loginRegisterType'] as String?,
      phone: json['phone'] as String?,
      age: (json['age'] as num?)?.toInt(),
      registerDate: json['registerDate'] as String?,
      registerMonth: (json['registerMonth'] as num?)?.toInt(),
      evaluationResult: json['evaluationResult'] as String?,
      registeredCapital: json['registeredCapital'] as String?,
      qualification: json['qualification'] as String?,
      legalChangeStatus: json['legalChangeStatus'] as bool?,
      changeDate: json['changeDate'] as String?,
      stockStatus: json['stockStatus'] as bool?,
      stockPercent: json['stockPercent'] as String?,
      openAccountBank: json['openAccountBank'] as String?,
      registerAddress: json['registerAddress'] as String?,
      businessScope: json['businessScope'] as String?,
      createTime: json['createTime'] as String?,
    );

Map<String, dynamic> _$EnterpriseInfoToJson(_EnterpriseInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'enterpriseId': instance.enterpriseId,
      'creditCode': instance.creditCode,
      'legalPerson': instance.legalPerson,
      'industry': instance.industry,
      'enterpriseName': instance.enterpriseName,
      'loginRegisterType': instance.loginRegisterType,
      'phone': instance.phone,
      'age': instance.age,
      'registerDate': instance.registerDate,
      'registerMonth': instance.registerMonth,
      'evaluationResult': instance.evaluationResult,
      'registeredCapital': instance.registeredCapital,
      'qualification': instance.qualification,
      'legalChangeStatus': instance.legalChangeStatus,
      'changeDate': instance.changeDate,
      'stockStatus': instance.stockStatus,
      'stockPercent': instance.stockPercent,
      'openAccountBank': instance.openAccountBank,
      'registerAddress': instance.registerAddress,
      'businessScope': instance.businessScope,
      'createTime': instance.createTime,
    };
