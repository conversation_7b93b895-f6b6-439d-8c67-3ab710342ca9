// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'enterprise_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$EnterpriseInfo {
  /// ID
  String? get id;

  /// 企业ID
  String? get enterpriseId;

  /// 统一信用代码
  String? get creditCode;

  /// 法人代表
  String? get legalPerson;

  /// 行业
  String? get industry;

  /// 企业名称
  String? get enterpriseName;

  /// 登记注册类型
  String? get loginRegisterType;

  /// 电话
  String? get phone;

  /// 年龄
  int? get age;

  /// 注册日期
  String? get registerDate;

  /// 注册月数
  int? get registerMonth;

  /// 评估结果
  String? get evaluationResult;

  /// 注册资本
  String? get registeredCapital;

  /// 资质
  String? get qualification;

  /// 法人变更状态
  bool? get legalChangeStatus;

  /// 变更日期
  String? get changeDate;

  /// 股票状态
  bool? get stockStatus;

  /// 股票比例
  String? get stockPercent;

  /// 开户银行
  String? get openAccountBank;

  /// 注册地址
  String? get registerAddress;

  /// 经营范围
  String? get businessScope;

  /// 创建时间
  String? get createTime;

  /// Create a copy of EnterpriseInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $EnterpriseInfoCopyWith<EnterpriseInfo> get copyWith =>
      _$EnterpriseInfoCopyWithImpl<EnterpriseInfo>(
          this as EnterpriseInfo, _$identity);

  /// Serializes this EnterpriseInfo to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is EnterpriseInfo &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.enterpriseId, enterpriseId) ||
                other.enterpriseId == enterpriseId) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.legalPerson, legalPerson) ||
                other.legalPerson == legalPerson) &&
            (identical(other.industry, industry) ||
                other.industry == industry) &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.loginRegisterType, loginRegisterType) ||
                other.loginRegisterType == loginRegisterType) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.registerDate, registerDate) ||
                other.registerDate == registerDate) &&
            (identical(other.registerMonth, registerMonth) ||
                other.registerMonth == registerMonth) &&
            (identical(other.evaluationResult, evaluationResult) ||
                other.evaluationResult == evaluationResult) &&
            (identical(other.registeredCapital, registeredCapital) ||
                other.registeredCapital == registeredCapital) &&
            (identical(other.qualification, qualification) ||
                other.qualification == qualification) &&
            (identical(other.legalChangeStatus, legalChangeStatus) ||
                other.legalChangeStatus == legalChangeStatus) &&
            (identical(other.changeDate, changeDate) ||
                other.changeDate == changeDate) &&
            (identical(other.stockStatus, stockStatus) ||
                other.stockStatus == stockStatus) &&
            (identical(other.stockPercent, stockPercent) ||
                other.stockPercent == stockPercent) &&
            (identical(other.openAccountBank, openAccountBank) ||
                other.openAccountBank == openAccountBank) &&
            (identical(other.registerAddress, registerAddress) ||
                other.registerAddress == registerAddress) &&
            (identical(other.businessScope, businessScope) ||
                other.businessScope == businessScope) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        enterpriseId,
        creditCode,
        legalPerson,
        industry,
        enterpriseName,
        loginRegisterType,
        phone,
        age,
        registerDate,
        registerMonth,
        evaluationResult,
        registeredCapital,
        qualification,
        legalChangeStatus,
        changeDate,
        stockStatus,
        stockPercent,
        openAccountBank,
        registerAddress,
        businessScope,
        createTime
      ]);

  @override
  String toString() {
    return 'EnterpriseInfo(id: $id, enterpriseId: $enterpriseId, creditCode: $creditCode, legalPerson: $legalPerson, industry: $industry, enterpriseName: $enterpriseName, loginRegisterType: $loginRegisterType, phone: $phone, age: $age, registerDate: $registerDate, registerMonth: $registerMonth, evaluationResult: $evaluationResult, registeredCapital: $registeredCapital, qualification: $qualification, legalChangeStatus: $legalChangeStatus, changeDate: $changeDate, stockStatus: $stockStatus, stockPercent: $stockPercent, openAccountBank: $openAccountBank, registerAddress: $registerAddress, businessScope: $businessScope, createTime: $createTime)';
  }
}

/// @nodoc
abstract mixin class $EnterpriseInfoCopyWith<$Res> {
  factory $EnterpriseInfoCopyWith(
          EnterpriseInfo value, $Res Function(EnterpriseInfo) _then) =
      _$EnterpriseInfoCopyWithImpl;
  @useResult
  $Res call(
      {String? id,
      String? enterpriseId,
      String? creditCode,
      String? legalPerson,
      String? industry,
      String? enterpriseName,
      String? loginRegisterType,
      String? phone,
      int? age,
      String? registerDate,
      int? registerMonth,
      String? evaluationResult,
      String? registeredCapital,
      String? qualification,
      bool? legalChangeStatus,
      String? changeDate,
      bool? stockStatus,
      String? stockPercent,
      String? openAccountBank,
      String? registerAddress,
      String? businessScope,
      String? createTime});
}

/// @nodoc
class _$EnterpriseInfoCopyWithImpl<$Res>
    implements $EnterpriseInfoCopyWith<$Res> {
  _$EnterpriseInfoCopyWithImpl(this._self, this._then);

  final EnterpriseInfo _self;
  final $Res Function(EnterpriseInfo) _then;

  /// Create a copy of EnterpriseInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? enterpriseId = freezed,
    Object? creditCode = freezed,
    Object? legalPerson = freezed,
    Object? industry = freezed,
    Object? enterpriseName = freezed,
    Object? loginRegisterType = freezed,
    Object? phone = freezed,
    Object? age = freezed,
    Object? registerDate = freezed,
    Object? registerMonth = freezed,
    Object? evaluationResult = freezed,
    Object? registeredCapital = freezed,
    Object? qualification = freezed,
    Object? legalChangeStatus = freezed,
    Object? changeDate = freezed,
    Object? stockStatus = freezed,
    Object? stockPercent = freezed,
    Object? openAccountBank = freezed,
    Object? registerAddress = freezed,
    Object? businessScope = freezed,
    Object? createTime = freezed,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      enterpriseId: freezed == enterpriseId
          ? _self.enterpriseId
          : enterpriseId // ignore: cast_nullable_to_non_nullable
              as String?,
      creditCode: freezed == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String?,
      legalPerson: freezed == legalPerson
          ? _self.legalPerson
          : legalPerson // ignore: cast_nullable_to_non_nullable
              as String?,
      industry: freezed == industry
          ? _self.industry
          : industry // ignore: cast_nullable_to_non_nullable
              as String?,
      enterpriseName: freezed == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String?,
      loginRegisterType: freezed == loginRegisterType
          ? _self.loginRegisterType
          : loginRegisterType // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _self.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      age: freezed == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as int?,
      registerDate: freezed == registerDate
          ? _self.registerDate
          : registerDate // ignore: cast_nullable_to_non_nullable
              as String?,
      registerMonth: freezed == registerMonth
          ? _self.registerMonth
          : registerMonth // ignore: cast_nullable_to_non_nullable
              as int?,
      evaluationResult: freezed == evaluationResult
          ? _self.evaluationResult
          : evaluationResult // ignore: cast_nullable_to_non_nullable
              as String?,
      registeredCapital: freezed == registeredCapital
          ? _self.registeredCapital
          : registeredCapital // ignore: cast_nullable_to_non_nullable
              as String?,
      qualification: freezed == qualification
          ? _self.qualification
          : qualification // ignore: cast_nullable_to_non_nullable
              as String?,
      legalChangeStatus: freezed == legalChangeStatus
          ? _self.legalChangeStatus
          : legalChangeStatus // ignore: cast_nullable_to_non_nullable
              as bool?,
      changeDate: freezed == changeDate
          ? _self.changeDate
          : changeDate // ignore: cast_nullable_to_non_nullable
              as String?,
      stockStatus: freezed == stockStatus
          ? _self.stockStatus
          : stockStatus // ignore: cast_nullable_to_non_nullable
              as bool?,
      stockPercent: freezed == stockPercent
          ? _self.stockPercent
          : stockPercent // ignore: cast_nullable_to_non_nullable
              as String?,
      openAccountBank: freezed == openAccountBank
          ? _self.openAccountBank
          : openAccountBank // ignore: cast_nullable_to_non_nullable
              as String?,
      registerAddress: freezed == registerAddress
          ? _self.registerAddress
          : registerAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      businessScope: freezed == businessScope
          ? _self.businessScope
          : businessScope // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: freezed == createTime
          ? _self.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _EnterpriseInfo implements EnterpriseInfo {
  const _EnterpriseInfo(
      {this.id,
      this.enterpriseId,
      this.creditCode,
      this.legalPerson,
      this.industry,
      this.enterpriseName,
      this.loginRegisterType,
      this.phone,
      this.age,
      this.registerDate,
      this.registerMonth,
      this.evaluationResult,
      this.registeredCapital,
      this.qualification,
      this.legalChangeStatus,
      this.changeDate,
      this.stockStatus,
      this.stockPercent,
      this.openAccountBank,
      this.registerAddress,
      this.businessScope,
      this.createTime});
  factory _EnterpriseInfo.fromJson(Map<String, dynamic> json) =>
      _$EnterpriseInfoFromJson(json);

  /// ID
  @override
  final String? id;

  /// 企业ID
  @override
  final String? enterpriseId;

  /// 统一信用代码
  @override
  final String? creditCode;

  /// 法人代表
  @override
  final String? legalPerson;

  /// 行业
  @override
  final String? industry;

  /// 企业名称
  @override
  final String? enterpriseName;

  /// 登记注册类型
  @override
  final String? loginRegisterType;

  /// 电话
  @override
  final String? phone;

  /// 年龄
  @override
  final int? age;

  /// 注册日期
  @override
  final String? registerDate;

  /// 注册月数
  @override
  final int? registerMonth;

  /// 评估结果
  @override
  final String? evaluationResult;

  /// 注册资本
  @override
  final String? registeredCapital;

  /// 资质
  @override
  final String? qualification;

  /// 法人变更状态
  @override
  final bool? legalChangeStatus;

  /// 变更日期
  @override
  final String? changeDate;

  /// 股票状态
  @override
  final bool? stockStatus;

  /// 股票比例
  @override
  final String? stockPercent;

  /// 开户银行
  @override
  final String? openAccountBank;

  /// 注册地址
  @override
  final String? registerAddress;

  /// 经营范围
  @override
  final String? businessScope;

  /// 创建时间
  @override
  final String? createTime;

  /// Create a copy of EnterpriseInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$EnterpriseInfoCopyWith<_EnterpriseInfo> get copyWith =>
      __$EnterpriseInfoCopyWithImpl<_EnterpriseInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$EnterpriseInfoToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _EnterpriseInfo &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.enterpriseId, enterpriseId) ||
                other.enterpriseId == enterpriseId) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.legalPerson, legalPerson) ||
                other.legalPerson == legalPerson) &&
            (identical(other.industry, industry) ||
                other.industry == industry) &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.loginRegisterType, loginRegisterType) ||
                other.loginRegisterType == loginRegisterType) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.registerDate, registerDate) ||
                other.registerDate == registerDate) &&
            (identical(other.registerMonth, registerMonth) ||
                other.registerMonth == registerMonth) &&
            (identical(other.evaluationResult, evaluationResult) ||
                other.evaluationResult == evaluationResult) &&
            (identical(other.registeredCapital, registeredCapital) ||
                other.registeredCapital == registeredCapital) &&
            (identical(other.qualification, qualification) ||
                other.qualification == qualification) &&
            (identical(other.legalChangeStatus, legalChangeStatus) ||
                other.legalChangeStatus == legalChangeStatus) &&
            (identical(other.changeDate, changeDate) ||
                other.changeDate == changeDate) &&
            (identical(other.stockStatus, stockStatus) ||
                other.stockStatus == stockStatus) &&
            (identical(other.stockPercent, stockPercent) ||
                other.stockPercent == stockPercent) &&
            (identical(other.openAccountBank, openAccountBank) ||
                other.openAccountBank == openAccountBank) &&
            (identical(other.registerAddress, registerAddress) ||
                other.registerAddress == registerAddress) &&
            (identical(other.businessScope, businessScope) ||
                other.businessScope == businessScope) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        enterpriseId,
        creditCode,
        legalPerson,
        industry,
        enterpriseName,
        loginRegisterType,
        phone,
        age,
        registerDate,
        registerMonth,
        evaluationResult,
        registeredCapital,
        qualification,
        legalChangeStatus,
        changeDate,
        stockStatus,
        stockPercent,
        openAccountBank,
        registerAddress,
        businessScope,
        createTime
      ]);

  @override
  String toString() {
    return 'EnterpriseInfo(id: $id, enterpriseId: $enterpriseId, creditCode: $creditCode, legalPerson: $legalPerson, industry: $industry, enterpriseName: $enterpriseName, loginRegisterType: $loginRegisterType, phone: $phone, age: $age, registerDate: $registerDate, registerMonth: $registerMonth, evaluationResult: $evaluationResult, registeredCapital: $registeredCapital, qualification: $qualification, legalChangeStatus: $legalChangeStatus, changeDate: $changeDate, stockStatus: $stockStatus, stockPercent: $stockPercent, openAccountBank: $openAccountBank, registerAddress: $registerAddress, businessScope: $businessScope, createTime: $createTime)';
  }
}

/// @nodoc
abstract mixin class _$EnterpriseInfoCopyWith<$Res>
    implements $EnterpriseInfoCopyWith<$Res> {
  factory _$EnterpriseInfoCopyWith(
          _EnterpriseInfo value, $Res Function(_EnterpriseInfo) _then) =
      __$EnterpriseInfoCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? id,
      String? enterpriseId,
      String? creditCode,
      String? legalPerson,
      String? industry,
      String? enterpriseName,
      String? loginRegisterType,
      String? phone,
      int? age,
      String? registerDate,
      int? registerMonth,
      String? evaluationResult,
      String? registeredCapital,
      String? qualification,
      bool? legalChangeStatus,
      String? changeDate,
      bool? stockStatus,
      String? stockPercent,
      String? openAccountBank,
      String? registerAddress,
      String? businessScope,
      String? createTime});
}

/// @nodoc
class __$EnterpriseInfoCopyWithImpl<$Res>
    implements _$EnterpriseInfoCopyWith<$Res> {
  __$EnterpriseInfoCopyWithImpl(this._self, this._then);

  final _EnterpriseInfo _self;
  final $Res Function(_EnterpriseInfo) _then;

  /// Create a copy of EnterpriseInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? enterpriseId = freezed,
    Object? creditCode = freezed,
    Object? legalPerson = freezed,
    Object? industry = freezed,
    Object? enterpriseName = freezed,
    Object? loginRegisterType = freezed,
    Object? phone = freezed,
    Object? age = freezed,
    Object? registerDate = freezed,
    Object? registerMonth = freezed,
    Object? evaluationResult = freezed,
    Object? registeredCapital = freezed,
    Object? qualification = freezed,
    Object? legalChangeStatus = freezed,
    Object? changeDate = freezed,
    Object? stockStatus = freezed,
    Object? stockPercent = freezed,
    Object? openAccountBank = freezed,
    Object? registerAddress = freezed,
    Object? businessScope = freezed,
    Object? createTime = freezed,
  }) {
    return _then(_EnterpriseInfo(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      enterpriseId: freezed == enterpriseId
          ? _self.enterpriseId
          : enterpriseId // ignore: cast_nullable_to_non_nullable
              as String?,
      creditCode: freezed == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String?,
      legalPerson: freezed == legalPerson
          ? _self.legalPerson
          : legalPerson // ignore: cast_nullable_to_non_nullable
              as String?,
      industry: freezed == industry
          ? _self.industry
          : industry // ignore: cast_nullable_to_non_nullable
              as String?,
      enterpriseName: freezed == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String?,
      loginRegisterType: freezed == loginRegisterType
          ? _self.loginRegisterType
          : loginRegisterType // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _self.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      age: freezed == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as int?,
      registerDate: freezed == registerDate
          ? _self.registerDate
          : registerDate // ignore: cast_nullable_to_non_nullable
              as String?,
      registerMonth: freezed == registerMonth
          ? _self.registerMonth
          : registerMonth // ignore: cast_nullable_to_non_nullable
              as int?,
      evaluationResult: freezed == evaluationResult
          ? _self.evaluationResult
          : evaluationResult // ignore: cast_nullable_to_non_nullable
              as String?,
      registeredCapital: freezed == registeredCapital
          ? _self.registeredCapital
          : registeredCapital // ignore: cast_nullable_to_non_nullable
              as String?,
      qualification: freezed == qualification
          ? _self.qualification
          : qualification // ignore: cast_nullable_to_non_nullable
              as String?,
      legalChangeStatus: freezed == legalChangeStatus
          ? _self.legalChangeStatus
          : legalChangeStatus // ignore: cast_nullable_to_non_nullable
              as bool?,
      changeDate: freezed == changeDate
          ? _self.changeDate
          : changeDate // ignore: cast_nullable_to_non_nullable
              as String?,
      stockStatus: freezed == stockStatus
          ? _self.stockStatus
          : stockStatus // ignore: cast_nullable_to_non_nullable
              as bool?,
      stockPercent: freezed == stockPercent
          ? _self.stockPercent
          : stockPercent // ignore: cast_nullable_to_non_nullable
              as String?,
      openAccountBank: freezed == openAccountBank
          ? _self.openAccountBank
          : openAccountBank // ignore: cast_nullable_to_non_nullable
              as String?,
      registerAddress: freezed == registerAddress
          ? _self.registerAddress
          : registerAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      businessScope: freezed == businessScope
          ? _self.businessScope
          : businessScope // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: freezed == createTime
          ? _self.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
