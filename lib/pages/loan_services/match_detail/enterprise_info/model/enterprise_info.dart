import 'package:freezed_annotation/freezed_annotation.dart';

part 'enterprise_info.freezed.dart';
part 'enterprise_info.g.dart';

/// 企业合作伙伴信息
// @freezed
// abstract class QccEnterprisePartner with _$QccEnterprisePartner {
//   const factory QccEnterprisePartner({
//     /// 股东名称
//     String? stockName,
//     /// 股东类型
//     String? stockType,
//     /// 持股比例
//     String? stockPercent,
//     /// 认缴出资
//     String? shouldCapi,
//     /// 实缴出资
//     String? realCapi,
//     /// 出资日期
//     String? capiDate,
//     /// 认缴日期
//     String? shoudDate,
//     /// 最终受益比例
//     String? finalBenefitPercent,
//   }) = _QccEnterprisePartner;

//   factory QccEnterprisePartner.fromJson(Map<String, dynamic> json) =>
//       _$QccEnterprisePartnerFromJson(json);
// }

// /// 企业员工信息
// @freezed
// abstract class EnterpriseEmployee with _$EnterpriseEmployee {
//   const factory EnterpriseEmployee({
//     // 根据实际API返回的员工信息字段添加
//     String? name,
//     String? position,
//   }) = _EnterpriseEmployee;

//   factory EnterpriseEmployee.fromJson(Map<String, dynamic> json) =>
//       _$EnterpriseEmployeeFromJson(json);
// }

/// 企业信息数据模型
@freezed
abstract class EnterpriseInfo with _$EnterpriseInfo {
  const factory EnterpriseInfo({
    /// ID
    String? id,
    /// 企业ID
    String? enterpriseId,
    /// 统一信用代码
    String? creditCode,
    /// 法人代表
    String? legalPerson,
    /// 行业
    String? industry,
    /// 企业名称
    String? enterpriseName,
    /// 登记注册类型
    String? loginRegisterType,
    /// 电话
    String? phone,
    /// 年龄
    int? age,
    /// 注册日期
    String? registerDate,
    /// 注册月数
    int? registerMonth,
    /// 评估结果
    String? evaluationResult,
    /// 注册资本
    String? registeredCapital,
    /// 资质
    String? qualification,
    /// 法人变更状态
    bool? legalChangeStatus,
    /// 变更日期
    String? changeDate,
    /// 股票状态
    bool? stockStatus,
    /// 股票比例
    String? stockPercent,
    /// 开户银行
    String? openAccountBank,
    /// 注册地址
    String? registerAddress,
    /// 经营范围
    String? businessScope,
    /// 创建时间
    String? createTime,
    // /// 企业合作伙伴列表
    // @Default([]) List<QccEnterprisePartner> qccEnterprisePartnersList,
    // /// 企业员工列表
    // @Default([]) List<EnterpriseEmployee> enterpriseEmployeesList,
  }) = _EnterpriseInfo;

  factory EnterpriseInfo.fromJson(Map<String, dynamic> json) =>
      _$EnterpriseInfoFromJson(json);
}
