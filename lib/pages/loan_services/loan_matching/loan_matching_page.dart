import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:zrreport/common/index.dart';
import 'provider/index.dart';
import 'widgets/age_input.dart';
import 'widgets/index.dart';

/// 贷款匹配页面
class LoanMatchingPage extends ConsumerStatefulWidget {
  const LoanMatchingPage({super.key});

  @override
  ConsumerState<LoanMatchingPage> createState() => _LoanMatchingPageState();
}

class _LoanMatchingPageState extends ConsumerState<LoanMatchingPage> {
  @override
  void initState() {
    super.initState();
    // 页面初始化时请求定位权限
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // ref.read(loanMatchingNotifierProvider.notifier).requestLocationPermission();
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(loanMatchingNotifierProvider);
    final notifier = ref.read(loanMatchingNotifierProvider.notifier);

    return Scaffold(
      backgroundColor: const Color(0xFFF2F9FF),
      body: AnnotatedRegion(
        value: SystemUiOverlayStyle.dark,
        child: Stack(
          children: [
            SvgPicture.asset(
              AssetsSvgs.matchBackgroundSvg,
              height: 300,
              fit: BoxFit.fill,
            ),

            Container(
              margin: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
              alignment: Alignment.center,
              height: kToolbarHeight,
              child: _buildTitle(),
            ),
            SingleChildScrollView(
              child: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
                child: SafeArea(
                  child: Column(
                    children: [

                      SizedBox(height: 200),

                      // // 匹配类型切换
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                        ),
                        child: Column(
                          children: [
                            MatchingTypeTabs(
                              selectedType: state.matchingType,
                              onTypeChanged: notifier.switchMatchingType,
                            ),

                            const SizedBox(height: 24),

                            // 公司名称输入
                            CompanyNameInput(
                              value: state.companyName,
                              onChanged: notifier.updateCompanyName,
                              errorText:
                                  state.submitError?.contains('公司名称') == true
                                      ? state.submitError
                                      : null,
                            ),
                      
                            const SizedBox(height: 24),

                            // 申请人身份选择
                            ApplicantTypeSelector(
                              selectedType: state.applicantType,
                              onTypeChanged: notifier.updateApplicantType,
                            ),
                      
                            if (state.matchingType ==
                                MatchingType.questionnaire) ...[
                              const SizedBox(height: 24),
                              // 年龄输
                              AgeInput(
                                value: state.age.toString(),
                                onChanged: (value) {
                                  notifier.updateAge(int.tryParse(value) ?? 0);
                                },
                              ),
                            ],

                            const SizedBox(height: 32),
                            // 立即查询按钮
                            _submmitButton(state, notifier),

                            const SizedBox(height: 16),

                            // 分享按钮
                            _shareButton(),

                            const SizedBox(height: 32),
                          ],
                        ),
                      ),

                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Text _buildTitle() {
    return const Text(
      '产品匹配',
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  _submmitButton(LoanMatchingState state, LoanMatchingNotifier notifier) {
    return Builder(builder: (context) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        child: buildFilledButton(
          // state.isSubmitting ? '查询中...' : '立即查询',
          '立即查询',
          fontWeight: FontWeight.w600,
          onPressed: state.isSubmitting
              ? null
              : () {
                  notifier.clearErrors();
                  notifier.submitLoanMatching(context);
                },
        ),
      );
    }
    );
  }

  Container _shareButton() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      width: double.infinity,
      child: OutlinedButton(
        onPressed: () {
          // 分享功能
        },
        style: OutlinedButton.styleFrom(
          foregroundColor: const Color(0xFF4A90E2),
          side: const BorderSide(color: Color(0xFF4A90E2)),
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: const Text(
          '分享',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
