import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';
import '../provider/index.dart';

/// 匹配类型切换标签组件
class MatchingTypeTabs extends StatelessWidget {
  final MatchingType selectedType;
  final ValueChanged<MatchingType> onTypeChanged;

  const MatchingTypeTabs({
    super.key,
    required this.selectedType,
    required this.onTypeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 8),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Color(0xFFF6F9F8), 
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: MatchingType.values.map((type) {
          final isSelected = type == selectedType;
          return Expanded(
            child: GestureDetector(
              onTap: () => onTypeChanged(type),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.white: Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  type.displayName,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color:
                        isSelected ? AppColors.primary : AppColors.textColor6,
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
