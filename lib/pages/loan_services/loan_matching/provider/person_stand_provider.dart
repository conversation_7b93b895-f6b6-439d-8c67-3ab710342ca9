import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';
import '../models/person_stand_models.dart';

part 'person_stand_provider.g.dart';

/// 申请人身份数据提供者
@Riverpod(keepAlive: true)
class PersonStandNotifier extends _$PersonStandNotifier {
  @override
  Future<List<PersonStandItem>> build() async {
    return await _fetchPersonStandData();
  }

  /// 获取申请人身份数据
  Future<List<PersonStandItem>> _fetchPersonStandData() async {
    // 临时使用模拟数据进行测试
    // TODO: 删除这个模拟数据，使用真实API
    /*
    return [
      PersonStandItem(
        dictCode: '171',
        dictSort: 0,
        dictLabel: '法人',
        dictValue: '1',
        dictType: 'person_stand',
        listClass: 'default',
      ),
      PersonStandItem(
        dictCode: '172',
        dictSort: 0,
        dictLabel: '最大股东',
        dictValue: '2',
        dictType: 'person_stand',
        listClass: 'default',
      ),
      PersonStandItem(
        dictCode: '173',
        dictSort: 0,
        dictLabel: '股东',
        dictValue: '3',
        dictType: 'person_stand',
        listClass: 'default',
      ),
      PersonStandItem(
        dictCode: '174',
        dictSort: 0,
        dictLabel: '其他',
        dictValue: '0',
        dictType: 'person_stand',
        listClass: 'default',
      ),
    ];
    */

    try {
      final response =
          await SXHttpService.to.get('/dict/data/getDictTypes/person_stand');

      final personStandResponse = PersonStandResponse.fromJson(
        response.data,
        (data) => PersonStandData.fromJson(data as Map<String, dynamic>),
      );
      if (personStandResponse.code == 200 && personStandResponse.data != null) {
        final result = personStandResponse.data!.personStand;
        return result;
      } else {
        throw Exception(personStandResponse.message.isNotEmpty
            ? personStandResponse.message
            : '获取申请人身份数据失败');
      }
    } catch (e) {
      throw Exception('网络请求失败: $e');
    }
  }

  /// 刷新数据
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _fetchPersonStandData());
  }
}
