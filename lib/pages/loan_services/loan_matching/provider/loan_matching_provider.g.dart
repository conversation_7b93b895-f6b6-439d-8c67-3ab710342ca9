// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loan_matching_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$loanMatchingNotifierHash() =>
    r'763c449c610007af53579d1bc4bf2c424f018af6';

/// 贷款匹配页面状态
///
/// Copied from [LoanMatchingNotifier].
@ProviderFor(LoanMatchingNotifier)
final loanMatchingNotifierProvider = AutoDisposeNotifierProvider<
    LoanMatchingNotifier, LoanMatchingState>.internal(
  LoanMatchingNotifier.new,
  name: r'loanMatchingNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$loanMatchingNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LoanMatchingNotifier = AutoDisposeNotifier<LoanMatchingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
