// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'person_stand_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PersonStandItem _$PersonStandItemFromJson(Map<String, dynamic> json) =>
    _PersonStandItem(
      dictCode: json['dictCode'] as String,
      dictSort: (json['dictSort'] as num?)?.toInt() ?? 0,
      dictLabel: json['dictLabel'] as String,
      dictValue: json['dictValue'] as String,
      dictType: json['dictType'] as String,
      cssClass: json['cssClass'] as String?,
      listClass: json['listClass'] as String?,
      remark: json['remark'] as String?,
    );

Map<String, dynamic> _$PersonStandItemToJson(_PersonStandItem instance) =>
    <String, dynamic>{
      'dictCode': instance.dictCode,
      'dictSort': instance.dictSort,
      'dictLabel': instance.dictLabel,
      'dictValue': instance.dictValue,
      'dictType': instance.dictType,
      'cssClass': instance.cssClass,
      'listClass': instance.listClass,
      'remark': instance.remark,
    };

_PersonStandData _$PersonStandDataFromJson(Map<String, dynamic> json) =>
    _PersonStandData(
      personStand: (json['person_stand'] as List<dynamic>?)
              ?.map((e) => PersonStandItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$PersonStandDataToJson(_PersonStandData instance) =>
    <String, dynamic>{
      'person_stand': instance.personStand,
    };
