// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'quiz_preparation_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$QuizPreparationState {
  /// 企业名称
  String get enterpriseName;

  /// 统一社会信用代码
  String get creditCode;

  /// 申请人身份
  String get personStand;

  /// 申请人年龄
  String get age;

  /// 地区ID
  String get areaId;

  /// 是否正在提交
  bool get isSubmitting;

  /// Create a copy of QuizPreparationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QuizPreparationStateCopyWith<QuizPreparationState> get copyWith =>
      _$QuizPreparationStateCopyWithImpl<QuizPreparationState>(
          this as QuizPreparationState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is QuizPreparationState &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.personStand, personStand) ||
                other.personStand == personStand) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.areaId, areaId) || other.areaId == areaId) &&
            (identical(other.isSubmitting, isSubmitting) ||
                other.isSubmitting == isSubmitting));
  }

  @override
  int get hashCode => Object.hash(runtimeType, enterpriseName, creditCode,
      personStand, age, areaId, isSubmitting);

  @override
  String toString() {
    return 'QuizPreparationState(enterpriseName: $enterpriseName, creditCode: $creditCode, personStand: $personStand, age: $age, areaId: $areaId, isSubmitting: $isSubmitting)';
  }
}

/// @nodoc
abstract mixin class $QuizPreparationStateCopyWith<$Res> {
  factory $QuizPreparationStateCopyWith(QuizPreparationState value,
          $Res Function(QuizPreparationState) _then) =
      _$QuizPreparationStateCopyWithImpl;
  @useResult
  $Res call(
      {String enterpriseName,
      String creditCode,
      String personStand,
      String age,
      String areaId,
      bool isSubmitting});
}

/// @nodoc
class _$QuizPreparationStateCopyWithImpl<$Res>
    implements $QuizPreparationStateCopyWith<$Res> {
  _$QuizPreparationStateCopyWithImpl(this._self, this._then);

  final QuizPreparationState _self;
  final $Res Function(QuizPreparationState) _then;

  /// Create a copy of QuizPreparationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enterpriseName = null,
    Object? creditCode = null,
    Object? personStand = null,
    Object? age = null,
    Object? areaId = null,
    Object? isSubmitting = null,
  }) {
    return _then(_self.copyWith(
      enterpriseName: null == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String,
      creditCode: null == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String,
      personStand: null == personStand
          ? _self.personStand
          : personStand // ignore: cast_nullable_to_non_nullable
              as String,
      age: null == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as String,
      areaId: null == areaId
          ? _self.areaId
          : areaId // ignore: cast_nullable_to_non_nullable
              as String,
      isSubmitting: null == isSubmitting
          ? _self.isSubmitting
          : isSubmitting // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _QuizPreparationState implements QuizPreparationState {
  const _QuizPreparationState(
      {this.enterpriseName = '',
      this.creditCode = '',
      this.personStand = '',
      this.age = '',
      this.areaId = '',
      this.isSubmitting = false});

  /// 企业名称
  @override
  @JsonKey()
  final String enterpriseName;

  /// 统一社会信用代码
  @override
  @JsonKey()
  final String creditCode;

  /// 申请人身份
  @override
  @JsonKey()
  final String personStand;

  /// 申请人年龄
  @override
  @JsonKey()
  final String age;

  /// 地区ID
  @override
  @JsonKey()
  final String areaId;

  /// 是否正在提交
  @override
  @JsonKey()
  final bool isSubmitting;

  /// Create a copy of QuizPreparationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$QuizPreparationStateCopyWith<_QuizPreparationState> get copyWith =>
      __$QuizPreparationStateCopyWithImpl<_QuizPreparationState>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _QuizPreparationState &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.personStand, personStand) ||
                other.personStand == personStand) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.areaId, areaId) || other.areaId == areaId) &&
            (identical(other.isSubmitting, isSubmitting) ||
                other.isSubmitting == isSubmitting));
  }

  @override
  int get hashCode => Object.hash(runtimeType, enterpriseName, creditCode,
      personStand, age, areaId, isSubmitting);

  @override
  String toString() {
    return 'QuizPreparationState(enterpriseName: $enterpriseName, creditCode: $creditCode, personStand: $personStand, age: $age, areaId: $areaId, isSubmitting: $isSubmitting)';
  }
}

/// @nodoc
abstract mixin class _$QuizPreparationStateCopyWith<$Res>
    implements $QuizPreparationStateCopyWith<$Res> {
  factory _$QuizPreparationStateCopyWith(_QuizPreparationState value,
          $Res Function(_QuizPreparationState) _then) =
      __$QuizPreparationStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String enterpriseName,
      String creditCode,
      String personStand,
      String age,
      String areaId,
      bool isSubmitting});
}

/// @nodoc
class __$QuizPreparationStateCopyWithImpl<$Res>
    implements _$QuizPreparationStateCopyWith<$Res> {
  __$QuizPreparationStateCopyWithImpl(this._self, this._then);

  final _QuizPreparationState _self;
  final $Res Function(_QuizPreparationState) _then;

  /// Create a copy of QuizPreparationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? enterpriseName = null,
    Object? creditCode = null,
    Object? personStand = null,
    Object? age = null,
    Object? areaId = null,
    Object? isSubmitting = null,
  }) {
    return _then(_QuizPreparationState(
      enterpriseName: null == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String,
      creditCode: null == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String,
      personStand: null == personStand
          ? _self.personStand
          : personStand // ignore: cast_nullable_to_non_nullable
              as String,
      age: null == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as String,
      areaId: null == areaId
          ? _self.areaId
          : areaId // ignore: cast_nullable_to_non_nullable
              as String,
      isSubmitting: null == isSubmitting
          ? _self.isSubmitting
          : isSubmitting // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on
