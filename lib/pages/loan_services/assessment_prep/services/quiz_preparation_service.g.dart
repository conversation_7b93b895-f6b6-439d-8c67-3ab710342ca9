// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quiz_preparation_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$quizPreparationServiceHash() =>
    r'5cd356d1d0751961d5b7e00a3c9280742c5c0234';

/// 答题准备服务
///
/// Copied from [quizPreparationService].
@ProviderFor(quizPreparationService)
final quizPreparationServiceProvider =
    AutoDisposeProvider<QuizPreparationService>.internal(
  quizPreparationService,
  name: r'quizPreparationServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$quizPreparationServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef QuizPreparationServiceRef
    = AutoDisposeProviderRef<QuizPreparationService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
