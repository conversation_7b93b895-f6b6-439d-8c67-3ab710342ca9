# 答题准备页面实现总结

## 完成的功能

✅ **页面结构**
- 创建了 `quiz_preparation` 目录作为工作目录
- 实现了完整的页面布局，符合UI设计要求
- 包含企业名称显示、插图、引导文字、按钮和同意条款

✅ **状态管理**
- 使用 Riverpod + riverpod_annotation 进行状态管理
- 实现了 `QuizPreparationNotifier` 和 `QuizPreparationState`
- 支持参数初始化和状态更新

✅ **数据模型**
- 使用 Freezed + freezed_annotation 生成数据模型
- `QuizPreparationParams`: 页面参数模型
- `QuizPreparationRequest`: API请求参数模型
- `QuizPreparationData`: API响应数据模型
- `QuizPreparationState`: 页面状态模型

✅ **API服务**
- 实现了 `QuizPreparationService` 服务层
- 调用 `/portal/cmsMatchEnterprise/create/questionMatch` 接口
- 使用 `SXHttpService` 进行网络请求
- 正确处理响应数据和错误

✅ **UI组件**
- 标题栏显示"答题匹配"
- 企业名称显示区域（带红色星号标识）
- 中间插图使用 `match_quiz_start.svg`
- "即将开始答题"标题和提示文字
- "开始评估"按钮（支持加载状态）
- 同意条款复选框和链接

✅ **交互逻辑**
- 用户必须勾选同意条款才能开始评估
- 点击"开始评估"调用API接口
- 成功后自动跳转到 `QuizPage` 并销毁当前页面
- 失败时显示错误信息使用 `ErrorStatusWidget`

✅ **错误处理**
- 网络错误处理
- API错误信息显示
- 加载状态管理

## 技术实现细节

### 1. 目录结构
```
lib/pages/product/quiz_preparation/
├── models/quiz_preparation_models.dart
├── provider/quiz_preparation_provider.dart
├── services/quiz_preparation_service.dart
├── quiz_preparation_page.dart
├── example_usage.dart
├── index.dart
├── README.md
└── SUMMARY.md
```

### 2. 核心类说明

**QuizPreparationPage**
- 主页面组件，接受 `QuizPreparationParams` 参数
- 使用 `ConsumerStatefulWidget` 支持状态管理
- 实现了完整的UI布局和交互逻辑

**QuizPreparationNotifier**
- 状态管理类，继承自 Riverpod 的 Notifier
- 提供 `initializeWithParams` 方法初始化状态
- 实现 `startAssessment` 方法处理评估逻辑

**QuizPreparationService**
- API服务类，处理网络请求
- 使用 `SXHttpService` 调用后端接口
- 返回 `QuizPreparationResponse` 类型数据

### 3. 参数传递
页面接受以下参数：
- `creditCode`: 统一社会信用代码
- `enterpriseName`: 企业名称
- `personStand`: 申请人身份 
- `age`: 申请人年龄
- `areaId`: 地区ID
- `channelType`: 渠道类型（默认"routine"）

### 4. API接口
- **URL**: `/portal/cmsMatchEnterprise/create/questionMatch`
- **方法**: POST
- **请求参数**: 包含 matchStep(0), personStand, age, creditCode, enterpriseName, areaId, channelType
- **响应数据**: 包含 id, matchStatus 等字段

### 5. 页面跳转
成功调用API后，使用以下代码跳转到答题页面：
```dart
Navigator.of(context).pushReplacement(
  MaterialPageRoute(
    builder: (context) => QuizPage(
      matchId: response.data!.id,
      matchType: "1",
    ),
  ),
);
```

## 使用方法

```dart
// 创建参数
final params = QuizPreparationParams(
  creditCode: "91360500859876371Q",
  enterpriseName: "中国建设银行股份有限公司新余市分行",
  personStand: "1",
  age: "36",
  areaId: "20",
  channelType: "routine",
);

// 导航到页面
Navigator.of(context).push(
  MaterialPageRoute(
    builder: (context) => QuizPreparationPage(params: params),
  ),
);
```

## 注意事项

1. 页面需要传入完整的 `QuizPreparationParams` 参数
2. 用户必须勾选同意条款才能开始评估
3. 成功后会自动销毁当前页面并跳转到答题页面
4. 错误信息使用 `ErrorStatusWidget` 显示
5. 所有代码都已生成并通过编译测试

## 文件说明

- `quiz_preparation_page.dart`: 主页面实现
- `models/quiz_preparation_models.dart`: 数据模型定义
- `provider/quiz_preparation_provider.dart`: 状态管理
- `services/quiz_preparation_service.dart`: API服务
- `example_usage.dart`: 使用示例
- `README.md`: 详细文档
- `index.dart`: 模块导出
