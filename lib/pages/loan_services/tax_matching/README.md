# 税务匹配补充材料页面

## 概述

税务匹配补充材料页面用于收集用户的税务登录信息，支持不同省份的税务登录流程。

## 功能特性

- ✅ 支持省份特定的登录流程
- ✅ 自动判断是否需要短信验证码
- ✅ 验证码倒计时功能
- ✅ 表单验证和错误处理
- ✅ 成功页面展示
- ✅ 使用 Riverpod AutoDispose 进行状态管理
- ✅ 使用 Freezed 生成数据模型
- ✅ 页面销毁时自动清理状态和资源

## 使用方法

### 1. 导航到页面

```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => TaxSupplementPage(
      creditCode: '91420107768067296N',
      enterpriseName: '武汉好又多（民意）百货商业有限公司青山分公司',
      provinceId: '6',
      personStand: '1',
    ),
  ),
);
```

### 2. 页面参数

- `creditCode`: 纳税人识别号
- `enterpriseName`: 公司名称
- `provinceId`: 省份ID
- `personStand`: 申请人身份

## 登录流程

### 不需要验证码的省份
- 浙江 (zhejiang)
- 湖北 (hubei)
- 广东 (guangdong)

这些省份直接调用 `QueryApi.taxOnlyAccountLogin` 接口。

### 需要验证码的省份
其他省份需要两步验证：
1. 调用 `QueryApi.taxLogin` 获取登录信息
2. 发送验证码 `QueryApi.taxSendSmsCode`
3. 验证码登录 `QueryApi.taxCaptchaLogin`

## API 接口

### 1. 不需要验证码登录
```
POST /tax/tax/onlyAccountLogin
```

### 2. 需要验证码登录（第一步）
```
POST /tax/tax/login
```

### 3. 发送验证码
```
POST /tax/tax/sendCode
```

### 4. 验证码登录
```
POST /tax/tax/captchaLogin
```

### 5. 创建匹配企业
```
POST /cmsMatchEnterprise/createMatchEnterprise
```

## 状态管理

使用 `TaxSupplementNotifier` 管理页面状态，采用 AutoDispose 模式：

```dart
final state = ref.watch(taxSupplementNotifierProvider);
final notifier = ref.read(taxSupplementNotifierProvider.notifier);
```

### AutoDispose 特性

- **自动清理**: 页面销毁时自动清理状态和定时器
- **状态重置**: 每次重新进入页面都会获得全新的状态
- **内存优化**: 避免内存泄漏和状态污染

## 数据模型

### TaxSupplementState
页面状态模型，包含：
- 企业信息
- 用户输入
- 验证码状态
- 加载状态

### CreateMatchEnterpriseRequest/Response
创建匹配企业的请求和响应模型

## 错误处理

- 使用 `Loading.error()` 显示错误信息
- 网络错误自动重试
- 表单验证错误提示

## 注意事项

1. **AutoDispose**: 使用 AutoDispose provider，页面销毁时自动清理所有资源
2. **状态重置**: 每次重新进入页面都会获得全新的状态，避免状态污染
3. **验证码限制**: 验证码有60秒倒计时限制
4. **协议同意**: 必须同意授权协议才能提交
5. **成功页面**: 成功后会显示匹配结果页面

## 解决的问题

- ✅ **状态污染**: 每次重新进入页面时状态不会保留上一次的数据
- ✅ **内存泄漏**: 自动清理定时器和其他资源
- ✅ **生命周期管理**: 正确的provider生命周期管理
