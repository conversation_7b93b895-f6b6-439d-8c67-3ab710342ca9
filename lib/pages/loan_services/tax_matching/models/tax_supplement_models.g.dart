// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tax_supplement_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TaxSupplementState _$TaxSupplementStateFromJson(Map<String, dynamic> json) =>
    _TaxSupplementState(
      enterpriseName: json['enterpriseName'] as String? ?? '',
      creditCode: json['creditCode'] as String? ?? '',
      provinceId: json['provinceId'] as String? ?? '',
      personStand: json['personStand'] as String? ?? '',
      provinceName: json['provinceName'] as String? ?? '',
      taxAccount: json['taxAccount'] as String? ?? '',
      taxPassword: json['taxPassword'] as String? ?? '',
      captchaCode: json['captchaCode'] as String? ?? '',
      needsCaptcha: json['needsCaptcha'] as bool? ?? false,
      agreedToTerms: json['agreedToTerms'] as bool? ?? false,
      captchaCountdown: (json['captchaCountdown'] as num?)?.toInt() ?? 0,
      taxLoginModel: json['taxLoginModel'] == null
          ? null
          : TaxLoginModel.fromJson(
              json['taxLoginModel'] as Map<String, dynamic>),
      taxOnlyAccountLoginModel: json['taxOnlyAccountLoginModel'] == null
          ? null
          : TaxOnlyAccountLoginModel.fromJson(
              json['taxOnlyAccountLoginModel'] as Map<String, dynamic>),
      isLoading: json['isLoading'] as bool? ?? false,
      showSuccessPage: json['showSuccessPage'] as bool? ?? false,
    );

Map<String, dynamic> _$TaxSupplementStateToJson(_TaxSupplementState instance) =>
    <String, dynamic>{
      'enterpriseName': instance.enterpriseName,
      'creditCode': instance.creditCode,
      'provinceId': instance.provinceId,
      'personStand': instance.personStand,
      'provinceName': instance.provinceName,
      'taxAccount': instance.taxAccount,
      'taxPassword': instance.taxPassword,
      'captchaCode': instance.captchaCode,
      'needsCaptcha': instance.needsCaptcha,
      'agreedToTerms': instance.agreedToTerms,
      'captchaCountdown': instance.captchaCountdown,
      'taxLoginModel': instance.taxLoginModel,
      'taxOnlyAccountLoginModel': instance.taxOnlyAccountLoginModel,
      'isLoading': instance.isLoading,
      'showSuccessPage': instance.showSuccessPage,
    };

_CreateMatchEnterpriseRequest _$CreateMatchEnterpriseRequestFromJson(
        Map<String, dynamic> json) =>
    _CreateMatchEnterpriseRequest(
      taskId: json['taskId'] as String,
      enterpriseId: json['enterpriseId'] as String,
      provinceName: json['provinceName'] as String,
      creditCode: json['creditCode'] as String,
      phone: json['phone'] as String,
      creditPwd: json['creditPwd'] as String,
      areaId: json['areaId'] as String,
      garageStatus: (json['garageStatus'] as num?)?.toInt() ?? 0,
      enterpriseName: json['enterpriseName'] as String,
      personStand: json['personStand'] as String,
      channelType: json['channelType'] as String? ?? 'routine',
    );

Map<String, dynamic> _$CreateMatchEnterpriseRequestToJson(
        _CreateMatchEnterpriseRequest instance) =>
    <String, dynamic>{
      'taskId': instance.taskId,
      'enterpriseId': instance.enterpriseId,
      'provinceName': instance.provinceName,
      'creditCode': instance.creditCode,
      'phone': instance.phone,
      'creditPwd': instance.creditPwd,
      'areaId': instance.areaId,
      'garageStatus': instance.garageStatus,
      'enterpriseName': instance.enterpriseName,
      'personStand': instance.personStand,
      'channelType': instance.channelType,
    };

_CreateMatchEnterpriseResponse _$CreateMatchEnterpriseResponseFromJson(
        Map<String, dynamic> json) =>
    _CreateMatchEnterpriseResponse(
      enterpriseId: json['enterpriseId'] as String,
      creditCode: json['creditCode'] as String?,
      id: json['id'] as String,
      matchStatus: (json['matchStatus'] as num).toInt(),
    );

Map<String, dynamic> _$CreateMatchEnterpriseResponseToJson(
        _CreateMatchEnterpriseResponse instance) =>
    <String, dynamic>{
      'enterpriseId': instance.enterpriseId,
      'creditCode': instance.creditCode,
      'id': instance.id,
      'matchStatus': instance.matchStatus,
    };
