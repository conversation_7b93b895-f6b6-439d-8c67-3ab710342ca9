// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tax_supplement_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TaxSupplementState {
  /// 公司名称
  String get enterpriseName;

  /// 纳税人识别号
  String get creditCode;

  /// 省份ID
  String get provinceId;

  /// 申请人身份
  String get personStand;

  /// 省份名称
  String get provinceName;

  /// 电子税务局账号
  String get taxAccount;

  /// 税务密码
  String get taxPassword;

  /// 验证码
  String get captchaCode;

  /// 是否需要验证码
  bool get needsCaptcha;

  /// 是否同意协议·
  bool get agreedToTerms;

  /// 验证码倒计时
  int get captchaCountdown;

  /// 税务登录模型（需要验证码时使用
  TaxLoginModel? get taxLoginModel;

  /// 税务登录模型（不需要验证码时使用
  TaxOnlyAccountLoginModel? get taxOnlyAccountLoginModel;

  /// 是否正在加载
  bool get isLoading;

  /// 是否显示成功页面
  bool get showSuccessPage;

  /// Create a copy of TaxSupplementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TaxSupplementStateCopyWith<TaxSupplementState> get copyWith =>
      _$TaxSupplementStateCopyWithImpl<TaxSupplementState>(
          this as TaxSupplementState, _$identity);

  /// Serializes this TaxSupplementState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TaxSupplementState &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.provinceId, provinceId) ||
                other.provinceId == provinceId) &&
            (identical(other.personStand, personStand) ||
                other.personStand == personStand) &&
            (identical(other.provinceName, provinceName) ||
                other.provinceName == provinceName) &&
            (identical(other.taxAccount, taxAccount) ||
                other.taxAccount == taxAccount) &&
            (identical(other.taxPassword, taxPassword) ||
                other.taxPassword == taxPassword) &&
            (identical(other.captchaCode, captchaCode) ||
                other.captchaCode == captchaCode) &&
            (identical(other.needsCaptcha, needsCaptcha) ||
                other.needsCaptcha == needsCaptcha) &&
            (identical(other.agreedToTerms, agreedToTerms) ||
                other.agreedToTerms == agreedToTerms) &&
            (identical(other.captchaCountdown, captchaCountdown) ||
                other.captchaCountdown == captchaCountdown) &&
            (identical(other.taxLoginModel, taxLoginModel) ||
                other.taxLoginModel == taxLoginModel) &&
            (identical(
                    other.taxOnlyAccountLoginModel, taxOnlyAccountLoginModel) ||
                other.taxOnlyAccountLoginModel == taxOnlyAccountLoginModel) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.showSuccessPage, showSuccessPage) ||
                other.showSuccessPage == showSuccessPage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      enterpriseName,
      creditCode,
      provinceId,
      personStand,
      provinceName,
      taxAccount,
      taxPassword,
      captchaCode,
      needsCaptcha,
      agreedToTerms,
      captchaCountdown,
      taxLoginModel,
      taxOnlyAccountLoginModel,
      isLoading,
      showSuccessPage);

  @override
  String toString() {
    return 'TaxSupplementState(enterpriseName: $enterpriseName, creditCode: $creditCode, provinceId: $provinceId, personStand: $personStand, provinceName: $provinceName, taxAccount: $taxAccount, taxPassword: $taxPassword, captchaCode: $captchaCode, needsCaptcha: $needsCaptcha, agreedToTerms: $agreedToTerms, captchaCountdown: $captchaCountdown, taxLoginModel: $taxLoginModel, taxOnlyAccountLoginModel: $taxOnlyAccountLoginModel, isLoading: $isLoading, showSuccessPage: $showSuccessPage)';
  }
}

/// @nodoc
abstract mixin class $TaxSupplementStateCopyWith<$Res> {
  factory $TaxSupplementStateCopyWith(
          TaxSupplementState value, $Res Function(TaxSupplementState) _then) =
      _$TaxSupplementStateCopyWithImpl;
  @useResult
  $Res call(
      {String enterpriseName,
      String creditCode,
      String provinceId,
      String personStand,
      String provinceName,
      String taxAccount,
      String taxPassword,
      String captchaCode,
      bool needsCaptcha,
      bool agreedToTerms,
      int captchaCountdown,
      TaxLoginModel? taxLoginModel,
      TaxOnlyAccountLoginModel? taxOnlyAccountLoginModel,
      bool isLoading,
      bool showSuccessPage});
}

/// @nodoc
class _$TaxSupplementStateCopyWithImpl<$Res>
    implements $TaxSupplementStateCopyWith<$Res> {
  _$TaxSupplementStateCopyWithImpl(this._self, this._then);

  final TaxSupplementState _self;
  final $Res Function(TaxSupplementState) _then;

  /// Create a copy of TaxSupplementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enterpriseName = null,
    Object? creditCode = null,
    Object? provinceId = null,
    Object? personStand = null,
    Object? provinceName = null,
    Object? taxAccount = null,
    Object? taxPassword = null,
    Object? captchaCode = null,
    Object? needsCaptcha = null,
    Object? agreedToTerms = null,
    Object? captchaCountdown = null,
    Object? taxLoginModel = freezed,
    Object? taxOnlyAccountLoginModel = freezed,
    Object? isLoading = null,
    Object? showSuccessPage = null,
  }) {
    return _then(_self.copyWith(
      enterpriseName: null == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String,
      creditCode: null == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String,
      provinceId: null == provinceId
          ? _self.provinceId
          : provinceId // ignore: cast_nullable_to_non_nullable
              as String,
      personStand: null == personStand
          ? _self.personStand
          : personStand // ignore: cast_nullable_to_non_nullable
              as String,
      provinceName: null == provinceName
          ? _self.provinceName
          : provinceName // ignore: cast_nullable_to_non_nullable
              as String,
      taxAccount: null == taxAccount
          ? _self.taxAccount
          : taxAccount // ignore: cast_nullable_to_non_nullable
              as String,
      taxPassword: null == taxPassword
          ? _self.taxPassword
          : taxPassword // ignore: cast_nullable_to_non_nullable
              as String,
      captchaCode: null == captchaCode
          ? _self.captchaCode
          : captchaCode // ignore: cast_nullable_to_non_nullable
              as String,
      needsCaptcha: null == needsCaptcha
          ? _self.needsCaptcha
          : needsCaptcha // ignore: cast_nullable_to_non_nullable
              as bool,
      agreedToTerms: null == agreedToTerms
          ? _self.agreedToTerms
          : agreedToTerms // ignore: cast_nullable_to_non_nullable
              as bool,
      captchaCountdown: null == captchaCountdown
          ? _self.captchaCountdown
          : captchaCountdown // ignore: cast_nullable_to_non_nullable
              as int,
      taxLoginModel: freezed == taxLoginModel
          ? _self.taxLoginModel
          : taxLoginModel // ignore: cast_nullable_to_non_nullable
              as TaxLoginModel?,
      taxOnlyAccountLoginModel: freezed == taxOnlyAccountLoginModel
          ? _self.taxOnlyAccountLoginModel
          : taxOnlyAccountLoginModel // ignore: cast_nullable_to_non_nullable
              as TaxOnlyAccountLoginModel?,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      showSuccessPage: null == showSuccessPage
          ? _self.showSuccessPage
          : showSuccessPage // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _TaxSupplementState implements TaxSupplementState {
  const _TaxSupplementState(
      {this.enterpriseName = '',
      this.creditCode = '',
      this.provinceId = '',
      this.personStand = '',
      this.provinceName = '',
      this.taxAccount = '',
      this.taxPassword = '',
      this.captchaCode = '',
      this.needsCaptcha = false,
      this.agreedToTerms = false,
      this.captchaCountdown = 0,
      this.taxLoginModel,
      this.taxOnlyAccountLoginModel,
      this.isLoading = false,
      this.showSuccessPage = false});
  factory _TaxSupplementState.fromJson(Map<String, dynamic> json) =>
      _$TaxSupplementStateFromJson(json);

  /// 公司名称
  @override
  @JsonKey()
  final String enterpriseName;

  /// 纳税人识别号
  @override
  @JsonKey()
  final String creditCode;

  /// 省份ID
  @override
  @JsonKey()
  final String provinceId;

  /// 申请人身份
  @override
  @JsonKey()
  final String personStand;

  /// 省份名称
  @override
  @JsonKey()
  final String provinceName;

  /// 电子税务局账号
  @override
  @JsonKey()
  final String taxAccount;

  /// 税务密码
  @override
  @JsonKey()
  final String taxPassword;

  /// 验证码
  @override
  @JsonKey()
  final String captchaCode;

  /// 是否需要验证码
  @override
  @JsonKey()
  final bool needsCaptcha;

  /// 是否同意协议·
  @override
  @JsonKey()
  final bool agreedToTerms;

  /// 验证码倒计时
  @override
  @JsonKey()
  final int captchaCountdown;

  /// 税务登录模型（需要验证码时使用
  @override
  final TaxLoginModel? taxLoginModel;

  /// 税务登录模型（不需要验证码时使用
  @override
  final TaxOnlyAccountLoginModel? taxOnlyAccountLoginModel;

  /// 是否正在加载
  @override
  @JsonKey()
  final bool isLoading;

  /// 是否显示成功页面
  @override
  @JsonKey()
  final bool showSuccessPage;

  /// Create a copy of TaxSupplementState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TaxSupplementStateCopyWith<_TaxSupplementState> get copyWith =>
      __$TaxSupplementStateCopyWithImpl<_TaxSupplementState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TaxSupplementStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TaxSupplementState &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.provinceId, provinceId) ||
                other.provinceId == provinceId) &&
            (identical(other.personStand, personStand) ||
                other.personStand == personStand) &&
            (identical(other.provinceName, provinceName) ||
                other.provinceName == provinceName) &&
            (identical(other.taxAccount, taxAccount) ||
                other.taxAccount == taxAccount) &&
            (identical(other.taxPassword, taxPassword) ||
                other.taxPassword == taxPassword) &&
            (identical(other.captchaCode, captchaCode) ||
                other.captchaCode == captchaCode) &&
            (identical(other.needsCaptcha, needsCaptcha) ||
                other.needsCaptcha == needsCaptcha) &&
            (identical(other.agreedToTerms, agreedToTerms) ||
                other.agreedToTerms == agreedToTerms) &&
            (identical(other.captchaCountdown, captchaCountdown) ||
                other.captchaCountdown == captchaCountdown) &&
            (identical(other.taxLoginModel, taxLoginModel) ||
                other.taxLoginModel == taxLoginModel) &&
            (identical(
                    other.taxOnlyAccountLoginModel, taxOnlyAccountLoginModel) ||
                other.taxOnlyAccountLoginModel == taxOnlyAccountLoginModel) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.showSuccessPage, showSuccessPage) ||
                other.showSuccessPage == showSuccessPage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      enterpriseName,
      creditCode,
      provinceId,
      personStand,
      provinceName,
      taxAccount,
      taxPassword,
      captchaCode,
      needsCaptcha,
      agreedToTerms,
      captchaCountdown,
      taxLoginModel,
      taxOnlyAccountLoginModel,
      isLoading,
      showSuccessPage);

  @override
  String toString() {
    return 'TaxSupplementState(enterpriseName: $enterpriseName, creditCode: $creditCode, provinceId: $provinceId, personStand: $personStand, provinceName: $provinceName, taxAccount: $taxAccount, taxPassword: $taxPassword, captchaCode: $captchaCode, needsCaptcha: $needsCaptcha, agreedToTerms: $agreedToTerms, captchaCountdown: $captchaCountdown, taxLoginModel: $taxLoginModel, taxOnlyAccountLoginModel: $taxOnlyAccountLoginModel, isLoading: $isLoading, showSuccessPage: $showSuccessPage)';
  }
}

/// @nodoc
abstract mixin class _$TaxSupplementStateCopyWith<$Res>
    implements $TaxSupplementStateCopyWith<$Res> {
  factory _$TaxSupplementStateCopyWith(
          _TaxSupplementState value, $Res Function(_TaxSupplementState) _then) =
      __$TaxSupplementStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String enterpriseName,
      String creditCode,
      String provinceId,
      String personStand,
      String provinceName,
      String taxAccount,
      String taxPassword,
      String captchaCode,
      bool needsCaptcha,
      bool agreedToTerms,
      int captchaCountdown,
      TaxLoginModel? taxLoginModel,
      TaxOnlyAccountLoginModel? taxOnlyAccountLoginModel,
      bool isLoading,
      bool showSuccessPage});
}

/// @nodoc
class __$TaxSupplementStateCopyWithImpl<$Res>
    implements _$TaxSupplementStateCopyWith<$Res> {
  __$TaxSupplementStateCopyWithImpl(this._self, this._then);

  final _TaxSupplementState _self;
  final $Res Function(_TaxSupplementState) _then;

  /// Create a copy of TaxSupplementState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? enterpriseName = null,
    Object? creditCode = null,
    Object? provinceId = null,
    Object? personStand = null,
    Object? provinceName = null,
    Object? taxAccount = null,
    Object? taxPassword = null,
    Object? captchaCode = null,
    Object? needsCaptcha = null,
    Object? agreedToTerms = null,
    Object? captchaCountdown = null,
    Object? taxLoginModel = freezed,
    Object? taxOnlyAccountLoginModel = freezed,
    Object? isLoading = null,
    Object? showSuccessPage = null,
  }) {
    return _then(_TaxSupplementState(
      enterpriseName: null == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String,
      creditCode: null == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String,
      provinceId: null == provinceId
          ? _self.provinceId
          : provinceId // ignore: cast_nullable_to_non_nullable
              as String,
      personStand: null == personStand
          ? _self.personStand
          : personStand // ignore: cast_nullable_to_non_nullable
              as String,
      provinceName: null == provinceName
          ? _self.provinceName
          : provinceName // ignore: cast_nullable_to_non_nullable
              as String,
      taxAccount: null == taxAccount
          ? _self.taxAccount
          : taxAccount // ignore: cast_nullable_to_non_nullable
              as String,
      taxPassword: null == taxPassword
          ? _self.taxPassword
          : taxPassword // ignore: cast_nullable_to_non_nullable
              as String,
      captchaCode: null == captchaCode
          ? _self.captchaCode
          : captchaCode // ignore: cast_nullable_to_non_nullable
              as String,
      needsCaptcha: null == needsCaptcha
          ? _self.needsCaptcha
          : needsCaptcha // ignore: cast_nullable_to_non_nullable
              as bool,
      agreedToTerms: null == agreedToTerms
          ? _self.agreedToTerms
          : agreedToTerms // ignore: cast_nullable_to_non_nullable
              as bool,
      captchaCountdown: null == captchaCountdown
          ? _self.captchaCountdown
          : captchaCountdown // ignore: cast_nullable_to_non_nullable
              as int,
      taxLoginModel: freezed == taxLoginModel
          ? _self.taxLoginModel
          : taxLoginModel // ignore: cast_nullable_to_non_nullable
              as TaxLoginModel?,
      taxOnlyAccountLoginModel: freezed == taxOnlyAccountLoginModel
          ? _self.taxOnlyAccountLoginModel
          : taxOnlyAccountLoginModel // ignore: cast_nullable_to_non_nullable
              as TaxOnlyAccountLoginModel?,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      showSuccessPage: null == showSuccessPage
          ? _self.showSuccessPage
          : showSuccessPage // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
mixin _$CreateMatchEnterpriseRequest {
  String get taskId;
  String get enterpriseId;
  String get provinceName;
  String get creditCode;
  String get phone;
  String get creditPwd;
  String get areaId;
  int get garageStatus;
  String get enterpriseName;
  String get personStand;
  String get channelType;

  /// Create a copy of CreateMatchEnterpriseRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CreateMatchEnterpriseRequestCopyWith<CreateMatchEnterpriseRequest>
      get copyWith => _$CreateMatchEnterpriseRequestCopyWithImpl<
              CreateMatchEnterpriseRequest>(
          this as CreateMatchEnterpriseRequest, _$identity);

  /// Serializes this CreateMatchEnterpriseRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CreateMatchEnterpriseRequest &&
            (identical(other.taskId, taskId) || other.taskId == taskId) &&
            (identical(other.enterpriseId, enterpriseId) ||
                other.enterpriseId == enterpriseId) &&
            (identical(other.provinceName, provinceName) ||
                other.provinceName == provinceName) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.creditPwd, creditPwd) ||
                other.creditPwd == creditPwd) &&
            (identical(other.areaId, areaId) || other.areaId == areaId) &&
            (identical(other.garageStatus, garageStatus) ||
                other.garageStatus == garageStatus) &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.personStand, personStand) ||
                other.personStand == personStand) &&
            (identical(other.channelType, channelType) ||
                other.channelType == channelType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      taskId,
      enterpriseId,
      provinceName,
      creditCode,
      phone,
      creditPwd,
      areaId,
      garageStatus,
      enterpriseName,
      personStand,
      channelType);

  @override
  String toString() {
    return 'CreateMatchEnterpriseRequest(taskId: $taskId, enterpriseId: $enterpriseId, provinceName: $provinceName, creditCode: $creditCode, phone: $phone, creditPwd: $creditPwd, areaId: $areaId, garageStatus: $garageStatus, enterpriseName: $enterpriseName, personStand: $personStand, channelType: $channelType)';
  }
}

/// @nodoc
abstract mixin class $CreateMatchEnterpriseRequestCopyWith<$Res> {
  factory $CreateMatchEnterpriseRequestCopyWith(
          CreateMatchEnterpriseRequest value,
          $Res Function(CreateMatchEnterpriseRequest) _then) =
      _$CreateMatchEnterpriseRequestCopyWithImpl;
  @useResult
  $Res call(
      {String taskId,
      String enterpriseId,
      String provinceName,
      String creditCode,
      String phone,
      String creditPwd,
      String areaId,
      int garageStatus,
      String enterpriseName,
      String personStand,
      String channelType});
}

/// @nodoc
class _$CreateMatchEnterpriseRequestCopyWithImpl<$Res>
    implements $CreateMatchEnterpriseRequestCopyWith<$Res> {
  _$CreateMatchEnterpriseRequestCopyWithImpl(this._self, this._then);

  final CreateMatchEnterpriseRequest _self;
  final $Res Function(CreateMatchEnterpriseRequest) _then;

  /// Create a copy of CreateMatchEnterpriseRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = null,
    Object? enterpriseId = null,
    Object? provinceName = null,
    Object? creditCode = null,
    Object? phone = null,
    Object? creditPwd = null,
    Object? areaId = null,
    Object? garageStatus = null,
    Object? enterpriseName = null,
    Object? personStand = null,
    Object? channelType = null,
  }) {
    return _then(_self.copyWith(
      taskId: null == taskId
          ? _self.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String,
      enterpriseId: null == enterpriseId
          ? _self.enterpriseId
          : enterpriseId // ignore: cast_nullable_to_non_nullable
              as String,
      provinceName: null == provinceName
          ? _self.provinceName
          : provinceName // ignore: cast_nullable_to_non_nullable
              as String,
      creditCode: null == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String,
      phone: null == phone
          ? _self.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String,
      creditPwd: null == creditPwd
          ? _self.creditPwd
          : creditPwd // ignore: cast_nullable_to_non_nullable
              as String,
      areaId: null == areaId
          ? _self.areaId
          : areaId // ignore: cast_nullable_to_non_nullable
              as String,
      garageStatus: null == garageStatus
          ? _self.garageStatus
          : garageStatus // ignore: cast_nullable_to_non_nullable
              as int,
      enterpriseName: null == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String,
      personStand: null == personStand
          ? _self.personStand
          : personStand // ignore: cast_nullable_to_non_nullable
              as String,
      channelType: null == channelType
          ? _self.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _CreateMatchEnterpriseRequest implements CreateMatchEnterpriseRequest {
  const _CreateMatchEnterpriseRequest(
      {required this.taskId,
      required this.enterpriseId,
      required this.provinceName,
      required this.creditCode,
      required this.phone,
      required this.creditPwd,
      required this.areaId,
      this.garageStatus = 0,
      required this.enterpriseName,
      required this.personStand,
      this.channelType = 'routine'});
  factory _CreateMatchEnterpriseRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateMatchEnterpriseRequestFromJson(json);

  @override
  final String taskId;
  @override
  final String enterpriseId;
  @override
  final String provinceName;
  @override
  final String creditCode;
  @override
  final String phone;
  @override
  final String creditPwd;
  @override
  final String areaId;
  @override
  @JsonKey()
  final int garageStatus;
  @override
  final String enterpriseName;
  @override
  final String personStand;
  @override
  @JsonKey()
  final String channelType;

  /// Create a copy of CreateMatchEnterpriseRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CreateMatchEnterpriseRequestCopyWith<_CreateMatchEnterpriseRequest>
      get copyWith => __$CreateMatchEnterpriseRequestCopyWithImpl<
          _CreateMatchEnterpriseRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CreateMatchEnterpriseRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CreateMatchEnterpriseRequest &&
            (identical(other.taskId, taskId) || other.taskId == taskId) &&
            (identical(other.enterpriseId, enterpriseId) ||
                other.enterpriseId == enterpriseId) &&
            (identical(other.provinceName, provinceName) ||
                other.provinceName == provinceName) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.creditPwd, creditPwd) ||
                other.creditPwd == creditPwd) &&
            (identical(other.areaId, areaId) || other.areaId == areaId) &&
            (identical(other.garageStatus, garageStatus) ||
                other.garageStatus == garageStatus) &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.personStand, personStand) ||
                other.personStand == personStand) &&
            (identical(other.channelType, channelType) ||
                other.channelType == channelType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      taskId,
      enterpriseId,
      provinceName,
      creditCode,
      phone,
      creditPwd,
      areaId,
      garageStatus,
      enterpriseName,
      personStand,
      channelType);

  @override
  String toString() {
    return 'CreateMatchEnterpriseRequest(taskId: $taskId, enterpriseId: $enterpriseId, provinceName: $provinceName, creditCode: $creditCode, phone: $phone, creditPwd: $creditPwd, areaId: $areaId, garageStatus: $garageStatus, enterpriseName: $enterpriseName, personStand: $personStand, channelType: $channelType)';
  }
}

/// @nodoc
abstract mixin class _$CreateMatchEnterpriseRequestCopyWith<$Res>
    implements $CreateMatchEnterpriseRequestCopyWith<$Res> {
  factory _$CreateMatchEnterpriseRequestCopyWith(
          _CreateMatchEnterpriseRequest value,
          $Res Function(_CreateMatchEnterpriseRequest) _then) =
      __$CreateMatchEnterpriseRequestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String taskId,
      String enterpriseId,
      String provinceName,
      String creditCode,
      String phone,
      String creditPwd,
      String areaId,
      int garageStatus,
      String enterpriseName,
      String personStand,
      String channelType});
}

/// @nodoc
class __$CreateMatchEnterpriseRequestCopyWithImpl<$Res>
    implements _$CreateMatchEnterpriseRequestCopyWith<$Res> {
  __$CreateMatchEnterpriseRequestCopyWithImpl(this._self, this._then);

  final _CreateMatchEnterpriseRequest _self;
  final $Res Function(_CreateMatchEnterpriseRequest) _then;

  /// Create a copy of CreateMatchEnterpriseRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? taskId = null,
    Object? enterpriseId = null,
    Object? provinceName = null,
    Object? creditCode = null,
    Object? phone = null,
    Object? creditPwd = null,
    Object? areaId = null,
    Object? garageStatus = null,
    Object? enterpriseName = null,
    Object? personStand = null,
    Object? channelType = null,
  }) {
    return _then(_CreateMatchEnterpriseRequest(
      taskId: null == taskId
          ? _self.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String,
      enterpriseId: null == enterpriseId
          ? _self.enterpriseId
          : enterpriseId // ignore: cast_nullable_to_non_nullable
              as String,
      provinceName: null == provinceName
          ? _self.provinceName
          : provinceName // ignore: cast_nullable_to_non_nullable
              as String,
      creditCode: null == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String,
      phone: null == phone
          ? _self.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String,
      creditPwd: null == creditPwd
          ? _self.creditPwd
          : creditPwd // ignore: cast_nullable_to_non_nullable
              as String,
      areaId: null == areaId
          ? _self.areaId
          : areaId // ignore: cast_nullable_to_non_nullable
              as String,
      garageStatus: null == garageStatus
          ? _self.garageStatus
          : garageStatus // ignore: cast_nullable_to_non_nullable
              as int,
      enterpriseName: null == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String,
      personStand: null == personStand
          ? _self.personStand
          : personStand // ignore: cast_nullable_to_non_nullable
              as String,
      channelType: null == channelType
          ? _self.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$CreateMatchEnterpriseResponse {
  String get enterpriseId;
  String? get creditCode;
  String get id;
  int get matchStatus;

  /// Create a copy of CreateMatchEnterpriseResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CreateMatchEnterpriseResponseCopyWith<CreateMatchEnterpriseResponse>
      get copyWith => _$CreateMatchEnterpriseResponseCopyWithImpl<
              CreateMatchEnterpriseResponse>(
          this as CreateMatchEnterpriseResponse, _$identity);

  /// Serializes this CreateMatchEnterpriseResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CreateMatchEnterpriseResponse &&
            (identical(other.enterpriseId, enterpriseId) ||
                other.enterpriseId == enterpriseId) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.matchStatus, matchStatus) ||
                other.matchStatus == matchStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, enterpriseId, creditCode, id, matchStatus);

  @override
  String toString() {
    return 'CreateMatchEnterpriseResponse(enterpriseId: $enterpriseId, creditCode: $creditCode, id: $id, matchStatus: $matchStatus)';
  }
}

/// @nodoc
abstract mixin class $CreateMatchEnterpriseResponseCopyWith<$Res> {
  factory $CreateMatchEnterpriseResponseCopyWith(
          CreateMatchEnterpriseResponse value,
          $Res Function(CreateMatchEnterpriseResponse) _then) =
      _$CreateMatchEnterpriseResponseCopyWithImpl;
  @useResult
  $Res call(
      {String enterpriseId, String? creditCode, String id, int matchStatus});
}

/// @nodoc
class _$CreateMatchEnterpriseResponseCopyWithImpl<$Res>
    implements $CreateMatchEnterpriseResponseCopyWith<$Res> {
  _$CreateMatchEnterpriseResponseCopyWithImpl(this._self, this._then);

  final CreateMatchEnterpriseResponse _self;
  final $Res Function(CreateMatchEnterpriseResponse) _then;

  /// Create a copy of CreateMatchEnterpriseResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enterpriseId = null,
    Object? creditCode = freezed,
    Object? id = null,
    Object? matchStatus = null,
  }) {
    return _then(_self.copyWith(
      enterpriseId: null == enterpriseId
          ? _self.enterpriseId
          : enterpriseId // ignore: cast_nullable_to_non_nullable
              as String,
      creditCode: freezed == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String?,
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      matchStatus: null == matchStatus
          ? _self.matchStatus
          : matchStatus // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _CreateMatchEnterpriseResponse implements CreateMatchEnterpriseResponse {
  const _CreateMatchEnterpriseResponse(
      {required this.enterpriseId,
      this.creditCode,
      required this.id,
      required this.matchStatus});
  factory _CreateMatchEnterpriseResponse.fromJson(Map<String, dynamic> json) =>
      _$CreateMatchEnterpriseResponseFromJson(json);

  @override
  final String enterpriseId;
  @override
  final String? creditCode;
  @override
  final String id;
  @override
  final int matchStatus;

  /// Create a copy of CreateMatchEnterpriseResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CreateMatchEnterpriseResponseCopyWith<_CreateMatchEnterpriseResponse>
      get copyWith => __$CreateMatchEnterpriseResponseCopyWithImpl<
          _CreateMatchEnterpriseResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CreateMatchEnterpriseResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CreateMatchEnterpriseResponse &&
            (identical(other.enterpriseId, enterpriseId) ||
                other.enterpriseId == enterpriseId) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.matchStatus, matchStatus) ||
                other.matchStatus == matchStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, enterpriseId, creditCode, id, matchStatus);

  @override
  String toString() {
    return 'CreateMatchEnterpriseResponse(enterpriseId: $enterpriseId, creditCode: $creditCode, id: $id, matchStatus: $matchStatus)';
  }
}

/// @nodoc
abstract mixin class _$CreateMatchEnterpriseResponseCopyWith<$Res>
    implements $CreateMatchEnterpriseResponseCopyWith<$Res> {
  factory _$CreateMatchEnterpriseResponseCopyWith(
          _CreateMatchEnterpriseResponse value,
          $Res Function(_CreateMatchEnterpriseResponse) _then) =
      __$CreateMatchEnterpriseResponseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String enterpriseId, String? creditCode, String id, int matchStatus});
}

/// @nodoc
class __$CreateMatchEnterpriseResponseCopyWithImpl<$Res>
    implements _$CreateMatchEnterpriseResponseCopyWith<$Res> {
  __$CreateMatchEnterpriseResponseCopyWithImpl(this._self, this._then);

  final _CreateMatchEnterpriseResponse _self;
  final $Res Function(_CreateMatchEnterpriseResponse) _then;

  /// Create a copy of CreateMatchEnterpriseResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? enterpriseId = null,
    Object? creditCode = freezed,
    Object? id = null,
    Object? matchStatus = null,
  }) {
    return _then(_CreateMatchEnterpriseResponse(
      enterpriseId: null == enterpriseId
          ? _self.enterpriseId
          : enterpriseId // ignore: cast_nullable_to_non_nullable
              as String,
      creditCode: freezed == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String?,
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      matchStatus: null == matchStatus
          ? _self.matchStatus
          : matchStatus // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

// dart format on
