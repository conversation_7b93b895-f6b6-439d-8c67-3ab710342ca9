import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:zrreport/common/index.dart';

part 'tax_supplement_models.freezed.dart';
part 'tax_supplement_models.g.dart';

/// 税务补充材料页面状态
@freezed
abstract class TaxSupplementState with _$TaxSupplementState {
  const factory TaxSupplementState({
    /// 公司名称
    @Default('') String enterpriseName,
    /// 纳税人识别号
    @Default('') String creditCode,
    /// 省份ID
    @Default('') String provinceId,
    /// 申请人身份
    @Default('') String personStand,
    /// 省份名称
    @Default('') String provinceName,
    /// 电子税务局账号
    @Default('') String taxAccount,
    /// 税务密码
    @Default('') String taxPassword,
    /// 验证码
    @Default('') String captchaCode,
    /// 是否需要验证码
    @Default(false) bool needsCaptcha,
    /// 是否同意协议·
    @Default(false) bool agreedToTerms,
    /// 验证码倒计时
    @Default(0) int captchaCountdown,
    /// 税务登录模型（需要验证码时使用
    TaxLoginModel? taxLoginModel,


    /// 税务登录模型（不需要验证码时使用
    TaxOnlyAccountLoginModel? taxOnlyAccountLoginModel,
    /// 是否正在加载
    @Default(false) bool isLoading,
    /// 是否显示成功页面
    @Default(false) bool showSuccessPage,
  }) = _TaxSupplementState;

  factory TaxSupplementState.fromJson(Map<String, dynamic> json) =>
      _$TaxSupplementStateFromJson(json);
}

/// 创建匹配企业请求模型
@freezed
abstract class CreateMatchEnterpriseRequest with _$CreateMatchEnterpriseRequest {
  const factory CreateMatchEnterpriseRequest({
    required String taskId,
    required String enterpriseId,
    required String provinceName,
    required String creditCode,
    required String phone,
    required String creditPwd,
    required String areaId,
    @Default(0) int garageStatus,
    required String enterpriseName,
    required String personStand,
    @Default('routine') String channelType,
  }) = _CreateMatchEnterpriseRequest;

  factory CreateMatchEnterpriseRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateMatchEnterpriseRequestFromJson(json);
}

/// 创建匹配企业响应模型
@freezed
abstract class CreateMatchEnterpriseResponse with _$CreateMatchEnterpriseResponse {
  const factory CreateMatchEnterpriseResponse({
    required String enterpriseId,
    String? creditCode,
    required String id,
    required int matchStatus,
  }) = _CreateMatchEnterpriseResponse;

  factory CreateMatchEnterpriseResponse.fromJson(Map<String, dynamic> json) =>
      _$CreateMatchEnterpriseResponseFromJson(json);
}
