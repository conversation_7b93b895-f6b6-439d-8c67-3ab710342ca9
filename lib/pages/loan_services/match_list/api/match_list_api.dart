import 'package:dio/dio.dart';
import 'package:zrreport/common/index.dart';
import '../models/index.dart';

/// 匹配列表API服务
class MatchListApi {
  /// 获取匹配企业列表
  static Future<BaseResponse<Pagination<MatchEnterprise>>> getMatchEnterpriseList({
    required MatchEnterpriseQuery query,
    CancelToken? cancelToken,
  }) async {
    // 构建查询参数
    final params = {
      'pageSize': query.pageSize,
      'pageNum': query.pageNum,
      'enterpriseName': query.enterpriseName,
    };
    
    // 如果指定了匹配步骤，添加到参数中
    if (query.matchStep != null) {
      params['matchStep'] = query.matchStep!;
    }

    final response = await SXHttpService.to.get(
      '/cmsMatchEnterprise',
      params: params,
      cancelToken: cancelToken,
    );

    return BaseResponse.fromJson(
      response.data,
      (data) => Pagination.fromJson(
        data as Map<String, dynamic>,
        (itemData) => MatchEnterprise.fromJson(itemData as Map<String, dynamic>),
      ),
    );
  }
}
