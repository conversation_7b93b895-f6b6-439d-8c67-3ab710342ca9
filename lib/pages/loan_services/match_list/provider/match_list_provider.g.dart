// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'match_list_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$matchListNotifierHash() => r'1a80ed2831330e1f3315e9d6ab1a0c8813dc5961';

/// 匹配列表状态管理
///
/// Copied from [MatchListNotifier].
@ProviderFor(MatchListNotifier)
final matchListNotifierProvider =
    AutoDisposeNotifierProvider<MatchListNotifier, MatchListState>.internal(
  MatchListNotifier.new,
  name: r'matchListNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$matchListNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MatchListNotifier = AutoDisposeNotifier<MatchListState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
