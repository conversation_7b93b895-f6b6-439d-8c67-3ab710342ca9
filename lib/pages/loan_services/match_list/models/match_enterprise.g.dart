// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'match_enterprise.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MatchEnterprise _$MatchEnterpriseFromJson(Map<String, dynamic> json) =>
    _MatchEnterprise(
      id: json['id'] as String,
      type: (json['type'] as num).toInt(),
      enterpriseId: json['enterpriseId'] as String?,
      enterpriseName: json['enterpriseName'] as String,
      addr: json['addr'] as String?,
      legalPerson: json['legalPerson'] as String?,
      creditCode: json['creditCode'] as String?,
      phone: json['phone'] as String?,
      provinceName: json['provinceName'] as String?,
      createTime: json['createTime'] as String?,
      updateTime: json['updateTime'] as String?,
      matchStatus: (json['matchStatus'] as num).toInt(),
      productNum: (json['productNum'] as num).toInt(),
      matchStep: (json['matchStep'] as num).toInt(),
      collect: json['collect'] as bool? ?? false,
    );

Map<String, dynamic> _$MatchEnterpriseToJson(_MatchEnterprise instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'enterpriseId': instance.enterpriseId,
      'enterpriseName': instance.enterpriseName,
      'addr': instance.addr,
      'legalPerson': instance.legalPerson,
      'creditCode': instance.creditCode,
      'phone': instance.phone,
      'provinceName': instance.provinceName,
      'createTime': instance.createTime,
      'updateTime': instance.updateTime,
      'matchStatus': instance.matchStatus,
      'productNum': instance.productNum,
      'matchStep': instance.matchStep,
      'collect': instance.collect,
    };

_MatchEnterpriseQuery _$MatchEnterpriseQueryFromJson(
        Map<String, dynamic> json) =>
    _MatchEnterpriseQuery(
      pageNum: (json['pageNum'] as num?)?.toInt() ?? 1,
      pageSize: (json['pageSize'] as num?)?.toInt() ?? 10,
      enterpriseName: json['enterpriseName'] as String? ?? '',
      matchStep: json['matchStep'] as String?,
    );

Map<String, dynamic> _$MatchEnterpriseQueryToJson(
        _MatchEnterpriseQuery instance) =>
    <String, dynamic>{
      'pageNum': instance.pageNum,
      'pageSize': instance.pageSize,
      'enterpriseName': instance.enterpriseName,
      'matchStep': instance.matchStep,
    };
