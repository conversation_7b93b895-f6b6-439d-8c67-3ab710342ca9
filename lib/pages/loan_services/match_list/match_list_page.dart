import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/index.dart';
import 'models/index.dart';
import 'provider/index.dart';

/// 匹配列表页面
class MatchListPage extends ConsumerStatefulWidget {
  const MatchListPage({super.key});

  @override
  ConsumerState<MatchListPage> createState() => _MatchListPageState();
}

class _MatchListPageState extends ConsumerState<MatchListPage> {
  final TextEditingController _searchController = TextEditingController();
  final EasyRefreshController _refreshController = EasyRefreshController();

  @override
  void initState() {
    super.initState();
    // 页面初始化时加载数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(matchListNotifierProvider.notifier).initialize();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  final blueBackground = AppColors.primary.withAlpha(40);

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(matchListNotifierProvider);
    final notifier = ref.read(matchListNotifierProvider.notifier);

    return KeyboardDismissibleScroll(
      child: Scaffold(
        backgroundColor: Color(0xFFF5F5F5),
        appBar: AppBar(
          title: const Text('匹配列表'),
          backgroundColor: Color(0xFFF5F5F5),
          elevation: 0,
        ),
        body: Column(
          children: [
            // 搜索框
            _buildSearchBar(notifier),
            SizedBox(height: 12),
            // 个人匹配历史标题
            _buildSectionHeader(),
            SizedBox(height: 12),
            // 筛选标签
            _buildFilterTabs(state, notifier),
            SizedBox(height: 12),
            // 列表内容
            Expanded(
              child: _buildContent(state, notifier),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建搜索框
  Widget _buildSearchBar(MatchListNotifier notifier) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0),
      child: StatefulBuilder(builder: (context, _setState) {
        return TextField(
          controller: _searchController,
          decoration: InputDecoration(
            isDense: true,
            hintText: '请输入关键字搜索',
            hintStyle: TextStyle(
                fontSize: 14, color: AppColors.textColor9, height: 20 / 14),
            hintMaxLines: 1,
            fillColor: Colors.white,
            filled: true,
            prefixIcon: Icon(
              Icons.search_sharp,
              color: AppColors.textColor9,
              size: 24,
            ),
            contentPadding:
                EdgeInsetsDirectional.symmetric(horizontal: 24, vertical: 6),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: Colors.transparent, // 边框颜色
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: Colors.transparent, // 边框颜色
              ),
            ),
          
            suffixIcon: _searchController.text.isNotEmpty
                ? GestureDetector(
                    onTap: () {
                      _searchController.text = '';
                      _setState(() {});
                    },
                    child: Icon(
                      Icons.cancel,
                      size: 16,
                      color: AppColors.textColor8,
                    ),
                  )
                : null,
            suffixIconConstraints:
                BoxConstraints.tightFor(width: 60, height: 40),
          ),
          onSubmitted: (value) {
            notifier.updateSearchKeyword(value.trim());
          },
          onChanged: (value) {
            notifier.updateSearchKeyword(value.trim());
            _setState(() {});
          },
        );
      }
      ),
    );
  }

  /// 构建区域标题
  Widget _buildSectionHeader() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Text(
            '个人匹配历史',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textColor1,
            ),
          ),
          Spacer(),
          Icon(
            Icons.info_outline,
            size: 16,
            color: AppColors.textColor9,
          ),
          SizedBox(width: 8),
          Text(
            '只保留最近90天的记录',
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textColor9,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建筛选标签
  Widget _buildFilterTabs(MatchListState state, MatchListNotifier notifier) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: MatchType.values.map((type) {
          final isSelected = state.selectedMatchType == type;
          return GestureDetector(
            onTap: () => notifier.switchMatchType(type),
            child: Container(
              margin: EdgeInsets.only(right: 12),
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 6),
              decoration: BoxDecoration(
                color:
                    isSelected ? blueBackground : Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected ? AppColors.primary : Colors.transparent,
                  width: 1,
                ),
              ),
              child: Text(
                type.displayName,
                style: TextStyle(
                    fontSize: 14,
                    color:
                        isSelected ? AppColors.primary : AppColors.textColor8,
                    fontWeight: isSelected ? FontWeight.bold : null),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 构建主要内容
  Widget _buildContent(MatchListState state, MatchListNotifier notifier) {
    if (state.isLoading) {
      return const LoadingWidget();
    }

    if (state.errorMessage != null) {
      return Center(
        child: ErrorStatusWidget(
          text: state.errorMessage!,
          onAttempt: () => notifier.refresh(),
        ),
      );
    }

    if (state.enterprises.isEmpty) {
      return const Center(
        child: EmptyWidget(text: '暂无匹配记录'),
      );
    }

    return EasyRefresh(
      controller: _refreshController,
      onRefresh: () async {
        await notifier.refresh();
      },
      onLoad: state.hasMore
          ? () async {
              await notifier.loadMore();
            }
          : null,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: state.enterprises.length,
        itemBuilder: (context, index) {
          final enterprise = state.enterprises[index];
          return _buildEnterpriseCard(enterprise);
        },
      ),
    );
  }

  /// 构建企业卡片
  Widget _buildEnterpriseCard(MatchEnterprise enterprise) {
    return GestureDetector(
      onTap: () {
        final params = MatchDetailParams(
          id: enterprise.id,
          type: enterprise.type,
        );

        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ProviderScope(
              child: MatchDetailPage(params: params),
            ),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 企业名称和标签
            Row(
              children: [
                Expanded(
                  child: Text(
                    enterprise.enterpriseName,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textColor1,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: 8),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: AppColors.orange,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '企业',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 12),
            // 时间和匹配类型
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 14,
                  color: AppColors.textColor9,
                ),
                SizedBox(width: 4),
                Text(
                  '${enterprise.createTime}',
                  style: TextStyle(
                      fontSize: 14,
                      color: AppColors.textColor9,
                      fontWeight: FontWeight.normal),
                ),

                Text(
                  ' ${_getMatchTypeText(enterprise.matchStep)}',
                  style: TextStyle(
                      fontSize: 14,
                      color: AppColors.textColor9,
                      fontWeight: FontWeight.w600),
                ),
                Spacer(),
                // 产品数量按钮
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.primary),
                    borderRadius: BorderRadius.circular(12),
                    color: blueBackground,
                  ),
                  child: Text(
                    '可做产品${enterprise.productNum}',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 获取匹配类型文本
  String _getMatchTypeText(int matchStep) {
    switch (matchStep) {
      case 0:
        return '答题匹配';
      case 1:
        return '税务匹配';
      default:
        return '匹配';
    }
  }
}
