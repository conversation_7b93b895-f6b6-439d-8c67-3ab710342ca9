// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loan_detail_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$loanDetailNotifierHash() =>
    r'd0cb1081745fee09dd08b4952d62616f5146b598';

/// 贷款商品详情页面状态管理
///
/// Copied from [LoanDetailNotifier].
@ProviderFor(LoanDetailNotifier)
final loanDetailNotifierProvider =
    AutoDisposeNotifierProvider<LoanDetailNotifier, LoanDetailState>.internal(
  LoanDetailNotifier.new,
  name: r'loanDetailNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$loanDetailNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LoanDetailNotifier = AutoDisposeNotifier<LoanDetailState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
