// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'loan_detail_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LoanDetailState {
  /// 是否正在加载
  bool get isLoading;

  /// 贷款商品详情数据
  ProductDetailModel? get loanDetail;

  /// 错误信息
  String? get error;

  /// 导航栏是否展开
  bool get isNavigationExpanded;

  /// 当前选中的导航项
  String get selectedNavigation;

  /// 导航项列表
  List<NavigationItem> get navigationItems;

  /// Create a copy of LoanDetailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LoanDetailStateCopyWith<LoanDetailState> get copyWith =>
      _$LoanDetailStateCopyWithImpl<LoanDetailState>(
          this as LoanDetailState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LoanDetailState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.loanDetail, loanDetail) ||
                other.loanDetail == loanDetail) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.isNavigationExpanded, isNavigationExpanded) ||
                other.isNavigationExpanded == isNavigationExpanded) &&
            (identical(other.selectedNavigation, selectedNavigation) ||
                other.selectedNavigation == selectedNavigation) &&
            const DeepCollectionEquality()
                .equals(other.navigationItems, navigationItems));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      loanDetail,
      error,
      isNavigationExpanded,
      selectedNavigation,
      const DeepCollectionEquality().hash(navigationItems));

  @override
  String toString() {
    return 'LoanDetailState(isLoading: $isLoading, loanDetail: $loanDetail, error: $error, isNavigationExpanded: $isNavigationExpanded, selectedNavigation: $selectedNavigation, navigationItems: $navigationItems)';
  }
}

/// @nodoc
abstract mixin class $LoanDetailStateCopyWith<$Res> {
  factory $LoanDetailStateCopyWith(
          LoanDetailState value, $Res Function(LoanDetailState) _then) =
      _$LoanDetailStateCopyWithImpl;
  @useResult
  $Res call(
      {bool isLoading,
      ProductDetailModel? loanDetail,
      String? error,
      bool isNavigationExpanded,
      String selectedNavigation,
      List<NavigationItem> navigationItems});

  $ProductDetailModelCopyWith<$Res>? get loanDetail;
}

/// @nodoc
class _$LoanDetailStateCopyWithImpl<$Res>
    implements $LoanDetailStateCopyWith<$Res> {
  _$LoanDetailStateCopyWithImpl(this._self, this._then);

  final LoanDetailState _self;
  final $Res Function(LoanDetailState) _then;

  /// Create a copy of LoanDetailState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? loanDetail = freezed,
    Object? error = freezed,
    Object? isNavigationExpanded = null,
    Object? selectedNavigation = null,
    Object? navigationItems = null,
  }) {
    return _then(_self.copyWith(
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      loanDetail: freezed == loanDetail
          ? _self.loanDetail
          : loanDetail // ignore: cast_nullable_to_non_nullable
              as ProductDetailModel?,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      isNavigationExpanded: null == isNavigationExpanded
          ? _self.isNavigationExpanded
          : isNavigationExpanded // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedNavigation: null == selectedNavigation
          ? _self.selectedNavigation
          : selectedNavigation // ignore: cast_nullable_to_non_nullable
              as String,
      navigationItems: null == navigationItems
          ? _self.navigationItems
          : navigationItems // ignore: cast_nullable_to_non_nullable
              as List<NavigationItem>,
    ));
  }

  /// Create a copy of LoanDetailState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProductDetailModelCopyWith<$Res>? get loanDetail {
    if (_self.loanDetail == null) {
      return null;
    }

    return $ProductDetailModelCopyWith<$Res>(_self.loanDetail!, (value) {
      return _then(_self.copyWith(loanDetail: value));
    });
  }
}

/// @nodoc

class _LoanDetailState implements LoanDetailState {
  const _LoanDetailState(
      {this.isLoading = false,
      this.loanDetail,
      this.error,
      this.isNavigationExpanded = true,
      this.selectedNavigation = '个人要求',
      final List<NavigationItem> navigationItems = const [
        NavigationItem(title: '个人要求', key: 'personal', isSelected: true),
        NavigationItem(title: '企业要求', key: 'company'),
        NavigationItem(title: '征信要求', key: 'credit'),
        NavigationItem(title: '准入地区', key: 'area'),
        NavigationItem(title: '禁入行业', key: 'industry')
      ]})
      : _navigationItems = navigationItems;

  /// 是否正在加载
  @override
  @JsonKey()
  final bool isLoading;

  /// 贷款商品详情数据
  @override
  final ProductDetailModel? loanDetail;

  /// 错误信息
  @override
  final String? error;

  /// 导航栏是否展开
  @override
  @JsonKey()
  final bool isNavigationExpanded;

  /// 当前选中的导航项
  @override
  @JsonKey()
  final String selectedNavigation;

  /// 导航项列表
  final List<NavigationItem> _navigationItems;

  /// 导航项列表
  @override
  @JsonKey()
  List<NavigationItem> get navigationItems {
    if (_navigationItems is EqualUnmodifiableListView) return _navigationItems;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_navigationItems);
  }

  /// Create a copy of LoanDetailState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoanDetailStateCopyWith<_LoanDetailState> get copyWith =>
      __$LoanDetailStateCopyWithImpl<_LoanDetailState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LoanDetailState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.loanDetail, loanDetail) ||
                other.loanDetail == loanDetail) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.isNavigationExpanded, isNavigationExpanded) ||
                other.isNavigationExpanded == isNavigationExpanded) &&
            (identical(other.selectedNavigation, selectedNavigation) ||
                other.selectedNavigation == selectedNavigation) &&
            const DeepCollectionEquality()
                .equals(other._navigationItems, _navigationItems));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      loanDetail,
      error,
      isNavigationExpanded,
      selectedNavigation,
      const DeepCollectionEquality().hash(_navigationItems));

  @override
  String toString() {
    return 'LoanDetailState(isLoading: $isLoading, loanDetail: $loanDetail, error: $error, isNavigationExpanded: $isNavigationExpanded, selectedNavigation: $selectedNavigation, navigationItems: $navigationItems)';
  }
}

/// @nodoc
abstract mixin class _$LoanDetailStateCopyWith<$Res>
    implements $LoanDetailStateCopyWith<$Res> {
  factory _$LoanDetailStateCopyWith(
          _LoanDetailState value, $Res Function(_LoanDetailState) _then) =
      __$LoanDetailStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      ProductDetailModel? loanDetail,
      String? error,
      bool isNavigationExpanded,
      String selectedNavigation,
      List<NavigationItem> navigationItems});

  @override
  $ProductDetailModelCopyWith<$Res>? get loanDetail;
}

/// @nodoc
class __$LoanDetailStateCopyWithImpl<$Res>
    implements _$LoanDetailStateCopyWith<$Res> {
  __$LoanDetailStateCopyWithImpl(this._self, this._then);

  final _LoanDetailState _self;
  final $Res Function(_LoanDetailState) _then;

  /// Create a copy of LoanDetailState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isLoading = null,
    Object? loanDetail = freezed,
    Object? error = freezed,
    Object? isNavigationExpanded = null,
    Object? selectedNavigation = null,
    Object? navigationItems = null,
  }) {
    return _then(_LoanDetailState(
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      loanDetail: freezed == loanDetail
          ? _self.loanDetail
          : loanDetail // ignore: cast_nullable_to_non_nullable
              as ProductDetailModel?,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      isNavigationExpanded: null == isNavigationExpanded
          ? _self.isNavigationExpanded
          : isNavigationExpanded // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedNavigation: null == selectedNavigation
          ? _self.selectedNavigation
          : selectedNavigation // ignore: cast_nullable_to_non_nullable
              as String,
      navigationItems: null == navigationItems
          ? _self._navigationItems
          : navigationItems // ignore: cast_nullable_to_non_nullable
              as List<NavigationItem>,
    ));
  }

  /// Create a copy of LoanDetailState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProductDetailModelCopyWith<$Res>? get loanDetail {
    if (_self.loanDetail == null) {
      return null;
    }

    return $ProductDetailModelCopyWith<$Res>(_self.loanDetail!, (value) {
      return _then(_self.copyWith(loanDetail: value));
    });
  }
}

// dart format on
