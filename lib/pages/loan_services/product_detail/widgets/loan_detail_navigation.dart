import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';
import '../models/product_detail_models.dart';

/// 贷款商品详情导航栏组件
class LoanDetailNavigation extends StatelessWidget {
  final bool isExpanded;
  final List<NavigationItem> navigationItems;
  final VoidCallback onToggle;
  final Function(String) onNavigationTap;

  const LoanDetailNavigation({
    super.key,
    required this.isExpanded,
    required this.navigationItems,
    required this.onToggle,
    required this.onNavigationTap,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: isExpanded ? 120 : 0,
      decoration: BoxDecoration(
        color: Colors.white,
        border: isExpanded ? const Border(
          right: BorderSide(color: AppColors.dividerColor, width: 0.5),
        ) : null,
      ),
      child: isExpanded ? Column(
        children: [
          // 导航项列表（可滚动）
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: _buildNavigationItems(),
              ),
            ),
          ),
        ],
      ) : null,
    );
  }



  /// 构建导航项列表
  List<Widget> _buildNavigationItems() {
    return navigationItems.map((item) {
      return _buildNavigationItem(item);
    }).toList();
  }

  /// 构建单个导航项
  Widget _buildNavigationItem(NavigationItem item) {
    final isSelected = item.isSelected;
    
    return Container(
      decoration: BoxDecoration(
        color: isSelected ? AppColors.primary.withValues(alpha: 0.1) : Colors.transparent,
        border: const Border(
          bottom: BorderSide(color: AppColors.dividerColor, width: 0.5),
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => onNavigationTap(item.key),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            child: Text(
              item.title,
              style: TextStyle(
                color: isSelected ? AppColors.primary : AppColors.textColor6,
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }
}
