import 'package:flutter/material.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:zrreport/common/index.dart';
import '../provider/loan_detail_state.dart';

/// 使用ScrollablePositionedList的贷款商品详情内容组件
class LoanDetailScrollableContent extends StatefulWidget {
  const LoanDetailScrollableContent({
    super.key,
    required this.state,
    required this.itemScrollController,
    required this.itemPositionsListener,
    required this.onItemPositionsChanged,
  });

  final LoanDetailState state;
  final ItemScrollController itemScrollController;
  final ItemPositionsListener itemPositionsListener;
  final VoidCallback onItemPositionsChanged;

  @override
  State<LoanDetailScrollableContent> createState() =>
      _LoanDetailScrollableContentState();
}

class _LoanDetailScrollableContentState
    extends State<LoanDetailScrollableContent> {
  @override
  void initState() {
    super.initState();
    // 添加位置监听器
    widget.itemPositionsListener.itemPositions
        .addListener(widget.onItemPositionsChanged);
  }

  @override
  void dispose() {
    // 移除位置监听器
    widget.itemPositionsListener.itemPositions
        .removeListener(widget.onItemPositionsChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final sectionItems = widget.state.sectionItems;

    if (sectionItems.isEmpty) {
      return const Center(
        child: Text(
          '暂无数据',
          style: TextStyle(
            color: AppColors.textColor9,
            fontSize: 14,
          ),
        ),
      );
    }

    return ScrollablePositionedList.builder(
      itemCount: sectionItems.length,
      itemBuilder: (context, index) => _buildSectionItem(sectionItems[index]),
      itemScrollController: widget.itemScrollController,
      itemPositionsListener: widget.itemPositionsListener,
      padding: const EdgeInsets.all(16),
    );
  }

  /// 构建section项目
  Widget _buildSectionItem(SectionItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 区块标题
          Row(
            children: [
              Container(
                width: 4,
                height: 16,
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                item.title,
                style: const TextStyle(
                  color: AppColors.textColor1,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 文本内容
          _buildTextContent(item.content),
        ],
      ),
    );
  }

  /// 构建文本内容
  Widget _buildTextContent(String content) {
    if (content.isEmpty) {
      return SizedBox();
    }
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        content,
        style: const TextStyle(
          color: AppColors.textColor6,
          fontSize: 14,
          height: 1.5,
        ),
      ),
    );
  }
}
