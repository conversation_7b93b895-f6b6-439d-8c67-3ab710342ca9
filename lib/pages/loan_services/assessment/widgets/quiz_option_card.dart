import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';
import '../models/question.dart';

/// 答题选项卡片组件
class QuizOptionCard extends StatelessWidget {
  final QuestionOption option;
  final bool isSelected;
  final VoidCallback onTap;
  final bool isMultiChoice; // 是否为多选题

  const QuizOptionCard({
    super.key,
    required this.option,
    required this.isSelected,
    required this.onTap,
    this.isMultiChoice = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : AppColors.disableBackground,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.03),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            const SizedBox(width: 12),
            // 选项文本
            Expanded(
              child: Text(
                option.label,
                style: TextStyle(
                  fontSize: 16,
                  color: isSelected ? Colors.white : AppColors.textColor1,
                  fontWeight: FontWeight.w600,
                  height: 1.4,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
