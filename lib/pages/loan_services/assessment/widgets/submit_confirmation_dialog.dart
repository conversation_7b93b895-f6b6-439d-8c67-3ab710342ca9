import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';

/// 提交确认对话框
class SubmitConfirmationDialog extends StatelessWidget {
  final VoidCallback onSubmit;
  final VoidCallback? onCancel;

  const SubmitConfirmationDialog({
    super.key,
    required this.onSubmit,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 320,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 关闭按钮
            Align(
              alignment: Alignment.topRight,
              child: GestureDetector(
                onTap: onCancel ?? () => Navigator.of(context).pop(),
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.grey,
                    size: 20,
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 标题
            const Text(
              '提交',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: AppColors.textColor1,
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 插图
            SizedBox(
              width: 120,
              height: 120,
              child: Image.asset(
                AssetsImages.querySuccessPng,
                fit: BoxFit.contain,
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 提示文本
            const Text(
              '最后一道题答完啦，点击提交上传吧~',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textColor3,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 32),
            
            // 提交按钮
            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onSubmit();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                  elevation: 0,
                ),
                child: const Text(
                  '提交',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示提交确认对话框
  static Future<void> show(
    BuildContext context, {
    required VoidCallback onSubmit,
    VoidCallback? onCancel,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => SubmitConfirmationDialog(
        onSubmit: onSubmit,
        onCancel: onCancel,
      ),
    );
  }
}
