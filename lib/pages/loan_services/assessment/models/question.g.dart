// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'question.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_QuestionOption _$QuestionOptionFromJson(Map<String, dynamic> json) =>
    _QuestionOption(
      id: json['id'] as String? ?? '',
      questionId: json['questionId'] as String? ?? '',
      nextQuestionId: json['nextQuestionId'] as String? ?? '',
      nextQuestionLabel: json['nextQuestionLabel'] as String?,
      label: json['label'] as String? ?? '',
      value: json['value'] as String? ?? '',
      selectStatus: json['selectStatus'] as bool? ?? false,
    );

Map<String, dynamic> _$QuestionOptionToJson(_QuestionOption instance) =>
    <String, dynamic>{
      'id': instance.id,
      'questionId': instance.questionId,
      'nextQuestionId': instance.nextQuestionId,
      'nextQuestionLabel': instance.nextQuestionLabel,
      'label': instance.label,
      'value': instance.value,
      'selectStatus': instance.selectStatus,
    };

_Question _$QuestionFromJson(Map<String, dynamic> json) => _Question(
      id: json['id'] as String? ?? '',
      categoryId: json['categoryId'] as String? ?? '',
      matchType: json['matchType'] as String? ?? '',
      conditions: json['conditions'] as String? ?? '',
      conditionsName: json['conditionsName'] as String?,
      metaId: json['metaId'] as String? ?? '',
      publishStatus: (json['publishStatus'] as num?)?.toInt() ?? 0,
      label: json['label'] as String? ?? '',
      type: (json['type'] as num?)?.toInt() ?? 0,
      must: (json['must'] as num?)?.toInt() ?? 0,
      note: json['note'] as String? ?? '',
      createBy: json['createBy'] as String? ?? '',
      createTime: json['createTime'] as String? ?? '',
      options: (json['options'] as List<dynamic>?)
              ?.map((e) => QuestionOption.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      answers: json['answers'] as List<dynamic>? ?? const [],
    );

Map<String, dynamic> _$QuestionToJson(_Question instance) => <String, dynamic>{
      'id': instance.id,
      'categoryId': instance.categoryId,
      'matchType': instance.matchType,
      'conditions': instance.conditions,
      'conditionsName': instance.conditionsName,
      'metaId': instance.metaId,
      'publishStatus': instance.publishStatus,
      'label': instance.label,
      'type': instance.type,
      'must': instance.must,
      'note': instance.note,
      'createBy': instance.createBy,
      'createTime': instance.createTime,
      'options': instance.options,
      'answers': instance.answers,
    };
