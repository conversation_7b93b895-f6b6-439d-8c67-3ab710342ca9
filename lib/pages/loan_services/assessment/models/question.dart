import 'package:freezed_annotation/freezed_annotation.dart';

part 'question.freezed.dart';
part 'question.g.dart';

/// 问题选项模型
@freezed
abstract class QuestionOption with _$QuestionOption {
  const factory QuestionOption({
    /// 选项ID
    @Default('') String id,

    /// 问题ID
    @Default('') String questionId,

    /// 下一题ID
    @Default('') String nextQuestionId,

    /// 下一题标签
    String? nextQuestionLabel,

    /// 选项标签
    @Default('') String label,

    /// 选项值
    @Default('') String value,

    /// 选择状态
    @Default(false) bool selectStatus,
  }) = _QuestionOption;

  factory QuestionOption.fromJson(Map<String, dynamic> json) =>
      _$QuestionOptionFromJson(json);
}

/// 问题模型
@freezed
abstract class Question with _$Question {
  const factory Question({
    /// 问题ID
    @Default('') String id,

    /// 分类ID
    @Default('') String categoryId,

    /// 匹配类型
    @Default('') String matchType,

    /// 条件
    @Default('') String conditions,

    /// 条件名称
    String? conditionsName,

    /// 元数据ID
    @Default('') String metaId,

    /// 发布状态
    @Default(0) int publishStatus,

    /// 问题标签
    @Default('') String label,

    /// 问题类型 2->单选，3->多选
    @Default(0) int type,

    /// 是否必须,0->否，1->是
    @Default(0) int must,

    /// 备注
    @Default('') String note,

    /// 创建者
    @Default('') String createBy,

    /// 创建时间
    @Default('') String createTime,

    /// 选项列表
    @Default([]) List<QuestionOption> options,

    /// 答案列表
    @Default([]) List<dynamic> answers,
  }) = _Question;

  factory Question.fromJson(Map<String, dynamic> json) =>
      _$QuestionFromJson(json);
}

/// Question扩展方法
extension QuestionExtension on Question {
  /// 是否为单选题
  bool get isSingleChoice => type == 2;

  /// 是否为多选题
  bool get isMultipleChoice => type == 3;

  /// 是否为必答题
  bool get isRequired => must == 1;

  /// 是否为选答题
  bool get isOptional => must == 0;

  /// 获取题目类型显示文本
  String get typeDisplayText => isSingleChoice ? '单选' : '多选';

  /// 获取必答状态显示文本
  String get requiredDisplayText => isRequired ? '必答' : '选答';
}
