// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'answer_sheet.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SheetVo {
  /// 选项ID
  String get optionsId;

  /// 选项值
  String get value;

  /// Create a copy of SheetVo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SheetVoCopyWith<SheetVo> get copyWith =>
      _$SheetVoCopyWithImpl<SheetVo>(this as SheetVo, _$identity);

  /// Serializes this SheetVo to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SheetVo &&
            (identical(other.optionsId, optionsId) ||
                other.optionsId == optionsId) &&
            (identical(other.value, value) || other.value == value));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, optionsId, value);

  @override
  String toString() {
    return 'SheetVo(optionsId: $optionsId, value: $value)';
  }
}

/// @nodoc
abstract mixin class $SheetVoCopyWith<$Res> {
  factory $SheetVoCopyWith(SheetVo value, $Res Function(SheetVo) _then) =
      _$SheetVoCopyWithImpl;
  @useResult
  $Res call({String optionsId, String value});
}

/// @nodoc
class _$SheetVoCopyWithImpl<$Res> implements $SheetVoCopyWith<$Res> {
  _$SheetVoCopyWithImpl(this._self, this._then);

  final SheetVo _self;
  final $Res Function(SheetVo) _then;

  /// Create a copy of SheetVo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? optionsId = null,
    Object? value = null,
  }) {
    return _then(_self.copyWith(
      optionsId: null == optionsId
          ? _self.optionsId
          : optionsId // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _SheetVo implements SheetVo {
  const _SheetVo({this.optionsId = '', this.value = ''});
  factory _SheetVo.fromJson(Map<String, dynamic> json) =>
      _$SheetVoFromJson(json);

  /// 选项ID
  @override
  @JsonKey()
  final String optionsId;

  /// 选项值
  @override
  @JsonKey()
  final String value;

  /// Create a copy of SheetVo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SheetVoCopyWith<_SheetVo> get copyWith =>
      __$SheetVoCopyWithImpl<_SheetVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SheetVoToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SheetVo &&
            (identical(other.optionsId, optionsId) ||
                other.optionsId == optionsId) &&
            (identical(other.value, value) || other.value == value));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, optionsId, value);

  @override
  String toString() {
    return 'SheetVo(optionsId: $optionsId, value: $value)';
  }
}

/// @nodoc
abstract mixin class _$SheetVoCopyWith<$Res> implements $SheetVoCopyWith<$Res> {
  factory _$SheetVoCopyWith(_SheetVo value, $Res Function(_SheetVo) _then) =
      __$SheetVoCopyWithImpl;
  @override
  @useResult
  $Res call({String optionsId, String value});
}

/// @nodoc
class __$SheetVoCopyWithImpl<$Res> implements _$SheetVoCopyWith<$Res> {
  __$SheetVoCopyWithImpl(this._self, this._then);

  final _SheetVo _self;
  final $Res Function(_SheetVo) _then;

  /// Create a copy of SheetVo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? optionsId = null,
    Object? value = null,
  }) {
    return _then(_SheetVo(
      optionsId: null == optionsId
          ? _self.optionsId
          : optionsId // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$AnswerSheet {
  /// 条件
  String get conditions;

  /// 问题类型
  int get questionType;

  /// 问题ID
  String get questionId;

  /// 答案选项列表
  List<SheetVo> get sheetVos;

  /// Create a copy of AnswerSheet
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AnswerSheetCopyWith<AnswerSheet> get copyWith =>
      _$AnswerSheetCopyWithImpl<AnswerSheet>(this as AnswerSheet, _$identity);

  /// Serializes this AnswerSheet to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AnswerSheet &&
            (identical(other.conditions, conditions) ||
                other.conditions == conditions) &&
            (identical(other.questionType, questionType) ||
                other.questionType == questionType) &&
            (identical(other.questionId, questionId) ||
                other.questionId == questionId) &&
            const DeepCollectionEquality().equals(other.sheetVos, sheetVos));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, conditions, questionType,
      questionId, const DeepCollectionEquality().hash(sheetVos));

  @override
  String toString() {
    return 'AnswerSheet(conditions: $conditions, questionType: $questionType, questionId: $questionId, sheetVos: $sheetVos)';
  }
}

/// @nodoc
abstract mixin class $AnswerSheetCopyWith<$Res> {
  factory $AnswerSheetCopyWith(
          AnswerSheet value, $Res Function(AnswerSheet) _then) =
      _$AnswerSheetCopyWithImpl;
  @useResult
  $Res call(
      {String conditions,
      int questionType,
      String questionId,
      List<SheetVo> sheetVos});
}

/// @nodoc
class _$AnswerSheetCopyWithImpl<$Res> implements $AnswerSheetCopyWith<$Res> {
  _$AnswerSheetCopyWithImpl(this._self, this._then);

  final AnswerSheet _self;
  final $Res Function(AnswerSheet) _then;

  /// Create a copy of AnswerSheet
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? conditions = null,
    Object? questionType = null,
    Object? questionId = null,
    Object? sheetVos = null,
  }) {
    return _then(_self.copyWith(
      conditions: null == conditions
          ? _self.conditions
          : conditions // ignore: cast_nullable_to_non_nullable
              as String,
      questionType: null == questionType
          ? _self.questionType
          : questionType // ignore: cast_nullable_to_non_nullable
              as int,
      questionId: null == questionId
          ? _self.questionId
          : questionId // ignore: cast_nullable_to_non_nullable
              as String,
      sheetVos: null == sheetVos
          ? _self.sheetVos
          : sheetVos // ignore: cast_nullable_to_non_nullable
              as List<SheetVo>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _AnswerSheet implements AnswerSheet {
  const _AnswerSheet(
      {this.conditions = '',
      this.questionType = 0,
      this.questionId = '',
      final List<SheetVo> sheetVos = const []})
      : _sheetVos = sheetVos;
  factory _AnswerSheet.fromJson(Map<String, dynamic> json) =>
      _$AnswerSheetFromJson(json);

  /// 条件
  @override
  @JsonKey()
  final String conditions;

  /// 问题类型
  @override
  @JsonKey()
  final int questionType;

  /// 问题ID
  @override
  @JsonKey()
  final String questionId;

  /// 答案选项列表
  final List<SheetVo> _sheetVos;

  /// 答案选项列表
  @override
  @JsonKey()
  List<SheetVo> get sheetVos {
    if (_sheetVos is EqualUnmodifiableListView) return _sheetVos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_sheetVos);
  }

  /// Create a copy of AnswerSheet
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AnswerSheetCopyWith<_AnswerSheet> get copyWith =>
      __$AnswerSheetCopyWithImpl<_AnswerSheet>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AnswerSheetToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AnswerSheet &&
            (identical(other.conditions, conditions) ||
                other.conditions == conditions) &&
            (identical(other.questionType, questionType) ||
                other.questionType == questionType) &&
            (identical(other.questionId, questionId) ||
                other.questionId == questionId) &&
            const DeepCollectionEquality().equals(other._sheetVos, _sheetVos));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, conditions, questionType,
      questionId, const DeepCollectionEquality().hash(_sheetVos));

  @override
  String toString() {
    return 'AnswerSheet(conditions: $conditions, questionType: $questionType, questionId: $questionId, sheetVos: $sheetVos)';
  }
}

/// @nodoc
abstract mixin class _$AnswerSheetCopyWith<$Res>
    implements $AnswerSheetCopyWith<$Res> {
  factory _$AnswerSheetCopyWith(
          _AnswerSheet value, $Res Function(_AnswerSheet) _then) =
      __$AnswerSheetCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String conditions,
      int questionType,
      String questionId,
      List<SheetVo> sheetVos});
}

/// @nodoc
class __$AnswerSheetCopyWithImpl<$Res> implements _$AnswerSheetCopyWith<$Res> {
  __$AnswerSheetCopyWithImpl(this._self, this._then);

  final _AnswerSheet _self;
  final $Res Function(_AnswerSheet) _then;

  /// Create a copy of AnswerSheet
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? conditions = null,
    Object? questionType = null,
    Object? questionId = null,
    Object? sheetVos = null,
  }) {
    return _then(_AnswerSheet(
      conditions: null == conditions
          ? _self.conditions
          : conditions // ignore: cast_nullable_to_non_nullable
              as String,
      questionType: null == questionType
          ? _self.questionType
          : questionType // ignore: cast_nullable_to_non_nullable
              as int,
      questionId: null == questionId
          ? _self.questionId
          : questionId // ignore: cast_nullable_to_non_nullable
              as String,
      sheetVos: null == sheetVos
          ? _self._sheetVos
          : sheetVos // ignore: cast_nullable_to_non_nullable
              as List<SheetVo>,
    ));
  }
}

/// @nodoc
mixin _$QuizSubmitRequest {
  /// 匹配ID
  String get matchId;

  /// 匹配类型
  String get matchType;

  /// 答题表单列表
  List<AnswerSheet> get answerSheetList;

  /// Create a copy of QuizSubmitRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QuizSubmitRequestCopyWith<QuizSubmitRequest> get copyWith =>
      _$QuizSubmitRequestCopyWithImpl<QuizSubmitRequest>(
          this as QuizSubmitRequest, _$identity);

  /// Serializes this QuizSubmitRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is QuizSubmitRequest &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.matchType, matchType) ||
                other.matchType == matchType) &&
            const DeepCollectionEquality()
                .equals(other.answerSheetList, answerSheetList));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchId, matchType,
      const DeepCollectionEquality().hash(answerSheetList));

  @override
  String toString() {
    return 'QuizSubmitRequest(matchId: $matchId, matchType: $matchType, answerSheetList: $answerSheetList)';
  }
}

/// @nodoc
abstract mixin class $QuizSubmitRequestCopyWith<$Res> {
  factory $QuizSubmitRequestCopyWith(
          QuizSubmitRequest value, $Res Function(QuizSubmitRequest) _then) =
      _$QuizSubmitRequestCopyWithImpl;
  @useResult
  $Res call(
      {String matchId, String matchType, List<AnswerSheet> answerSheetList});
}

/// @nodoc
class _$QuizSubmitRequestCopyWithImpl<$Res>
    implements $QuizSubmitRequestCopyWith<$Res> {
  _$QuizSubmitRequestCopyWithImpl(this._self, this._then);

  final QuizSubmitRequest _self;
  final $Res Function(QuizSubmitRequest) _then;

  /// Create a copy of QuizSubmitRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? matchId = null,
    Object? matchType = null,
    Object? answerSheetList = null,
  }) {
    return _then(_self.copyWith(
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String,
      matchType: null == matchType
          ? _self.matchType
          : matchType // ignore: cast_nullable_to_non_nullable
              as String,
      answerSheetList: null == answerSheetList
          ? _self.answerSheetList
          : answerSheetList // ignore: cast_nullable_to_non_nullable
              as List<AnswerSheet>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _QuizSubmitRequest implements QuizSubmitRequest {
  const _QuizSubmitRequest(
      {this.matchId = '',
      this.matchType = '',
      final List<AnswerSheet> answerSheetList = const []})
      : _answerSheetList = answerSheetList;
  factory _QuizSubmitRequest.fromJson(Map<String, dynamic> json) =>
      _$QuizSubmitRequestFromJson(json);

  /// 匹配ID
  @override
  @JsonKey()
  final String matchId;

  /// 匹配类型
  @override
  @JsonKey()
  final String matchType;

  /// 答题表单列表
  final List<AnswerSheet> _answerSheetList;

  /// 答题表单列表
  @override
  @JsonKey()
  List<AnswerSheet> get answerSheetList {
    if (_answerSheetList is EqualUnmodifiableListView) return _answerSheetList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_answerSheetList);
  }

  /// Create a copy of QuizSubmitRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$QuizSubmitRequestCopyWith<_QuizSubmitRequest> get copyWith =>
      __$QuizSubmitRequestCopyWithImpl<_QuizSubmitRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$QuizSubmitRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _QuizSubmitRequest &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.matchType, matchType) ||
                other.matchType == matchType) &&
            const DeepCollectionEquality()
                .equals(other._answerSheetList, _answerSheetList));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchId, matchType,
      const DeepCollectionEquality().hash(_answerSheetList));

  @override
  String toString() {
    return 'QuizSubmitRequest(matchId: $matchId, matchType: $matchType, answerSheetList: $answerSheetList)';
  }
}

/// @nodoc
abstract mixin class _$QuizSubmitRequestCopyWith<$Res>
    implements $QuizSubmitRequestCopyWith<$Res> {
  factory _$QuizSubmitRequestCopyWith(
          _QuizSubmitRequest value, $Res Function(_QuizSubmitRequest) _then) =
      __$QuizSubmitRequestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String matchId, String matchType, List<AnswerSheet> answerSheetList});
}

/// @nodoc
class __$QuizSubmitRequestCopyWithImpl<$Res>
    implements _$QuizSubmitRequestCopyWith<$Res> {
  __$QuizSubmitRequestCopyWithImpl(this._self, this._then);

  final _QuizSubmitRequest _self;
  final $Res Function(_QuizSubmitRequest) _then;

  /// Create a copy of QuizSubmitRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? matchId = null,
    Object? matchType = null,
    Object? answerSheetList = null,
  }) {
    return _then(_QuizSubmitRequest(
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String,
      matchType: null == matchType
          ? _self.matchType
          : matchType // ignore: cast_nullable_to_non_nullable
              as String,
      answerSheetList: null == answerSheetList
          ? _self._answerSheetList
          : answerSheetList // ignore: cast_nullable_to_non_nullable
              as List<AnswerSheet>,
    ));
  }
}

// dart format on
