// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'quiz_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$QuizState {
  /// 当前问题
  Question? get currentQuestion;

  /// 选中的选项列表
  List<QuestionOption> get selectedOptions;

  /// 是否加载中
  bool get isLoading;

  /// 错误信息
  String? get error;

  /// 是否提交中
  bool get isSubmitting;

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QuizStateCopyWith<QuizState> get copyWith =>
      _$QuizStateCopyWithImpl<QuizState>(this as QuizState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is QuizState &&
            (identical(other.currentQuestion, currentQuestion) ||
                other.currentQuestion == currentQuestion) &&
            const DeepCollectionEquality()
                .equals(other.selectedOptions, selectedOptions) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.isSubmitting, isSubmitting) ||
                other.isSubmitting == isSubmitting));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      currentQuestion,
      const DeepCollectionEquality().hash(selectedOptions),
      isLoading,
      error,
      isSubmitting);

  @override
  String toString() {
    return 'QuizState(currentQuestion: $currentQuestion, selectedOptions: $selectedOptions, isLoading: $isLoading, error: $error, isSubmitting: $isSubmitting)';
  }
}

/// @nodoc
abstract mixin class $QuizStateCopyWith<$Res> {
  factory $QuizStateCopyWith(QuizState value, $Res Function(QuizState) _then) =
      _$QuizStateCopyWithImpl;
  @useResult
  $Res call(
      {Question? currentQuestion,
      List<QuestionOption> selectedOptions,
      bool isLoading,
      String? error,
      bool isSubmitting});

  $QuestionCopyWith<$Res>? get currentQuestion;
}

/// @nodoc
class _$QuizStateCopyWithImpl<$Res> implements $QuizStateCopyWith<$Res> {
  _$QuizStateCopyWithImpl(this._self, this._then);

  final QuizState _self;
  final $Res Function(QuizState) _then;

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentQuestion = freezed,
    Object? selectedOptions = null,
    Object? isLoading = null,
    Object? error = freezed,
    Object? isSubmitting = null,
  }) {
    return _then(_self.copyWith(
      currentQuestion: freezed == currentQuestion
          ? _self.currentQuestion
          : currentQuestion // ignore: cast_nullable_to_non_nullable
              as Question?,
      selectedOptions: null == selectedOptions
          ? _self.selectedOptions
          : selectedOptions // ignore: cast_nullable_to_non_nullable
              as List<QuestionOption>,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      isSubmitting: null == isSubmitting
          ? _self.isSubmitting
          : isSubmitting // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuestionCopyWith<$Res>? get currentQuestion {
    if (_self.currentQuestion == null) {
      return null;
    }

    return $QuestionCopyWith<$Res>(_self.currentQuestion!, (value) {
      return _then(_self.copyWith(currentQuestion: value));
    });
  }
}

/// @nodoc

class _QuizState implements QuizState {
  const _QuizState(
      {this.currentQuestion,
      final List<QuestionOption> selectedOptions = const [],
      this.isLoading = false,
      this.error,
      this.isSubmitting = false})
      : _selectedOptions = selectedOptions;

  /// 当前问题
  @override
  final Question? currentQuestion;

  /// 选中的选项列表
  final List<QuestionOption> _selectedOptions;

  /// 选中的选项列表
  @override
  @JsonKey()
  List<QuestionOption> get selectedOptions {
    if (_selectedOptions is EqualUnmodifiableListView) return _selectedOptions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedOptions);
  }

  /// 是否加载中
  @override
  @JsonKey()
  final bool isLoading;

  /// 错误信息
  @override
  final String? error;

  /// 是否提交中
  @override
  @JsonKey()
  final bool isSubmitting;

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$QuizStateCopyWith<_QuizState> get copyWith =>
      __$QuizStateCopyWithImpl<_QuizState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _QuizState &&
            (identical(other.currentQuestion, currentQuestion) ||
                other.currentQuestion == currentQuestion) &&
            const DeepCollectionEquality()
                .equals(other._selectedOptions, _selectedOptions) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.isSubmitting, isSubmitting) ||
                other.isSubmitting == isSubmitting));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      currentQuestion,
      const DeepCollectionEquality().hash(_selectedOptions),
      isLoading,
      error,
      isSubmitting);

  @override
  String toString() {
    return 'QuizState(currentQuestion: $currentQuestion, selectedOptions: $selectedOptions, isLoading: $isLoading, error: $error, isSubmitting: $isSubmitting)';
  }
}

/// @nodoc
abstract mixin class _$QuizStateCopyWith<$Res>
    implements $QuizStateCopyWith<$Res> {
  factory _$QuizStateCopyWith(
          _QuizState value, $Res Function(_QuizState) _then) =
      __$QuizStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {Question? currentQuestion,
      List<QuestionOption> selectedOptions,
      bool isLoading,
      String? error,
      bool isSubmitting});

  @override
  $QuestionCopyWith<$Res>? get currentQuestion;
}

/// @nodoc
class __$QuizStateCopyWithImpl<$Res> implements _$QuizStateCopyWith<$Res> {
  __$QuizStateCopyWithImpl(this._self, this._then);

  final _QuizState _self;
  final $Res Function(_QuizState) _then;

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? currentQuestion = freezed,
    Object? selectedOptions = null,
    Object? isLoading = null,
    Object? error = freezed,
    Object? isSubmitting = null,
  }) {
    return _then(_QuizState(
      currentQuestion: freezed == currentQuestion
          ? _self.currentQuestion
          : currentQuestion // ignore: cast_nullable_to_non_nullable
              as Question?,
      selectedOptions: null == selectedOptions
          ? _self._selectedOptions
          : selectedOptions // ignore: cast_nullable_to_non_nullable
              as List<QuestionOption>,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      isSubmitting: null == isSubmitting
          ? _self.isSubmitting
          : isSubmitting // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuestionCopyWith<$Res>? get currentQuestion {
    if (_self.currentQuestion == null) {
      return null;
    }

    return $QuestionCopyWith<$Res>(_self.currentQuestion!, (value) {
      return _then(_self.copyWith(currentQuestion: value));
    });
  }
}

// dart format on
