import 'dart:async';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';

part 'register_provider.g.dart';

@riverpod
class Register extends _$Register {
  @override
  RegisterState build() {
    return RegisterState(
      canGetVerifyCode: false,
      verifyCodeButtonText: '获取验证码',
      isAgree: false,
    );
  }

  Timer? _countdownTimer;
  int _countdown = 0;

  // 更新倒计时按钮文本
  void _updateCountdownText() {
    if (_countdown <= 0) {
      _countdownTimer?.cancel();
      state = state.copyWith(
        verifyCodeButtonText: '获取验证码',
        canGetVerifyCode: _isValidPhone(state.phone),
      );
    } else {
      state = state.copyWith(
        verifyCodeButtonText: '重新获取($_countdown)',
        canGetVerifyCode: false,
      );
      _countdown--;
    }
  }

  // 开始倒计时
  void _startCountdown(int seconds) {
    _countdown = seconds;
    _countdownTimer?.cancel();
    _countdownTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      _updateCountdownText();
    });
    _updateCountdownText();
  }

  // 更新手机号
  void updatePhone(String phone) {
    state = state.copyWith(
      phone: phone,
      canGetVerifyCode: _isValidPhone(phone) && _countdown <= 0,
    );
  }

  // 更新验证码
  void updateVerifyCode(String code) {
    state = state.copyWith(verifyCode: code);
  }

  // 更新密码
  void updatePassword(String password) {
    state = state.copyWith(password: password);
  }

  // 更新同意协议状态
  void updateAgree(bool agree) {
    state = state.copyWith(isAgree: agree);
  }

  // 获取验证码
  Future<void> getVerifyCode() async {
    final phone = state.phone;
    if (!_isValidPhone(phone)) {
      Loading.error('请输入有效的手机号');
      return;
    }

    Loading.show('发送中...');
    try {
      final response = await LoginApi.getSmsCode(phone);
      if (response.data == true) {
        Loading.toast('验证码已发送');
        _startCountdown(60);
      } else {
        Loading.error(response.message);
      }
    } catch (e) {
      Loading.error('获取验证码失败');
    } finally {
      Loading.dismiss();
    }
  }

  // 验证手机号格式
  bool _isValidPhone(String phone) {
    final RegExp phoneRegExp = RegExp(r'^1[3-9]\d{9}$');
    return phoneRegExp.hasMatch(phone);
  }

  Future<void> register(BuildContext context) async {
    FocusScope.of(context).unfocus();

    final phone = state.phone;
    if (!_isValidPhone(phone)) {
      Loading.error('请输入有效的手机号');
      return;
    }

    if (state.password.isEmpty) {
      Loading.error('请输入密码');
      return;
    }

    final code = state.verifyCode;
    if (code.isEmpty) {
      Loading.error('请输入验证码');
      return;
    }

    if (!state.isAgree) {
      Loading.error('请同意用户协议');
      return;
    }

    // 登录
    Loading.show('注册中...');
    try {
      RegisterRequestEntity entity = RegisterRequestEntity(
          authCode: code, password: state.password, phone: phone);
      await LoginApi.register(entity);
      Loading.toast('注册成功');
      defaultLogger.debug("register success");
      gotoLogin(context);
    } catch (e) {
      Loading.error(e.toString());
    } finally {
      Loading.dismiss();
    }
  }

  void gotoLogin(BuildContext context) {
    Navigator.of(context).pushReplacementNamed(RouteNames.systemLogin);
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
  }
}

class RegisterState {
  final String phone;
  final String verifyCode;
  final String password;
  final bool canGetVerifyCode;
  final String verifyCodeButtonText;
  final bool isAgree;

  RegisterState({
    this.phone = '',
    this.verifyCode = '',
    this.password = '',
    required this.canGetVerifyCode,
    required this.verifyCodeButtonText,
    required this.isAgree,
  });

  RegisterState copyWith({
    String? phone,
    String? verifyCode,
    String? password,
    bool? canGetVerifyCode,
    String? verifyCodeButtonText,
    bool? isAgree,
  }) {
    return RegisterState(
      phone: phone ?? this.phone,
      verifyCode: verifyCode ?? this.verifyCode,
      password: password ?? this.password,
      canGetVerifyCode: canGetVerifyCode ?? this.canGetVerifyCode,
      verifyCodeButtonText: verifyCodeButtonText ?? this.verifyCodeButtonText,
      isAgree: isAgree ?? this.isAgree,
    );
  }
}
