import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/gestures.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter_svg/svg.dart';
import 'package:zrreport/common/index.dart';

import 'register_provider.dart';

class RegisterPage extends BasePage {
  const RegisterPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final phoneController = useTextEditingController();
    final codeController = useTextEditingController();
    final passwordController = useTextEditingController();

    useEffect(() {
      void phoneListener() {
        ProviderScope.containerOf(context)
            .read(registerProvider.notifier)
            .updatePhone(phoneController.text);
      }

      void codeListener() {
        ProviderScope.containerOf(context)
            .read(registerProvider.notifier)
            .updateVerifyCode(codeController.text);
      }

      void passwordListener() {
        ProviderScope.containerOf(context)
            .read(registerProvider.notifier)
            .updatePassword(passwordController.text);
      }

      phoneController.addListener(phoneListener);
      codeController.addListener(codeListener);
      passwordController.addListener(passwordListener);

      return () {
        phoneController.removeListener(phoneListener);
        codeController.removeListener(codeListener);
        passwordController.removeListener(passwordListener);
      };
    }, [phoneController, codeController, passwordController]);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text('注册'),
      ),
      body: SafeArea(
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () => FocusScope.of(context).unfocus(),
          child: ListView(
            children: [
              // 顶部欢迎文本
              buildWelcomeHeader(),
              ..._buildRegisterView(
                  context, phoneController, codeController, passwordController),
            ],
          ),
        ),
      ),
    );
  }

  /// 登录注册欢迎header
  Widget buildWelcomeHeader() {
    return [
      Image.asset(
        AssetsImages.loginBackgroundPng,
        height: 161.h,
        width: double.infinity,
        fit: BoxFit.cover,
      ),
      Padding(
        padding: const EdgeInsets.only(left: 16),
        child: SvgPicture.asset(AssetsSvgs.loginWelcomHeaderSvg, height: 80),
      ),
    ].toStack(alignment: Alignment.centerLeft);
  }

  List<Widget> _buildRegisterView(
          BuildContext context,
          TextEditingController phoneController,
          TextEditingController codeController,
          TextEditingController passwordController) =>
      <Widget>[
        SizedBox(height: 20),
        // 手机号输入框
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24),
          child: RegisterPhoneInputField(controller: phoneController),
        ),

        SizedBox(height: 20),

        // 密码输入框
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24),
          child: PasswordInputField(controller: passwordController),
        ),

        SizedBox(height: 20),

        // 验证码输入框
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24),
          child: CodeInputField(controller: codeController),
        ),

        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24),
          child: SizedBox(height: 40),
        ),

        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24),
          child: RegisterButton(),
        ),

        SizedBox(height: 16),

        RegisterFooter(),

        SizedBox(height: 16),

        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24),
          child: RegisterTermsAgreement(),
        ),
      ];
}

// 手机号输入字段
class RegisterPhoneInputField extends StatelessWidget {
  final TextEditingController controller;

  const RegisterPhoneInputField({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      return buildTextFiledWithHeader(
          pngPath: AssetsImages.mobilePng,
          header: "手机号",
          hintText: "请输入您的手机号",
          textEditingController: controller);
    }
    );
  }
}

// 密码输入字段
class PasswordInputField extends StatelessWidget {
  final TextEditingController controller;

  const PasswordInputField({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      return buildTextFiledWithHeader(
          pngPath: AssetsImages.mobilePng,
          header: "密码",
          hintText: "请输入您的密码",
          textEditingController: controller);
    });
  }
}

// 验证码输入字段
class CodeInputField extends StatelessWidget {
  final TextEditingController controller;

  const CodeInputField({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      final registerForm = ref.watch(registerProvider);
      return buildTextFiledWithHeader(
        svgPath: AssetsSvgs.smsCodeSvg,
        header: "验证码",
        hintText: "请输入验证码",
        textEditingController: controller,
        headerSuffixIcon: buildFilledButton(
          width: 100,
          backgroundColor: registerForm.canGetVerifyCode
              ? context.colors.primary
              : context.colors.disabled,
          fontColor: registerForm.canGetVerifyCode
              ? Colors.white
              : AppColors.textColor3,
          fontSize: 15,
          height: 35,
          registerForm.verifyCodeButtonText,
          onPressed: registerForm.canGetVerifyCode
              ? () => ref.read(registerProvider.notifier).getVerifyCode()
              : null,
          radius: 4,
        ),
      );
    });
  }
}

// 登录按钮
class RegisterButton extends StatelessWidget {
  const RegisterButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      return buildFilledButton("立即注册",
          onPressed: () =>
              ref.read(registerProvider.notifier).register(context),
          fontWeight: FontWeight.bold);
    });
  }
}

// 登录页脚
class RegisterFooter extends StatelessWidget {
  const RegisterFooter({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 24),
        child: Container(
          color: Colors.white,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              RichText(
                text: TextSpan(
                  style: const TextStyle(color: Colors.grey),
                  children: [
                    const TextSpan(text: '已有账号？'),
                    TextSpan(
                      text: '账号密码',
                      style: TextStyle(color: context.colors.primary),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () => Navigator.of(context)
                            .pushReplacementNamed(RouteNames.systemLogin),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16),
            ],
          ),
        ),
      );
    });
  }
}

// 条款协议组件
class RegisterTermsAgreement extends StatelessWidget {
  const RegisterTermsAgreement({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      final registerForm = ref.watch(registerProvider);

      return buildTermView(
        registerForm.isAgree,
        (value) => ref.read(registerProvider.notifier).updateAgree(value),
      );
    });
  }
}
