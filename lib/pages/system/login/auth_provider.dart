import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';

part 'auth_provider.g.dart';

// 用户登录状态Provider
@Riverpod(keepAlive: true)
class Auth extends _$Auth {
  @override
  UserDetailModel? build() {
    // 使用UserService中的用户信息作为初始状态
    // if (UserService.to.isLogin) {
    //   return UserService.to.userInfo.value;
    // }
    return null;
  }

  bool get isLogin => state != null && UserService.to.token.isNotEmpty;

  UserProfile? get userProfile => state?.umsMember;

  // 登录
  Future<void> login(String phone, String password, String code) async {
    try {
      // 使用UserService进行登录
      await UserService.to.login(phone, password, code);
      // 更新state为UserService的userInfo
      // state = UserService.to.userInfo.value;
    } catch (e) {
      rethrow;
    }
  }

  // 退出登录
  Future<void> logout() async {
    await UserService.to.logout();
    state = null;
  }

  // 刷新用户信息
  Future<UserProfile?> refreshUserInfo() async {
    // final userProfile = await UserService.to.refreshUserInfo();
    // state = UserService.to.userInfo.value;
    // return userProfile;
    return null;
  }
}

// 登录表单状态Provider
@riverpod
class LoginForm extends _$LoginForm {
  @override
  LoginFormState build() {
    return LoginFormState(
      isVerifyCodeLogin: true,
      canGetVerifyCode: false,
      verifyCodeButtonText: '获取验证码',
      isAgree: false,
    );
  }

  Timer? _countdownTimer;
  int _countdown = 0;

  // 更新倒计时按钮文本
  void _updateCountdownText() {
    if (_countdown <= 0) {
      _countdownTimer?.cancel();
      state = state.copyWith(
        verifyCodeButtonText: '获取验证码',
        canGetVerifyCode: _isValidPhone(state.phone),
      );
    } else {
      state = state.copyWith(
        verifyCodeButtonText: '重新获取($_countdown)',
        canGetVerifyCode: false,
      );
      _countdown--;
    }
  }

  // 开始倒计时
  void _startCountdown(int seconds) {
    _countdown = seconds;
    _countdownTimer?.cancel();
    _countdownTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      _updateCountdownText();
    });
    _updateCountdownText();
  }

  // 切换登录类型
  void switchLoginType(bool isVerifyCode) {
    state = state.copyWith(
      isVerifyCodeLogin: isVerifyCode,
      verifyCode: '',
      password: '',
    );
  }

  // 更新手机号
  void updatePhone(String phone) {
    state = state.copyWith(
      phone: phone,
      canGetVerifyCode: _isValidPhone(phone) && _countdown <= 0,
    );
  }

  // 更新验证码
  void updateVerifyCode(String code) {
    state = state.copyWith(verifyCode: code);
  }

  // 更新密码
  void updatePassword(String password) {
    state = state.copyWith(password: password);
  }

  // 更新同意协议状态
  void updateAgree(bool agree) {
    state = state.copyWith(isAgree: agree);
  }

  // 获取验证码
  Future<void> getVerifyCode() async {
    final phone = state.phone;
    if (!_isValidPhone(phone)) {
      Loading.error('请输入有效的手机号');
      return;
    }

    Loading.show('发送中...');
    try {
      final response = await LoginApi.getSmsCode(phone);
      if (response.data == true) {
        Loading.toast('验证码已发送');
        _startCountdown(60);
      } else {
        Loading.error(response.message);
      }
    } catch (e) {
      Loading.error('获取验证码失败');
    } finally {
      Loading.dismiss();
    }
  }

  // 验证手机号格式
  bool _isValidPhone(String phone) {
    final RegExp phoneRegExp = RegExp(r'^1[3-9]\d{9}$');
    return phoneRegExp.hasMatch(phone);
  }

  // 登录处理
  Future<void> login(BuildContext context) async {
    FocusScope.of(context).unfocus();

    final phone = state.phone;
    if (!_isValidPhone(phone)) {
      Loading.error('请输入有效的手机号');
      return;
    }

    if (!state.isAgree) {
      await showTermDialog();
      if (!state.isAgree) return;
    }

    if (state.isVerifyCodeLogin) {
      final code = state.verifyCode;
      if (code.isEmpty) {
        Loading.error('请输入验证码');
        return;
      }

      Loading.show('登录中...');
      try {
        await ref.read(authProvider.notifier).login(phone, '', code);
        Navigator.of(context).pop();
      } catch (e) {
        Loading.error(e.toString());
      } finally {
        Loading.dismiss();
      }
    } else {
      final password = state.password;
      if (password.isEmpty) {
        Loading.error('请输入密码');
        return;
      }
      Loading.show('登录中...');
      try {
        await ref.read(authProvider.notifier).login(phone, password, '');
        Navigator.of(context).pop();
      } catch (e) {
        Loading.error(e.toString());
      } finally {
        Loading.dismiss();
      }
    }
  }

  void dispose() {
    _countdownTimer?.cancel();
  }

  Future<void> showTermDialog() async {
    await showCommonDialog(
      Get.context!,
      title: '温馨提示',
      content: '',
      child: buildTermDialogView(),
      onConfirm: () => updateAgree(true),
    );
  }
}

// 登录表单状态
class LoginFormState {
  final String phone;
  final String verifyCode;
  final String password;
  final bool isVerifyCodeLogin;
  final bool canGetVerifyCode;
  final String verifyCodeButtonText;
  final bool isAgree;

  LoginFormState({
    this.phone = '',
    this.verifyCode = '',
    this.password = '',
    required this.isVerifyCodeLogin,
    required this.canGetVerifyCode,
    required this.verifyCodeButtonText,
    required this.isAgree,
  });

  LoginFormState copyWith({
    String? phone,
    String? verifyCode,
    String? password,
    bool? isVerifyCodeLogin,
    bool? canGetVerifyCode,
    String? verifyCodeButtonText,
    bool? isAgree,
  }) {
    return LoginFormState(
      phone: phone ?? this.phone,
      verifyCode: verifyCode ?? this.verifyCode,
      password: password ?? this.password,
      isVerifyCodeLogin: isVerifyCodeLogin ?? this.isVerifyCodeLogin,
      canGetVerifyCode: canGetVerifyCode ?? this.canGetVerifyCode,
      verifyCodeButtonText: verifyCodeButtonText ?? this.verifyCodeButtonText,
      isAgree: isAgree ?? this.isAgree,
    );
  }
}
