import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter_svg/svg.dart';
import 'package:zrreport/common/index.dart';

import 'auth_provider.dart';

/// 登录页面
class LoginPage1 extends BasePage {
  const LoginPage1({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final phoneController = useTextEditingController();
    final codeController = useTextEditingController();
    final passwordController = useTextEditingController();

    // 添加监听器
    useEffect(() {
      void phoneListener() {
        ProviderScope.containerOf(context)
            .read(loginFormProvider.notifier)
            .updatePhone(phoneController.text);
      }

      void codeListener() {
        ProviderScope.containerOf(context)
            .read(loginFormProvider.notifier)
            .updateVerifyCode(codeController.text);
      }

      void passwordListener() {
        ProviderScope.containerOf(context)
            .read(loginFormProvider.notifier)
            .updatePassword(passwordController.text);
      }

      phoneController.addListener(phoneListener);
      codeController.addListener(codeListener);
      passwordController.addListener(passwordListener);

      return () {
        phoneController.removeListener(phoneListener);
        codeController.removeListener(codeListener);
        passwordController.removeListener(passwordListener);
      };
    }, [phoneController, codeController, passwordController]);

    return Scaffold(
      // backgroundColor: Color(0xFFF0F5FE),
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text('登录'),
      ),
      body: SafeArea(
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () => FocusScope.of(context).unfocus(),
          child: ListView(
            children: [
              // 顶部欢迎文本
              buildWelcomeHeader(),
              Column(
                children: _buildLoginView(context, phoneController,
                    codeController, passwordController),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildWelcomeHeader() {
    return [
      Image.asset(
        AssetsImages.loginBackgroundPng,
        height: 161.h,
        width: double.infinity,
        fit: BoxFit.cover,
      ),
      Padding(
        padding: const EdgeInsets.only(left: 16),
        child: SvgPicture.asset(AssetsSvgs.loginWelcomHeaderSvg, height: 80),
      ),
    ].toStack(alignment: Alignment.centerLeft);
  }

  List<Widget> _buildLoginView(
          BuildContext context,
          TextEditingController phoneController,
          TextEditingController codeController,
          TextEditingController passwordController) =>
      <Widget>[
        Container(
          height: 20,
          color: Color(0xFFF0F5FE),
          child: Container(
            color: Colors.white,
          ).clipRRect(topLeft: 16, topRight: 16),
        ),
        // 登录方式切换
        LoginTypeSwitcher()
            .paddingHorizontal(24)
            ,

        SizedBox(height: 18),

        // 手机号输入框
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24),
          child: PhoneInputField(controller: phoneController),
        ),

        SizedBox(height: 30),

        // 验证码/密码输入框
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24),
          child: PasswordOrCodeField(
            codeController: codeController,
            passwordController: passwordController,
          ),
        ),

        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24),
          child: SizedBox(height: 40),
        ),

        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24),
          child: LoginButton(),
        ),

        SizedBox(height: 16),

        LoginFooter(),

        SizedBox(height: 16),

        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24),
          child: TermsAgreement(),
        ),
      ];
}

// 登录方式切换组件
class LoginTypeSwitcher extends StatelessWidget {
  const LoginTypeSwitcher({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      final loginForm = ref.watch(loginFormProvider);

      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildLoginTypeButton(
            '验证码登录',
            loginForm.isVerifyCodeLogin,
            () => ref.read(loginFormProvider.notifier).switchLoginType(true),
            context,
          ),
          SizedBox(width: 24),
          _buildLoginTypeButton(
            '密码登录',
            !loginForm.isVerifyCodeLogin,
            () => ref.read(loginFormProvider.notifier).switchLoginType(false),
            context,
          ),
        ],
      );
    });
  }

  Widget _buildLoginTypeButton(
      String text, bool isSelected, VoidCallback onTap, BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Text(
        text,
        style: TextStyle(
          color: isSelected ? context.colors.primary : Colors.grey,
          fontSize: 16,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
    );
  }
}

// 手机号输入字段
class PhoneInputField extends StatelessWidget {
  final TextEditingController controller;

  const PhoneInputField({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      return buildTextFiledWithHeader(
          pngPath: AssetsImages.mobilePng,
          header: "手机号",
          hintText: "请输入您的手机号",
          textEditingController: controller);
    });
  }
}

// 密码或验证码输入字段
class PasswordOrCodeField extends StatelessWidget {
  final TextEditingController codeController;
  final TextEditingController passwordController;

  const PasswordOrCodeField({
    Key? key,
    required this.codeController,
    required this.passwordController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      final loginForm = ref.watch(loginFormProvider);

      if (loginForm.isVerifyCodeLogin) {
        return buildTextFiledWithHeader(
          svgPath: AssetsSvgs.smsCodeSvg,
          header: "验证码",
          hintText: "请输入验证码",
          textEditingController: codeController,
          headerSuffixIcon: buildFilledButton(
            width: 100,
            backgroundColor: loginForm.canGetVerifyCode
                ? context.colors.primary
                : context.colors.disabled,
            fontColor: loginForm.canGetVerifyCode
                ? Colors.white
                : AppColors.textColor3,
            fontSize: 15,
            height: 35,
            loginForm.verifyCodeButtonText,
            onPressed: loginForm.canGetVerifyCode
                ? () => ref.read(loginFormProvider.notifier).getVerifyCode()
                : null,
            radius: 4,
          ),
        );
      } else {
        return buildTextFiledWithHeader(
          svgPath: AssetsSvgs.passwordSvg,
          header: "密码",
          hintText: "请输入您的密码",
          textEditingController: passwordController,
          keyboardType: TextInputType.visiblePassword,
        );
      }
    });
  }
}

// 登录按钮
class LoginButton extends StatelessWidget {
  const LoginButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      return buildFilledButton("立即登录",
          onPressed: () => ref.read(loginFormProvider.notifier).login(context),
          fontWeight: FontWeight.bold);
    });
  }
}

// 登录页脚
class LoginFooter extends StatelessWidget {
  const LoginFooter({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      final loginForm = ref.watch(loginFormProvider);

      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 24),
        child: Container(
          color: Colors.white,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (loginForm.isVerifyCodeLogin)
                Text(
                  textAlign: TextAlign.start,
                  '首次登录将默认自动注册',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                )
              else
                RichText(
                  text: TextSpan(
                    style: const TextStyle(color: Colors.grey),
                    children: [
                      const TextSpan(text: '还没有账号?'),
                      TextSpan(
                        text: '立即注册',
                        style: TextStyle(color: context.colors.primary),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () => Navigator.of(context)
                              .pushReplacementNamed(RouteNames.systemRegister),
                      ),
                    ],
                  ),
                ),
              SizedBox(height: 16),
            ],
          ),
        ),
      );
    });
  }
}

// 条款协议组件
class TermsAgreement extends StatelessWidget {
  const TermsAgreement({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      final loginForm = ref.watch(loginFormProvider);

      return buildTermView(
        loginForm.isAgree,
        (value) => ref.read(loginFormProvider.notifier).updateAgree(value),
      );
    });
  }
}
