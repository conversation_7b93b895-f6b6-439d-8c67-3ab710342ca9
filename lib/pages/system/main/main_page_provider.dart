import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';

part 'main_page_provider.g.dart';

class MainPageState {
  int currentIndex = 0;

  MainPageState copyWith({int? newIndex}) {
    return MainPageState()
      //  ..pageStatus = pageStatus
      ..currentIndex = newIndex ?? this.currentIndex;
  }
}

@riverpod
class MainPageProvider extends _$MainPageProvider {
  PageController pageController = PageController();

  @override
  MainPageState build() {
    return MainPageState();
  }

  Future<void> onTap(int index) async {
    if (index == 2 || index == 3) {
      //报告或者我的
      if ((!await loginIfNotLogin())) {
        return;
      }
    } 
    jumpPage(index);
  }

  void jumpPage(int index) {
    pageController.jumpToPage(index);
    state = state.copyWith(newIndex: index);
  }
}
