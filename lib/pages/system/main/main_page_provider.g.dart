// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'main_page_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$mainPageProviderHash() => r'928e3790cd8694b5cd82610586d1ce5997940161';

/// See also [MainPageProvider].
@ProviderFor(MainPageProvider)
final mainPageProviderProvider =
    AutoDisposeNotifierProvider<MainPageProvider, MainPageState>.internal(
  MainPageProvider.new,
  name: r'mainPageProviderProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$mainPageProviderHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MainPageProvider = AutoDisposeNotifier<MainPageState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
