import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/common/provider/dict_types/test_dict_helper.dart';
import 'package:zrreport/global.dart';
import 'package:zrreport/pages/home/<USER>/home_page.dart';
import 'package:zrreport/pages/index.dart';

import 'main_page_provider.dart';

class MainPage extends ConsumerStatefulWidget {
  const MainPage({Key? key}) : super(key: key);

  @override
  ConsumerState<MainPage> createState() => _MainPageState();
}

class _MainPageState extends ConsumerState<MainPage>
    with AutomaticKeepAliveClientMixin {
  // 返回键退出
  // 退出请求时间
  DateTime? currentBackPressTime;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // 初始化用户信息
    Future.microtask(() {
      ref.read(userProfileNotifierProvider.notifier).refreshUserProfile();
    });

    // 预加载字典数据
    Future.microtask(() async {
      await Global.preloadDictData();
      // 在开发模式下测试字典功能
      if (kDebugMode) {
        // 延迟一秒后测试，确保数据已加载
        Future.delayed(const Duration(seconds: 2), () {
          DictTypesHelperTest.testInitialization();
        });
      }
    });

    UserService.to.isLoginRx.listen((isLogin) {
      if (!mounted) {
        return;
      }
      if (isLogin) {
        // 登录成功后，刷新用户信息
        ref.read(userProfileNotifierProvider.notifier).refreshUserProfile();
      } else {
        // 用户登出时，清除用户信息
        ref.read(userProfileNotifierProvider.notifier).clearProfile();
      }
    });
  }

  bool closeOnConfirm() {
    DateTime now = DateTime.now();
    // 物理键，两次间隔大于1秒, 退出请求无效
    if (currentBackPressTime == null ||
        now.difference(currentBackPressTime!) > const Duration(seconds: 2)) {
      currentBackPressTime = now;
      Loading.toast('再次按下以关闭应用程序');
      return false;
    }

    // 退出请求有效
    currentBackPressTime = null;
    return true;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 调用父类的build方法
    ref.listen(userProfileNotifierProvider, (prev, next) {
      debugPrint("nickname from ${prev?.nickname} to ${next?.nickname}");
    });
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          return;
        }

        if (closeOnConfirm()) {
          SystemNavigator.pop();
        }
      },
      child: Scaffold(
        // resizeToAvoidBottomInset: false,
          backgroundColor: Colors.white,
          body: _buildPageView(),
          bottomNavigationBar: Theme(
            data: Theme.of(context).copyWith(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
            ),
            child: _buildBottomNavigationBar(),
        ),
      ),
    );
  }

  Widget _buildPageView() {
    final provider = ref.read(mainPageProviderProvider.notifier);
    return PageView(
      physics: const NeverScrollableScrollPhysics(),
      pageSnapping: false,
      allowImplicitScrolling: false,
      controller: provider.pageController,
      children: [
        HomePage(),
        LoanMatchingPage(),
        HistoryMain(),
        MinePage(),
      ],
    );
  }

  Widget _buildBottomNavigationBar() {
    final state = ref.watch(mainPageProviderProvider);
    return BottomNavigationBar(
      backgroundColor: Colors.transparent,
      onTap: ref.read(mainPageProviderProvider.notifier).onTap,
      type: BottomNavigationBarType.fixed,
      selectedItemColor: Color(0xFF276FF7),
      currentIndex: state.currentIndex,
      elevation: 0,
      selectedFontSize: 0,
      unselectedFontSize: 0,
      items: [
        _buildBarItem(
            AssetsImages.tabIconHomePng, AssetsImages.tabIconHomeActivePng),
        _buildBarItem(AssetsImages.tabIconProductPng,
            AssetsImages.tabIconProductActivePng),
        _buildBarItem(
            AssetsImages.tabIconReportPng, AssetsImages.tabIconReportActivePng),
        _buildBarItem(
            AssetsImages.tabIconMinePng, AssetsImages.tabIconMineActivePng),
      ],
    );
  }

  BottomNavigationBarItem _buildBarItem(String asset, String activeAsset) {
    return BottomNavigationBarItem(
        icon: Image.asset(asset),
        label: "",
        activeIcon: Image.asset(activeAsset));
  }
}
