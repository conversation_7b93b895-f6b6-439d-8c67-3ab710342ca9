import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get/get.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/common/routers/index.dart';
import 'package:zrreport/pages/index.dart';

// class MainPage extends ConsumerWidget {
//   const MainPage({Key? key}) : super(key: key);

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     return Scaffold(
//         backgroundColor: Color(0xFFF4F8FB),
//         extendBodyBehindAppBar: true,
//         appBar: AppBar(
//           title: Text(
//             "助融报告",
//             style: TextStyle(
//                 color: Colors.white, fontSize: 16, fontWeight: FontWeight.w600),
//           ),
//           backgroundColor: Colors.transparent,
//           elevation: 0,
//         ),
//         extendBody: false,
//         body: Container(
//           decoration: BoxDecoration(
//             image: DecorationImage(
//               alignment: Alignment.topCenter,
//               image: AssetImage(AssetsImages.homeBackgroundPng),
//               fit: BoxFit.fitWidth,
//             ),
//           ),
//           child: SafeArea(
//             bottom: false,
//             child: ListView(
//               padding: EdgeInsets.symmetric(horizontal: 16),
//               children: [
//                 SizedBox(height: 22),
//                 _buildUserWidget(),
//                 SizedBox(height: 60),
//                 _buildTaxCard(),
//                 SizedBox(height: 10),
//                 VisibilityMonitor(
//                   child: _buildUsageCard(ref),
//                   keyPrefix: 'home_userage_card',
//                   onBecomeVisible: () {
//                     // ignore: unused_result
//                     ref.refresh(usageStatisticsProvider.future);
//                   },
//                 ),
//                 SizedBox(height: 16),
//                 _buildMenuCard(),
//               ],
//             ),
//           ),
//         ));
//   }

//   _buildUserWidget() {
//     final profile = UserService.to.userProfile;
//     final nickStyle = TextStyle(
//         color: Colors.white,
//         fontSize: 16,
//         fontWeight: FontWeight.w600,
//         height: 22 / 16);

//     final phoneStyle =
//         TextStyle(color: Colors.white, fontSize: 11, height: 15 / 11);
//     return Row(
//       children: [
//         AvatarWidget(
//           size: 52,
//           imageUrl: profile?.icon,
//         ),
//         SizedBox(width: 12),
//         Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               children: [
//                 Text(profile?.nickname ?? '', style: nickStyle),
//                 SizedBox(width: 10),
//                 GestureDetector(
//                   onTap: () => Get.toNamed(RouteNames.mineEditProfile),
//                   child: Container(
//                     decoration: const BoxDecoration(
//                       color: Colors.white,
//                       borderRadius: BorderRadius.all(
//                         Radius.circular(12),
//                       ),
//                     ),
//                     padding: EdgeInsets.symmetric(horizontal: 12, vertical: 5),
//                     child: Text(
//                       "设置",
//                       style: TextStyle(
//                           fontSize: 11,
//                           color: AppColors.primary,
//                           fontWeight: FontWeight.w600),
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//             SizedBox(height: 4),
//             Text('手机号:${profile?.phone ?? ''}', style: phoneStyle),
//           ],
//         )
//       ],
//     );
//   }

//   _buildTaxCard() {
//     return Container(
//       decoration: const BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.all(
//           Radius.circular(12),
//         ),
//       ),
//       padding: EdgeInsets.only(left: 24, right: 24, top: 30, bottom: 20),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Row(
//             children: [
//               Text(
//                 "税务报告",
//                 style: TextStyle(
//                     fontSize: 18,
//                     color: Color(0xFF222222),
//                     fontWeight: FontWeight.w600),
//               ),
//               Spacer(),
//               Text(
//                 "查看样例报告",
//                 style: TextStyle(
//                     fontSize: 13,
//                     color: AppColors.primary,
//                     fontWeight: FontWeight.w600),
//               ),
//             ],
//           ),
//           SizedBox(height: 8),
//           Text(
//             "税票查询/提升通过率",
//             style: TextStyle(fontSize: 13, color: Color(0xFF222222)),
//           ),
//           SizedBox(height: 28),
//           buildFilledButton('立即查询',
//               onPressed: () => Get.toNamed(RouteNames.queryStep1))
//         ],
//       ),
//     );
//   }

//   _buildUsageCard(WidgetRef ref) {
//     final usageAsync = ref.watch(usageStatisticsProvider);
//     return Container(
//       decoration: const BoxDecoration(
//         borderRadius: BorderRadius.all(
//           Radius.circular(12),
//         ),
//         image: DecorationImage(
//           image: AssetImage(AssetsImages.homeUsageBackgroundPng),
//           fit: BoxFit.fitWidth,
//         ),
//       ),
//       padding: EdgeInsets.only(left: 12, right: 12, top: 12, bottom: 20),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Row(children: [
//             Image.asset(AssetsImages.homeUsageIconPng),
//             SizedBox(width: 4),
//             Text(
//               "使用情况",
//               style: TextStyle(
//                   fontSize: 18,
//                   fontWeight: FontWeight.w600,
//                   color: AppColors.primary),
//             ),
//             Spacer(),
//             GestureDetector(
//                 onTap: () => Get.toNamed(RouteNames.usageStatistics),
//                 child:
//                     Text('查看详情>', style: TextStyle(color: AppColors.primary))),
//           ]),
//           SizedBox(height: 15),
//           Row(
//             children: [
//               _buildUsageItemByAsyncValue(usageAsync,
//                   title: (data) => "${data.freeQueryNum}次",
//                   titleColor: Color(0xFF222222),
//                   subtitle: '总次数'),
//               SizedBox(width: 8),
//               _buildUsageItemByAsyncValue(usageAsync,
//                   title: (data) => "${data.usedQueryNum}次",
//                   titleColor: Color(0xFFF04F2F),
//                   subtitle: '已使用'),
//               SizedBox(width: 8),
//               _buildUsageItemByAsyncValue(usageAsync,
//                   title: (data) => "${data.availQueryNum}次",
//                   titleColor: Color(0xFF222222),
//                   subtitle: '可用'),
//             ],
//           ),
//           SizedBox(height: 14),
//           Row(
//             children: [
//               SizedBox(width: 10),
//               Text('推广增加查询次数\n可以无限叠加使用',
//                   style: TextStyle(
//                       fontSize: 13,
//                       color: AppColors.primary,
//                       height: 22 / 13,
//                       fontWeight: FontWeight.w400)),
//               Spacer(),
//               buildFilledButton('推广得查询次数',
//                   width: 140,
//                   height: 32,
//                   fontSize: 13,
//                   fontWeight: FontWeight.w600,
//                   onPressed: () => Get.toNamed(RouteNames.usageStatistics)),
//               SizedBox(width: 10),
//             ],
//           )

//         ],
//       ),
//     );
//   }

//   _buildUsageItemByAsyncValue(AsyncValue<UsageStatisticsModel> usageAsync,
//       {required String Function(UsageStatisticsModel data) title,
//       required Color titleColor,
//       required String subtitle}) {
//     return Expanded(
//         child: usageAsync.when(
//       loading: () => _buildUsageItem("--", Color(0xFF888888), subtitle),
//       error: (_, st) => _buildUsageItem("--", Color(0xFF888888), subtitle),
//       data: (data) => _buildUsageItem(title(data), titleColor, subtitle),
//     ));
//   }

//   _buildUsageItem(String title, Color titleColor, String subtitle) {
//     return Container(
//       decoration: const BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.all(
//           Radius.circular(12),
//         ),
//       ),
//       padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
//       child: Column(children: [
//         Text(
//           title,
//           style: TextStyle(
//               fontSize: 14, fontWeight: FontWeight.w600, color: titleColor),
//         ),
//         SizedBox(height: 2),
//         Text(
//           subtitle,
//           style: TextStyle(fontSize: 12, color: Color(0xFF888888)),
//         ),
//       ]),
//     );
//   }

//   _buildMenuCard() {
//     return Row(
//       children: [
//         Expanded(
//             child: GestureDetector(
//           onTap: () => Get.toNamed(RouteNames.listProvince),
//           child: Image.asset(
//             AssetsImages.homeMaintainBackgroundPng,
//             fit: BoxFit.fitWidth,
//           ),
//         )),
//         SizedBox(width: 12),
//         Expanded(
//             child: GestureDetector(
//           onTap: () => Get.toNamed(RouteNames.histories),
//           child: Image.asset(
//             AssetsImages.homeHistoryBackgroundPngPng,
//             fit: BoxFit.fitWidth,
//           ),
//         )),
//       ],
//     );
//   }
// }
