import 'dart:ffi';

import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/system/edit_profile/profile_state.dart';

class ProfileForm extends StatelessWidget {
  ProfileForm({
    super.key,
    required this.state
  });

  ProfileState state;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTextFiledWithHeader(
            maxLength: 12,
            header: '昵称',
            hintText: '请输入12字以内的昵称',
            textEditingController: TextEditingController(
                text: state.nickname.isNotEmpty ? state.nickname : ''),
          ),
          const SizedBox(height: 20),
          _buildTextFiledWithHeader(
              maxLength: 12,
              header: '手机号',
              isEditable: false,
              hintText: '',
              textColor: AppColors.textColor9,
              textEditingController: TextEditingController(
                  text: state.phone)),
        ],
      ),
    );
  }

  Widget _buildTextFiledWithHeader(
      {required String header,
      Widget? headerSuffixIcon,
      bool isEditable = true,
      int? maxLength,
      required String hintText,
      TextEditingController? textEditingController,
      Color? textColor}) {
    return TextField(
      enabled: isEditable,
      controller: textEditingController,
      style: TextStyle(fontSize: 16, color: textColor ?? AppColors.textColor1),
      maxLength: maxLength,
      textAlign: TextAlign.end,
      decoration: InputDecoration(
        prefix: Text(header,
            style: TextStyle(
              color: AppColors.textColor1,
              fontSize: 16,
            )),
        suffixIcon: headerSuffixIcon,
        hintText: hintText,
        counterText: '',
        hintStyle: TextStyle(color: AppColors.textColor9, fontSize: 16),
        border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide.none),
        contentPadding: EdgeInsets.only(left: 0, right: 0, bottom: 20),
      ),
    ).border(bottom: 0.5, color: AppColors.dividerColor);
  }
}
