import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';

class AvatarPicker extends StatelessWidget {
  final String? avatarUrl;
  final VoidCallback onAvatarChanged;

  const AvatarPicker({
    super.key,
    this.avatarUrl,
    required this.onAvatarChanged,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onAvatarChanged,
      child: Center(
        child: Column(
          children: [
            Stack(
              alignment: Alignment.center,
              children: [
                AvatarWidget(
                  imageUrl: avatarUrl,
                  size: 100,
                ),
                ClipOval(
                  child: Container(
                    color: Color(0xFF222222).withAlpha(128),
                    width: 100,
                    height: 100,
                  ),
                ),
                const Icon(
                  Icons.camera_alt_outlined,
                  color: Colors.white,
                  size: 30,
                ),
              ],
            ),

          ],
        ),
      ),
    );
  }
}
