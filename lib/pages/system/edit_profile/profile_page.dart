// lib/pages/system/edit_profile/profile_page.dart
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get/get.dart';
import 'package:zrreport/common/index.dart';

import 'profile_provider.dart';
import 'widgets/avatar_picker.dart';
import 'widgets/profile_form.dart';

class EditProfilePage extends ConsumerWidget {
  const EditProfilePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(profileNotifierProvider);
    final notifier = ref.read(profileNotifierProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new),
          onPressed: () => Get.back(),
        ),
      ),
      body: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              const SizedBox(height: 20),
              AvatarPicker(
                onAvatarChanged: notifier.handleAvatarChanged,
                avatarUrl: state.avatarUrl,
              ),
              const SizedBox(height: 20),


              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTextFiledWithHeader(
                      maxLength: 12,
                      header: '昵称',
                      hintText: '请输入12字以内的昵称',
                      textEditingController: TextEditingController(
                          text:
                              state.nickname.isNotEmpty ? state.nickname : ''),
                      onSubmitted: (value) {
                        notifier.updateNickname(value);
                      },
                    ),
                    const SizedBox(height: 20),
                    _buildTextFiledWithHeader(
                        maxLength: 12,
                        header: '手机号',
                        isEditable: false,
                        hintText: '',
                        textColor: AppColors.textColor9,
                        textEditingController:
                            TextEditingController(text: state.phone)),
                  ],
                ),
              ),

              Spacer(),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: ElevatedButton(
                  onPressed: state.isSubmitting ? null : notifier.handleSubmit,
                  style: ElevatedButton.styleFrom(
                    elevation: 0,
                    minimumSize: const Size(double.infinity, 44),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(22),
                    ),
                  ),
                  child: state.isSubmitting
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text(
                          '确定',
                          style: TextStyle(
                              fontSize: 17, fontWeight: FontWeight.w600),
                        ),
                ),
              ),
              const SizedBox(height: 20),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: ElevatedButton(
                  onPressed: () => notifier.handleLogout(context),
                  style: ElevatedButton.styleFrom(
                    elevation: 0,
                    backgroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 44),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(22),
                      side: BorderSide(color: AppColors.primary),
                    ),
                  ),
                  child: const Text(
                    '退出登录',
                    style: TextStyle(
                        color: AppColors.primary,
                        fontSize: 17,
                        fontWeight: FontWeight.w600),
                  ),
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }


  Widget _buildTextFiledWithHeader(
      {required String header,
      Widget? headerSuffixIcon,
      bool isEditable = true,
      int? maxLength,
      required String hintText,
      TextEditingController? textEditingController,
      Color? textColor,
    ValueChanged<String>? onChanged,
    ValueChanged<String>? onSubmitted,
  }) {
    return TextField(
      enabled: isEditable,
      controller: textEditingController,
      style: TextStyle(fontSize: 16, color: textColor ?? AppColors.textColor1),
      maxLength: maxLength,
      textAlign: TextAlign.end,
      decoration: InputDecoration(
        prefix: Text(header,
            style: TextStyle(
              color: AppColors.textColor1,
              fontSize: 16,
            )),
        suffixIcon: headerSuffixIcon,
        hintText: hintText,
        counterText: '',
        hintStyle: TextStyle(color: AppColors.textColor9, fontSize: 16),
        border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide.none),
        contentPadding: EdgeInsets.only(left: 0, right: 0, bottom: 20),
      ),
      onChanged: onChanged,
      onSubmitted: onSubmitted,
    ).border(bottom: 0.5, color: AppColors.dividerColor);
  }
}