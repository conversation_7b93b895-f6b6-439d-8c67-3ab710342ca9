// lib/pages/system/edit_profile/profile_state.dart
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:zrreport/common/index.dart';

part 'profile_state.freezed.dart';

@freezed
abstract class ProfileState with _$ProfileState {
  const factory ProfileState(
      // UserProfile? userProfile,
      {@Default(false) bool isSubmitting,
      @Default('') String nickname,
      String? avatarUrl,
      @Default('') String phone}) = _ProfileState;

  // 添加一个私有构造函数
  const ProfileState._();
}
