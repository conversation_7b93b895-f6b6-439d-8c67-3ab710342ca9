// lib/pages/system/edit_profile/profile_provider.dart
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/system/main/main_page_provider.dart';
import 'profile_state.dart';

part 'profile_provider.g.dart';

@riverpod
class ProfileNotifier extends _$ProfileNotifier {
  @override
  ProfileState build() {
    final userProfile = ref.watch(userProfileNotifierProvider);
    return ProfileState(
      nickname: userProfile?.nickname ?? '',
      avatarUrl: userProfile?.icon ?? '',
      isSubmitting: false,
      phone: userProfile?.phone ?? '',
    ); 
  }

  Future<void> handleAvatarChanged() async {
    ImagePickerHelper(Get.context!).pickWithCropImage(
      ImageSource.gallery,
      aspectRatio: 1,
      callback: (croppedFile) async {
        if (croppedFile != null) {
          try {
            final uploadResult =
                await UserApi.uploadUserAvatar(croppedFile.path);
            state = state.copyWith(avatarUrl: uploadResult.data?.url);
          } catch (e) {
            Loading.error('上传头像失败');
          }
        }
      },
    );
  }

  Future<void> handleSubmit() async {
    if (state.isSubmitting) return;

    if (state.nickname.isEmpty) {
      Loading.error('请输入昵称');
      return;
    }

    try {
      state = state.copyWith(isSubmitting: true);
      await UserApi.updateUserProfile(
        UpdateProfileRequestEntity(
          nickname: state.nickname,
          icon: state.avatarUrl ?? '',
        ),
      );

      ref.read(userProfileNotifierProvider.notifier).updateProfile(
          nickname: state.nickname, avatarUrl: state.avatarUrl ?? '');
      Get.back();
      Loading.toast('资料更新成功!');
    } catch (e) {
      Loading.toast('资料更新失败!');
      userLogger.debug('更新资料失败: $e');
    } finally {
      state = state.copyWith(isSubmitting: false);
    }
  }

  Future<void> handleLogout(BuildContext context) async {
    showCommonDialog(
      Get.context!,
      title: '退出登录',
      content: '确定要退出登录吗？',
      onTextChanged: (value) {},
      cancelText: '取消',
      confirmText: '确定',
      onConfirm: () async {
        try {
          Loading.show('退出中...');
          await LoginApi.logout();
          await UserService.to.logout();
          Loading.toast('已退出登录');

          Navigator.of(context).pushNamedAndRemoveUntil(
            RouteNames.systemMain,
            (route) => false,
          );

          final mainPage = ProviderScope.containerOf(context)
              .read(mainPageProviderProvider.notifier);
          mainPage.jumpPage(0);
        } catch (e) {
          Loading.error(e.toString());
        }
      },
    );
  }

  void updateNickname(String value) {
    state = state.copyWith(nickname: value);
  }
}
