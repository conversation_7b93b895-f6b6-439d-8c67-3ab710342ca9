import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:zrreport/common/index.dart';

import 'index.dart';

///
class DevelopConfigPage extends GetView<DevelopConfigController> {
  const DevelopConfigPage({super.key});

  // 主视图
  Widget _buildView() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // 环境选择
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Text(
                      '服务器环境',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Spacer(),
                    buildFilledButton('保存配置',
                        width: 80,
                        onPressed: () => controller.saveEnvironment())
                  ],
                ).height(44),
                const SizedBox(height: 16),
                Obx(() => Column(
                      children: [
                        _buildEnvironmentItem(
                          '测试环境',
                          Environment.test,
                          controller.currentEnvironment.value ==
                              Environment.test,
                        ),
                        _buildEnvironmentItem(
                          'UAT环境',
                          Environment.uat,
                          controller.currentEnvironment.value ==
                              Environment.uat,
                        ),
                        _buildEnvironmentItem(
                          '生产环境',
                          Environment.prod,
                          controller.currentEnvironment.value ==
                              Environment.prod,
                        ),
                      ],
                    )),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEnvironmentItem(
    String title,
    Environment environment,
    bool isSelected,
  ) {
    return ListTile(
      title: Text(title),
      subtitle: Text(environment.baseUrl),
      trailing: isSelected
          ? const Icon(Icons.check_circle, color: Colors.blue)
          : const Icon(Icons.radio_button_unchecked),
      onTap: () => controller.switchEnvironment(environment),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<DevelopConfigController>(
        init: DevelopConfigController(),
        builder: (context) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('开发配置'),
              centerTitle: true,
            ),
            body: SafeArea(
              child: _buildView(),
            ),
          );
        });
  }
}
