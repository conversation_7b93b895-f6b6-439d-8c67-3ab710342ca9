import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zrreport/common/index.dart';

import 'index.dart';

class WebPage extends GetView<WebPageController> {
  const WebPage({super.key});

  bool get showNavigationBar {
    return Get.arguments["showNavigationBar"] ?? true;
  }

  Widget _buildView() {
    return WebViewContainer(
      initialUrl: Get.arguments["url"],
      onWebControllerCreate: (webViewController) {
        controller.webViewController = webViewController;
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          return;
        }
        backAction();
      },
      child: GetBuilder<WebPageController>(
        init: WebPageController(),
        id: "webpage",
        builder: (_) {
          return Scaffold(
            appBar: showNavigationBar
                ? AppBar(
                    title: Text(Get.arguments?['title'] ?? ''),
                    leading: IconButton(
                      onPressed: () => backAction(),
                      icon: const Icon(Icons.arrow_back_ios_new),
                    ),
                  )
                : null,
            body: _buildView(),
          );
        },
      ),
    );
  }

  Future<void> backAction() async {
    if (await controller.webViewController?.canGoBack() ?? false) {
      controller.webViewController?.goBack();
      return;
    }
    Get.back();
  }
}
