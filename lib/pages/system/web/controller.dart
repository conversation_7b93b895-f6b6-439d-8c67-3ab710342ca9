import 'package:get/get.dart';
import 'package:zrreport/common/components/web/controller.dart';

class WebPageController extends GetxController {
  WebPageController();

  _initData() {
    update(["webpage"]);
  }

  void onTap() {}

  // @override
  // void onInit() {
  //   super.onInit();
  // }

  @override
  void onReady() {
    super.onReady();
    _initData();
  }

  MyWebViewController? webViewController;

  // @override
  // void onClose() {
  //   super.onClose();
  // }
}
