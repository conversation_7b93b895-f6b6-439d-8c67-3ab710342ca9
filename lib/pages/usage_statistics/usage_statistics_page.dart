import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import 'usage_statistics_card.dart';

class UsageStatisticsPage extends ConsumerWidget {
  UsageStatisticsPage({Key? key}) : super(key: key);

  final numTextStyle = TextStyle(
      color: Color(0xFF222222), fontSize: 22, fontWeight: FontWeight.bold);

  final largeTextStyle =
      TextStyle(color: Colors.white, fontSize: 28, fontWeight: FontWeight.w600);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final usageAsync = ref.watch(usageStatisticsNotifierProvider);
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '使用情况',
        ),
        elevation: 0,
      ),
      backgroundColor: Color(0xFFF5F5F5),
      body: usageAsync.when(
        loading: () => LoadingWidget(),
        error: (e, st) => Center(
            child: ErrorStatusWidget(
                onAttempt: () => ref.refresh(usageStatisticsNotifierProvider))),
        data: (model) => ListView(
          padding: const EdgeInsets.only(top: 16, bottom: 16),
          children: [
            SizedBox(height: 16),
            _buildTopCard(model),
            SizedBox(height: 16),
            _buildNewUserCard(model),
            SizedBox(height: 12),
            _buildInviteCard(model),
            SizedBox(height: 12),
            _buildDailyGiftCard(model),
          ],
        ),
      ),
    );
  }

  Widget _buildTopCard(UsageStatisticsModel model) {
    final normalTextColor = TextStyle(color: Colors.white, fontSize: 13);
    return AspectRatio(
      aspectRatio: 343.0 / 150,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        padding: const EdgeInsets.symmetric(horizontal: 24),
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(AssetsImages.usageBackgroundPng),
            fit: BoxFit.fill,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 27),
                  const Text('可用查询次数',
                      style: TextStyle(color: Colors.white, fontSize: 13)),
                  const SizedBox(height: 8),
                  RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                            text: model.availQueryNum, style: largeTextStyle),
                        WidgetSpan(child: SizedBox(width: 4)),
                        TextSpan(
                          text: '次',
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w600),
                        ),
                      ],
                    ),
                  ),
                  Spacer(),
                  Row(
                    children: [
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(text: "永久查询次数", style: normalTextColor),
                            WidgetSpan(child: SizedBox(width: 8)),
                            TextSpan(
                              text: "${model.availPermanentNum}次",
                              style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600),
                            ),
                          ],
                        ),
                      ),
                      Spacer(),
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(text: "今日赠送可用次数", style: normalTextColor),
                            WidgetSpan(child: SizedBox(width: 8)),
                            TextSpan(
                              text: "${model.availNowTempNum}次",
                              style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 18),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNewUserCard(UsageStatisticsModel model) {
    return UsageStatisticsCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
            _buildCardTitle('新用户次数', ''),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatColumn('总查询次数', model.freeSignNum),
              _buildStatColumn('已使用次数', model.usedSignNum),
              _buildStatColumn('可用查询次数', model.availSignNum),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInviteCard(UsageStatisticsModel model) {
    return UsageStatisticsCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCardTitle('邀请好友', '已使用：${model.usedSpreadNum}次'),

          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatColumn('邀请好友人数', model.spreadCount),
              _buildStatColumn('获得查询次数', model.freeSpreadNum),
              _buildStatColumn('可用查询次数', model.availSpreadNum),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDailyGiftCard(UsageStatisticsModel model) {
    return UsageStatisticsCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCardTitle('每日赠送', '累计使用次数：${model.usedTempNum}次'),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatColumn('每日赠送次数', model.freeTempNum),
              _buildStatColumn('已使用次数', model.usedNowTempNum),
              _buildStatColumn('可用查询次数', model.availNowTempNum),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCardTitle(String title, String info) {
    return Row(
      children: [
        Container(width: 4, height: 16, color: AppColors.primary),
        const SizedBox(width: 8),
        Text(title,
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
        const Spacer(),
        Text(info,
            style: const TextStyle(color: AppColors.textColor6, fontSize: 13)),
      ],
    );
  }

  Widget _buildStatColumn(String label, String value) {
    return Column(
      children: [
        Text(value,
            style: const TextStyle(
              height: 31/22,
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: Color(0xFF222222))),
        const SizedBox(height: 4),
        Text(label,
            style: const TextStyle(fontSize: 12, color: AppColors.textColor9)),
      ],
    );
  }
}
