import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';

class UsageStatisticsCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? margin;
  final Color? color;

  const UsageStatisticsCard({
    Key? key,
    required this.child,
    this.margin,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16),
      padding: margin ?? const EdgeInsets.only(left: 12,right: 12,top: 16,bottom: 20),
      decoration: BoxDecoration(
        color: color ?? AppColors.background,
        borderRadius: BorderRadius.circular(16),
      ),
      child: child,
    );
  }
}
