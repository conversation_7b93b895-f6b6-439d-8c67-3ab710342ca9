// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'area_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$areaProviderHash() => r'9c9ffa0c45d3c99fb480a768bbfd09f425ca652c';

/// 地区列表 Provider
///
/// Copied from [AreaProvider].
@ProviderFor(AreaProvider)
final areaProviderProvider = AutoDisposeAsyncNotifierProvider<AreaProvider,
    Map<String, List<Area>>>.internal(
  AreaProvider.new,
  name: r'areaProviderProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$areaProviderHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AreaProvider = AutoDisposeAsyncNotifier<Map<String, List<Area>>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
