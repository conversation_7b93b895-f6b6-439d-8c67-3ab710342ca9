import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/location/select_area/api/area_api.dart';
import 'package:lpinyin/lpinyin.dart';
import '../model/area.dart';

part 'area_provider.g.dart';

/// 地区列表 Provider
@riverpod
class AreaProvider extends _$AreaProvider {
  @override
  FutureOr<Map<String, List<Area>>> build() async {
    final areas = await AreaApi.getAreaList();
    final areasWithPinyin = areas.data?.map((area) {
          final pinyin = PinyinHelper.getFirstWordPinyin(area.name);
          return area.copyWith(pinYin: pinyin);
        }).toList() ??
        [];
    return _groupAreasByInitial(areasWithPinyin);
  }

  Future<void> searchAreas(String keyword) async {
    // state = const AsyncValue.loading();
    try {
      final areas = await AreaApi.getAreaList(keyword: keyword);
      final areasWithPinyin = areas.data?.map((area) {
            final pinyin = PinyinHelper.getFirstWordPinyin(area.name);
            return area.copyWith(pinYin: pinyin);
          }).toList() ??
          [];
      state = AsyncValue.data(_groupAreasByInitial(areasWithPinyin));
    } catch (e, st) {
      defaultLogger.warning("根据名字($keyword)搜索地区失败, error:$e");
    }
  }

  // 辅助方法：按首字母分组地区
  Map<String, List<Area>> _groupAreasByInitial(List<Area> areas) {
    final Map<String, List<Area>> groupedAreas = {};

    for (var area in areas) {
      final initial =
          area.pinYin.isNotEmpty ? area.pinYin[0].toUpperCase() : '#';

      if (!groupedAreas.containsKey(initial)) {
        groupedAreas[initial] = [];
      }
      groupedAreas[initial]!.add(area);
    }

    // 对每个分组内的地区按拼音排序
    groupedAreas.forEach((key, value) {
      value.sort((a, b) => a.pinYin.compareTo(b.pinYin));
    });

    return groupedAreas;
  }
}
