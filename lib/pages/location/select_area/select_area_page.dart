import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/location/location_main/provider/location_main_provider.dart';
import 'provider/area_provider.dart';
import 'model/area.dart';

/// 选择地区页面
class SelectAreaPage extends ConsumerStatefulWidget {
  SelectAreaPage({super.key, required this.currentCity});

  String currentCity;

  @override
  ConsumerState<SelectAreaPage> createState() => _SelectAreaPageState();
}

class _SelectAreaPageState extends ConsumerState<SelectAreaPage> {
  final TextEditingController _searchController = TextEditingController();
  final ItemScrollController _itemScrollController = ItemScrollController();
  final ItemPositionsListener _itemPositionsListener =
      ItemPositionsListener.create();

  final List<String> _alphabet = List.generate(26, (index) {
    return String.fromCharCode('A'.codeUnitAt(0) + index);
  });

  // 用于存储当前滚动到的字母索引
  String _currentInitial = 'A';

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      ref
          .read(areaProviderProvider.notifier)
          .searchAreas(_searchController.text);
    });

    // 添加滚动监听
    _itemPositionsListener.itemPositions.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _itemPositionsListener.itemPositions.removeListener(_onScroll);
    super.dispose();
  }

  // 处理滚动事件
  void _onScroll() {
    final positions = _itemPositionsListener.itemPositions.value;
    if (positions.isEmpty) return;

    // 获取当前可见的第一个项目
    final firstItem = positions.first;
    final index = firstItem.index;

    // 获取当前分组的数据
    final areaMapAsync = ref.read(areaProviderProvider);
    areaMapAsync.whenData((groupedAreas) {
      final sortedKeys = groupedAreas.keys.toList()..sort();
      if (index >= 0 && index < sortedKeys.length) {
        final newInitial = sortedKeys[index];
        if (newInitial != _currentInitial) {
          setState(() {
            _currentInitial = newInitial;
          });
        }
      }
    });
  }

  var scrollDirection = ScrollDirection.idle;

  @override
  Widget build(BuildContext context) {
    final areaMapAsync = ref.watch(areaProviderProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('选择地区'),
      ),
      body: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Padding(
          padding: EdgeInsets.only(left: 16),
          child: Column(
            children: [
              SizedBox(height: 13),
              // 搜索框
              _searchBar(),
              _buildCurrentLocation(ref),
              Expanded(
                child: areaMapAsync.when(
                  data: (groupedAreas) {
                    if (groupedAreas.isEmpty) {
                      return const Center(
                        child: Text('没有找到相关地区'),
                      );
                    }
                    final sortedKeys = groupedAreas.keys.toList()..sort();
                    return NotificationListener<ScrollNotification>(
                      onNotification: (ScrollNotification notification) {
                        if (notification is UserScrollNotification) {
                          if (scrollDirection == ScrollDirection.idle &&
                              notification.direction != scrollDirection) {
                            print('列表开始滚动 ${notification.direction}');
                            FocusManager.instance.primaryFocus?.unfocus();
                          }
                          scrollDirection = notification.direction;
                        }
                        return false;
                      },
                      child: Row(
                        children: [
                          _areaList(sortedKeys, groupedAreas),
                          // 右侧字母导航条
                          _alphabetList(sortedKeys),
                        ],
                      ),
                    );
                  },
                  loading: () => LoadingWidget(),
                  error: (e, st) => ErrorStatusWidget(
                      text: '$e',
                      onAttempt: () {
                        // ignore: unused_result
                        ref.refresh(areaProviderProvider);
                      }),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  TextField _searchBar() {
    return TextField(
      controller: _searchController,
      style: TextStyle(
          fontSize: 14,
          color: AppColors.textColor1,
          fontWeight: FontWeight.w600),
      decoration: InputDecoration(
        isDense: true,
        hintText: '请输入要查询的内容',
        prefixIcon: const Icon(
          Icons.search,
          size: 32,
          color: Color(0xFF888888),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.0),
          borderSide: BorderSide.none,
        ),
        hintStyle: TextStyle(
            fontSize: 13,
            color: AppColors.textColor9,
            fontWeight: FontWeight.w600),
        filled: true,
        fillColor: Color(0xFFF5F5F5),
      ),
    );
  }

  Expanded _areaList(
      List<String> sortedKeys, Map<String, List<Area>> groupedAreas) {
    return Expanded(
      child: ScrollablePositionedList.builder(
        itemScrollController: _itemScrollController,
        itemPositionsListener: _itemPositionsListener,
        itemCount: sortedKeys.length,
        itemBuilder: (context, index) {
          final initial = sortedKeys[index];
          final areas = groupedAreas[initial]!;
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 字母头部
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 8, bottom: 8),
                    child: Text(
                      initial,
                      style: const TextStyle(
                          fontSize: 15.0,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textColor9),
                    ),
                  ),
                  const Divider(
                    height: 1.0,
                    color: Color(0xFFEDEDED),
                  ),
                ],
              ),
              // 地区列表
              ...areas.map((area) {
                return Column(
                  children: [
                    ListTile(
                      contentPadding: EdgeInsets.only(top: 5, bottom: 5),
                      title: HilightText(
                        text: area.name,
                        hilightText: _searchController.text,
                      ),
                      onTap: () {
                        Navigator.pop(context, area);
                      },
                    ),
                    const Divider(
                      height: 1.0,
                      color: Color(0xFFEDEDED),
                    ),
                  ],
                );
              }),
            ],
          );
        },
      ),
    );
  }

  _alphabetList(List<String> sortedKeys) {
    return Container(
      width: 15.0,
      alignment: Alignment.centerRight,
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: _alphabet.length,
        itemBuilder: (context, index) {
          final letter = _alphabet[index];
          return GestureDetector(
            onTap: () {
              final targetIndex = sortedKeys.indexOf(letter);
              if (targetIndex != -1) {
                _itemScrollController.scrollTo(
                  index: targetIndex,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeOut,
                );
              }
            },
            child: Container(
              color: letter == _currentInitial ? AppColors.orange : null,
              height: 15.0,
              width: 15,
              alignment: Alignment.center,
              child: Text(
                letter,
                style: TextStyle(
                  fontSize: 11.0,
                  fontWeight: FontWeight.bold,
                  color: letter == _currentInitial
                      ? Colors.white
                      : AppColors.textColor6,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  _buildCurrentLocation(WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20.0),
      alignment: Alignment.centerLeft,
      child: Row(
        children: [
          Icon(
            Icons.location_on_outlined,
            weight: 1,
            size: 16,
          ),
          Text(
            '当前定位城市: ${widget.currentCity}',
            style: const TextStyle(fontSize: 14.0, color: AppColors.textColor3),
          ),
        ],
      ),
    );
  }
}

class HilightText extends StatelessWidget {
  HilightText({
    super.key,
    required this.text,
    required this.hilightText,
    TextStyle? textStyle,
    TextStyle? hilightTextStyle,
  }) {
    _textStyle =
        textStyle ?? TextStyle(color: AppColors.textColor1, fontSize: 14);
    _hilightTextStyle =
        hilightTextStyle ?? TextStyle(color: AppColors.orange, fontSize: 14);
  }

  String text;
  String hilightText;

  late TextStyle _textStyle;
  late TextStyle _hilightTextStyle;

  @override
  Widget build(BuildContext context) {
    Widget titleWidget;
    if (hilightText.isEmpty) {
      titleWidget = Text(
        text,
        style: _textStyle,
      );
    } else {
      final spans = <TextSpan>[];
      int start = 0;
      while (true) {
        final index = text.indexOf(hilightText, start);
        if (index == -1) {
          if (start < text.length) {
            spans.add(TextSpan(
              text: text.substring(start),
              style: _textStyle,
            ));
          }
          break;
        }

        if (index > start) {
          spans.add(TextSpan(
            text: text.substring(start, index),
            style: _textStyle,
          ));
        }

        spans.add(TextSpan(
          text: hilightText,
          style: _hilightTextStyle,
        ));

        start = index + hilightText.length;
      }

      titleWidget = RichText(
        text: TextSpan(children: spans),
      );
    }

    return titleWidget;
  }
}
