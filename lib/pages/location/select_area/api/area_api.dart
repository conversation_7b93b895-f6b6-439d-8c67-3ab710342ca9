import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/location/select_area/model/area.dart';

class AreaApi {
  /// 获取地区列表
  static Future<BaseResponse<List<Area>>> getAreaList(
      {String keyword = ''}) async {
    final response = await SXHttpService.to
        .get('/area/list/secondary', params: {"keyword": keyword});
    return BaseResponse.fromJsonList(
      response.data,
      (data) => Area.fromJson(data as Map<String, dynamic>),
    );
  }
}
