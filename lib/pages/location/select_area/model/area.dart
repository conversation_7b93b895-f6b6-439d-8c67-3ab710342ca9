import 'package:freezed_annotation/freezed_annotation.dart';

part 'area.freezed.dart';
part 'area.g.dart';

/// 二级地区
@freezed
abstract class Area with _$Area {
  const factory Area({
    required String id,
    String? parentId,
    int? level,
    required String name,
    String? wholeName,
    double? lon,
    double? lat,
    String? cityCode,
    String? zipCode,
    String? areaCode,
    @Default('') String pinYin,
    String? simplePy, // 用于存储生成的拼音首字母
  }) = _Area;

  factory Area.fromJson(Map<String, dynamic> json) => _$AreaFromJson(json);
}
