// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'area.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Area {
  String get id;
  String? get parentId;
  int? get level;
  String get name;
  String? get wholeName;
  double? get lon;
  double? get lat;
  String? get cityCode;
  String? get zipCode;
  String? get areaCode;
  String get pinYin;
  String? get simplePy;

  /// Create a copy of Area
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AreaCopyWith<Area> get copyWith =>
      _$AreaCopyWithImpl<Area>(this as Area, _$identity);

  /// Serializes this Area to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Area &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.parentId, parentId) ||
                other.parentId == parentId) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.wholeName, wholeName) ||
                other.wholeName == wholeName) &&
            (identical(other.lon, lon) || other.lon == lon) &&
            (identical(other.lat, lat) || other.lat == lat) &&
            (identical(other.cityCode, cityCode) ||
                other.cityCode == cityCode) &&
            (identical(other.zipCode, zipCode) || other.zipCode == zipCode) &&
            (identical(other.areaCode, areaCode) ||
                other.areaCode == areaCode) &&
            (identical(other.pinYin, pinYin) || other.pinYin == pinYin) &&
            (identical(other.simplePy, simplePy) ||
                other.simplePy == simplePy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, parentId, level, name,
      wholeName, lon, lat, cityCode, zipCode, areaCode, pinYin, simplePy);

  @override
  String toString() {
    return 'Area(id: $id, parentId: $parentId, level: $level, name: $name, wholeName: $wholeName, lon: $lon, lat: $lat, cityCode: $cityCode, zipCode: $zipCode, areaCode: $areaCode, pinYin: $pinYin, simplePy: $simplePy)';
  }
}

/// @nodoc
abstract mixin class $AreaCopyWith<$Res> {
  factory $AreaCopyWith(Area value, $Res Function(Area) _then) =
      _$AreaCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String? parentId,
      int? level,
      String name,
      String? wholeName,
      double? lon,
      double? lat,
      String? cityCode,
      String? zipCode,
      String? areaCode,
      String pinYin,
      String? simplePy});
}

/// @nodoc
class _$AreaCopyWithImpl<$Res> implements $AreaCopyWith<$Res> {
  _$AreaCopyWithImpl(this._self, this._then);

  final Area _self;
  final $Res Function(Area) _then;

  /// Create a copy of Area
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? parentId = freezed,
    Object? level = freezed,
    Object? name = null,
    Object? wholeName = freezed,
    Object? lon = freezed,
    Object? lat = freezed,
    Object? cityCode = freezed,
    Object? zipCode = freezed,
    Object? areaCode = freezed,
    Object? pinYin = null,
    Object? simplePy = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      parentId: freezed == parentId
          ? _self.parentId
          : parentId // ignore: cast_nullable_to_non_nullable
              as String?,
      level: freezed == level
          ? _self.level
          : level // ignore: cast_nullable_to_non_nullable
              as int?,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      wholeName: freezed == wholeName
          ? _self.wholeName
          : wholeName // ignore: cast_nullable_to_non_nullable
              as String?,
      lon: freezed == lon
          ? _self.lon
          : lon // ignore: cast_nullable_to_non_nullable
              as double?,
      lat: freezed == lat
          ? _self.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as double?,
      cityCode: freezed == cityCode
          ? _self.cityCode
          : cityCode // ignore: cast_nullable_to_non_nullable
              as String?,
      zipCode: freezed == zipCode
          ? _self.zipCode
          : zipCode // ignore: cast_nullable_to_non_nullable
              as String?,
      areaCode: freezed == areaCode
          ? _self.areaCode
          : areaCode // ignore: cast_nullable_to_non_nullable
              as String?,
      pinYin: null == pinYin
          ? _self.pinYin
          : pinYin // ignore: cast_nullable_to_non_nullable
              as String,
      simplePy: freezed == simplePy
          ? _self.simplePy
          : simplePy // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _Area implements Area {
  const _Area(
      {required this.id,
      this.parentId,
      this.level,
      required this.name,
      this.wholeName,
      this.lon,
      this.lat,
      this.cityCode,
      this.zipCode,
      this.areaCode,
      this.pinYin = '',
      this.simplePy});
  factory _Area.fromJson(Map<String, dynamic> json) => _$AreaFromJson(json);

  @override
  final String id;
  @override
  final String? parentId;
  @override
  final int? level;
  @override
  final String name;
  @override
  final String? wholeName;
  @override
  final double? lon;
  @override
  final double? lat;
  @override
  final String? cityCode;
  @override
  final String? zipCode;
  @override
  final String? areaCode;
  @override
  @JsonKey()
  final String pinYin;
  @override
  final String? simplePy;

  /// Create a copy of Area
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AreaCopyWith<_Area> get copyWith =>
      __$AreaCopyWithImpl<_Area>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AreaToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Area &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.parentId, parentId) ||
                other.parentId == parentId) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.wholeName, wholeName) ||
                other.wholeName == wholeName) &&
            (identical(other.lon, lon) || other.lon == lon) &&
            (identical(other.lat, lat) || other.lat == lat) &&
            (identical(other.cityCode, cityCode) ||
                other.cityCode == cityCode) &&
            (identical(other.zipCode, zipCode) || other.zipCode == zipCode) &&
            (identical(other.areaCode, areaCode) ||
                other.areaCode == areaCode) &&
            (identical(other.pinYin, pinYin) || other.pinYin == pinYin) &&
            (identical(other.simplePy, simplePy) ||
                other.simplePy == simplePy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, parentId, level, name,
      wholeName, lon, lat, cityCode, zipCode, areaCode, pinYin, simplePy);

  @override
  String toString() {
    return 'Area(id: $id, parentId: $parentId, level: $level, name: $name, wholeName: $wholeName, lon: $lon, lat: $lat, cityCode: $cityCode, zipCode: $zipCode, areaCode: $areaCode, pinYin: $pinYin, simplePy: $simplePy)';
  }
}

/// @nodoc
abstract mixin class _$AreaCopyWith<$Res> implements $AreaCopyWith<$Res> {
  factory _$AreaCopyWith(_Area value, $Res Function(_Area) _then) =
      __$AreaCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String? parentId,
      int? level,
      String name,
      String? wholeName,
      double? lon,
      double? lat,
      String? cityCode,
      String? zipCode,
      String? areaCode,
      String pinYin,
      String? simplePy});
}

/// @nodoc
class __$AreaCopyWithImpl<$Res> implements _$AreaCopyWith<$Res> {
  __$AreaCopyWithImpl(this._self, this._then);

  final _Area _self;
  final $Res Function(_Area) _then;

  /// Create a copy of Area
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? parentId = freezed,
    Object? level = freezed,
    Object? name = null,
    Object? wholeName = freezed,
    Object? lon = freezed,
    Object? lat = freezed,
    Object? cityCode = freezed,
    Object? zipCode = freezed,
    Object? areaCode = freezed,
    Object? pinYin = null,
    Object? simplePy = freezed,
  }) {
    return _then(_Area(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      parentId: freezed == parentId
          ? _self.parentId
          : parentId // ignore: cast_nullable_to_non_nullable
              as String?,
      level: freezed == level
          ? _self.level
          : level // ignore: cast_nullable_to_non_nullable
              as int?,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      wholeName: freezed == wholeName
          ? _self.wholeName
          : wholeName // ignore: cast_nullable_to_non_nullable
              as String?,
      lon: freezed == lon
          ? _self.lon
          : lon // ignore: cast_nullable_to_non_nullable
              as double?,
      lat: freezed == lat
          ? _self.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as double?,
      cityCode: freezed == cityCode
          ? _self.cityCode
          : cityCode // ignore: cast_nullable_to_non_nullable
              as String?,
      zipCode: freezed == zipCode
          ? _self.zipCode
          : zipCode // ignore: cast_nullable_to_non_nullable
              as String?,
      areaCode: freezed == areaCode
          ? _self.areaCode
          : areaCode // ignore: cast_nullable_to_non_nullable
              as String?,
      pinYin: null == pinYin
          ? _self.pinYin
          : pinYin // ignore: cast_nullable_to_non_nullable
              as String,
      simplePy: freezed == simplePy
          ? _self.simplePy
          : simplePy // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
