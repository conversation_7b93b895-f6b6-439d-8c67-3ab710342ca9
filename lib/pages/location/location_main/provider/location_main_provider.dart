import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/components/amap/model/amap_tipps.dart';
import 'package:zrreport/common/index.dart';
import 'dart:async';

part 'location_main_provider.freezed.dart';
part 'location_main_provider.g.dart';

@freezed
abstract class LocationMainState with _$LocationMainState {
  const factory LocationMainState({
    required String cityCode,
    required String cityName,
    required String address,
    required List<AmapTip> tips,
  }) = _LocationMainState;
}

@riverpod
class LocationMain extends _$LocationMain {
  Timer? _debounceTimer;
  static const Duration _debounceDelay = Duration(milliseconds: 500);

  LocationDetail? systemLocation;

  @override
  LocationMainState build(LocationDetail? initialLocation) {
    systemLocation = initialLocation;
    ref.onDispose(() {
      _debounceTimer?.cancel();
    });
    return LocationMainState(
      cityName: initialLocation?.cityName ?? '',
      cityCode: initialLocation?.adCode ?? '',
      address: initialLocation?.townName ?? '',
      tips: [],
    );
  }

  /// 搜索地点提示（带防抖机制）
  Future<void> searchTips(String keyword) async {
    // 取消之前的定时器
    _debounceTimer?.cancel();

    // 如果关键词为空，直接清空结果
    if (keyword.trim().isEmpty) {
      resetTips();
      return;
    }

    // 设置新的定时器
    _debounceTimer = Timer(_debounceDelay, () async {
      try {
        final resp = await AmapService()
            .inputtips(city: state.cityCode, keywords: keyword);
        state = state.copyWith(
            tips: resp.tips.where((tip) => tip.location.isNotEmpty).toList());
      } catch (e) {
        defaultLogger.error('搜索地点提示失败: $e');
      }
    });
  }

  void resetTips() {
    state = state.copyWith(tips: []);
  }

  void update(String cityCode, String cityName) {
    systemLocation = null;
    state = state.copyWith(cityName: cityName, cityCode: cityCode, address: '');
  }

  /// 获取地理位置信息
  Future<AmapLocationResponse?> updateSystemLocation(
      double longitude, double latitude) async {
    try {
      final resp = await AmapService()
          .regeocode(longitude: longitude, latitude: latitude);
      state = state.copyWith(
        cityName: resp.regeocode.addressComponent.city,
        cityCode: resp.regeocode.addressComponent.citycode,
        address: resp.regeocode.address,
      );

      systemLocation = LocationDetail(
        longitude: longitude,
        latitude: latitude,
        cityName: resp.regeocode.addressComponent.city,
        adCode: resp.regeocode.addressComponent.citycode,
        townName: resp.regeocode.address,
      );
    } catch (e) {
      print('获取地理位置信息失败: $e');
      return null;
    }
    return null;
  }
}
