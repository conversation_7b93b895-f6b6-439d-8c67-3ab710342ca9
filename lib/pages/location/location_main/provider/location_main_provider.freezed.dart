// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location_main_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LocationMainState {
  String get cityCode;
  String get cityName;
  String get address;
  List<AmapTip> get tips;

  /// Create a copy of LocationMainState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LocationMainStateCopyWith<LocationMainState> get copyWith =>
      _$LocationMainStateCopyWithImpl<LocationMainState>(
          this as LocationMainState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LocationMainState &&
            (identical(other.cityCode, cityCode) ||
                other.cityCode == cityCode) &&
            (identical(other.cityName, cityName) ||
                other.cityName == cityName) &&
            (identical(other.address, address) || other.address == address) &&
            const DeepCollectionEquality().equals(other.tips, tips));
  }

  @override
  int get hashCode => Object.hash(runtimeType, cityCode, cityName, address,
      const DeepCollectionEquality().hash(tips));

  @override
  String toString() {
    return 'LocationMainState(cityCode: $cityCode, cityName: $cityName, address: $address, tips: $tips)';
  }
}

/// @nodoc
abstract mixin class $LocationMainStateCopyWith<$Res> {
  factory $LocationMainStateCopyWith(
          LocationMainState value, $Res Function(LocationMainState) _then) =
      _$LocationMainStateCopyWithImpl;
  @useResult
  $Res call(
      {String cityCode, String cityName, String address, List<AmapTip> tips});
}

/// @nodoc
class _$LocationMainStateCopyWithImpl<$Res>
    implements $LocationMainStateCopyWith<$Res> {
  _$LocationMainStateCopyWithImpl(this._self, this._then);

  final LocationMainState _self;
  final $Res Function(LocationMainState) _then;

  /// Create a copy of LocationMainState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cityCode = null,
    Object? cityName = null,
    Object? address = null,
    Object? tips = null,
  }) {
    return _then(_self.copyWith(
      cityCode: null == cityCode
          ? _self.cityCode
          : cityCode // ignore: cast_nullable_to_non_nullable
              as String,
      cityName: null == cityName
          ? _self.cityName
          : cityName // ignore: cast_nullable_to_non_nullable
              as String,
      address: null == address
          ? _self.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      tips: null == tips
          ? _self.tips
          : tips // ignore: cast_nullable_to_non_nullable
              as List<AmapTip>,
    ));
  }
}

/// @nodoc

class _LocationMainState implements LocationMainState {
  const _LocationMainState(
      {required this.cityCode,
      required this.cityName,
      required this.address,
      required final List<AmapTip> tips})
      : _tips = tips;

  @override
  final String cityCode;
  @override
  final String cityName;
  @override
  final String address;
  final List<AmapTip> _tips;
  @override
  List<AmapTip> get tips {
    if (_tips is EqualUnmodifiableListView) return _tips;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tips);
  }

  /// Create a copy of LocationMainState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LocationMainStateCopyWith<_LocationMainState> get copyWith =>
      __$LocationMainStateCopyWithImpl<_LocationMainState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LocationMainState &&
            (identical(other.cityCode, cityCode) ||
                other.cityCode == cityCode) &&
            (identical(other.cityName, cityName) ||
                other.cityName == cityName) &&
            (identical(other.address, address) || other.address == address) &&
            const DeepCollectionEquality().equals(other._tips, _tips));
  }

  @override
  int get hashCode => Object.hash(runtimeType, cityCode, cityName, address,
      const DeepCollectionEquality().hash(_tips));

  @override
  String toString() {
    return 'LocationMainState(cityCode: $cityCode, cityName: $cityName, address: $address, tips: $tips)';
  }
}

/// @nodoc
abstract mixin class _$LocationMainStateCopyWith<$Res>
    implements $LocationMainStateCopyWith<$Res> {
  factory _$LocationMainStateCopyWith(
          _LocationMainState value, $Res Function(_LocationMainState) _then) =
      __$LocationMainStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String cityCode, String cityName, String address, List<AmapTip> tips});
}

/// @nodoc
class __$LocationMainStateCopyWithImpl<$Res>
    implements _$LocationMainStateCopyWith<$Res> {
  __$LocationMainStateCopyWithImpl(this._self, this._then);

  final _LocationMainState _self;
  final $Res Function(_LocationMainState) _then;

  /// Create a copy of LocationMainState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? cityCode = null,
    Object? cityName = null,
    Object? address = null,
    Object? tips = null,
  }) {
    return _then(_LocationMainState(
      cityCode: null == cityCode
          ? _self.cityCode
          : cityCode // ignore: cast_nullable_to_non_nullable
              as String,
      cityName: null == cityName
          ? _self.cityName
          : cityName // ignore: cast_nullable_to_non_nullable
              as String,
      address: null == address
          ? _self.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      tips: null == tips
          ? _self._tips
          : tips // ignore: cast_nullable_to_non_nullable
              as List<AmapTip>,
    ));
  }
}

// dart format on
