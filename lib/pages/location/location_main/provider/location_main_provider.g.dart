// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'location_main_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$locationMainHash() => r'5a15ed2c57076a7e1ae2730fea64e9cc44848352';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$LocationMain
    extends BuildlessAutoDisposeNotifier<LocationMainState> {
  late final LocationDetail? initialLocation;

  LocationMainState build(
    LocationDetail? initialLocation,
  );
}

/// See also [LocationMain].
@ProviderFor(LocationMain)
const locationMainProvider = LocationMainFamily();

/// See also [LocationMain].
class LocationMainFamily extends Family<LocationMainState> {
  /// See also [LocationMain].
  const LocationMainFamily();

  /// See also [LocationMain].
  LocationMainProvider call(
    LocationDetail? initialLocation,
  ) {
    return LocationMainProvider(
      initialLocation,
    );
  }

  @override
  LocationMainProvider getProviderOverride(
    covariant LocationMainProvider provider,
  ) {
    return call(
      provider.initialLocation,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'locationMainProvider';
}

/// See also [LocationMain].
class LocationMainProvider
    extends AutoDisposeNotifierProviderImpl<LocationMain, LocationMainState> {
  /// See also [LocationMain].
  LocationMainProvider(
    LocationDetail? initialLocation,
  ) : this._internal(
          () => LocationMain()..initialLocation = initialLocation,
          from: locationMainProvider,
          name: r'locationMainProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$locationMainHash,
          dependencies: LocationMainFamily._dependencies,
          allTransitiveDependencies:
              LocationMainFamily._allTransitiveDependencies,
          initialLocation: initialLocation,
        );

  LocationMainProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.initialLocation,
  }) : super.internal();

  final LocationDetail? initialLocation;

  @override
  LocationMainState runNotifierBuild(
    covariant LocationMain notifier,
  ) {
    return notifier.build(
      initialLocation,
    );
  }

  @override
  Override overrideWith(LocationMain Function() create) {
    return ProviderOverride(
      origin: this,
      override: LocationMainProvider._internal(
        () => create()..initialLocation = initialLocation,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        initialLocation: initialLocation,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<LocationMain, LocationMainState>
      createElement() {
    return _LocationMainProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is LocationMainProvider &&
        other.initialLocation == initialLocation;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, initialLocation.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin LocationMainRef on AutoDisposeNotifierProviderRef<LocationMainState> {
  /// The parameter `initialLocation` of this provider.
  LocationDetail? get initialLocation;
}

class _LocationMainProviderElement
    extends AutoDisposeNotifierProviderElement<LocationMain, LocationMainState>
    with LocationMainRef {
  _LocationMainProviderElement(super.provider);

  @override
  LocationDetail? get initialLocation =>
      (origin as LocationMainProvider).initialLocation;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
