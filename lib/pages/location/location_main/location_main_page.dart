import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/branch/provider/location_provider.dart';
import 'package:zrreport/pages/location/location_history/location_history_page.dart';
import 'package:zrreport/pages/system/main/main_page_provider.dart';

import '../location_history/service/location_history_service.dart';
import '../select_area/model/area.dart';
import '../select_area/select_area_page.dart';
import 'provider/location_main_provider.dart';

/// 位置历史记录页面
class LocationMainPage extends ConsumerWidget {
  LocationMainPage({super.key, this.currentLocation});

  final LocationDetail? currentLocation;

  final TextEditingController _searchController = TextEditingController();
  final ScrollDirection scrollDirection = ScrollDirection.idle;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mainPageState = ref.watch(locationMainProvider(currentLocation));
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('选择地区'),
        ),
        body: Column(
          children: [
            _searchBar(context, ref),
            if (mainPageState.tips.isNotEmpty)
              Expanded(child: _buildSearchList(ref))
            else
              Expanded(
                child: Column(
                  children: [
                    // 当前的位置
                    _location(ref),
                    // 历史记录列表
                    SizedBox(height: 16),
                    Divider(),
                    SizedBox(height: 16),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        children: [
                          const Icon(Icons.location_on_outlined,
                              color: Color(0xFF3D3d3d)),
                          SizedBox(width: 5),
                          Text(
                            '历史记录',
                            style: TextStyle(
                                color: AppColors.textColor6, fontSize: 14),
                          ),
                        ],
                      ),
                    ),

                    Expanded(child: LocationHistoryPage()),
                  ],
                ),
              )
          ],
        ),
      ),
    );
  }

  _searchBar(BuildContext context, WidgetRef ref) {
    final state = ref.read(locationMainProvider(currentLocation));
    return Padding(
      padding: const EdgeInsets.only(left: 15, right: 15, top: 10),
      child: Row(
        children: [
          GestureDetector(
            onTap: () async {
              final selectedArea = await Navigator.push<Area>(
                context,
                MaterialPageRoute(
                  builder: (context) => SelectAreaPage(
                    currentCity: '',
                  ),
                ),
              );
              if (selectedArea != null &&
                  (selectedArea.areaCode ?? '').isNotEmpty) {
                final notifier =
                    ref.read(locationMainProvider(currentLocation).notifier);
                notifier.update(selectedArea.areaCode ?? "", selectedArea.name);
              }
            },
            child: Row(
              children: [
                const Icon(Icons.location_on_outlined, color: Colors.black54),
                const SizedBox(width: 8.0),
                Text(
                  state.cityName,
                  style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87),
                  overflow: TextOverflow.ellipsis,
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                )
              ],
            ),
          ),
          const SizedBox(width: 20.0),
          Expanded(
              child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: StatefulBuilder(builder: (context, _setState) {
              return TextField(
                controller: _searchController,
                style: TextStyle(
                    color: AppColors.textColor3,
                    fontSize: 13,
                    fontWeight: FontWeight.w600),
                decoration: InputDecoration(
                  hintText: '请输入要查询的内容',
                  hintStyle: TextStyle(
                      color: AppColors.textColor9,
                      fontSize: 13,
                      fontWeight: FontWeight.w600),
                  border: InputBorder.none,
                  icon: Icon(Icons.search, color: AppColors.textColor8),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? GestureDetector(
                          onTap: () {
                            _searchController.text = '';
                            ref
                                .read(locationMainProvider(currentLocation)
                                    .notifier)
                                .resetTips();
                          },
                          child: Icon(
                            Icons.cancel,
                            size: 16,
                            color: AppColors.textColor8,
                          ),
                        )
                      : null,
                  suffixIconConstraints:
                      BoxConstraints.tightFor(width: 16, height: 16),
                ),
                onChanged: (value) {
                  _setState(() {});
                  ref
                      .read(locationMainProvider(currentLocation).notifier)
                      .searchTips(value);
                },
              );
            }),
          )),
        ],
      ),
    );
  }

  _location(WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.only(left: 15, right: 15, top: 10),
      child: Row(
        children: [
          Builder(builder: (context) {
            return Expanded(
              child: GestureDetector(
                onTap: () => onClickAddress(context, ref),
                child: Text(
                  ref.watch(locationMainProvider(currentLocation)).address,
                  style: const TextStyle(
                      fontSize: 15, color: AppColors.textColor3),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            );
          }),
          SizedBox(width: 16),
          GestureDetector(
            onTap: () => onRelocate(ref),
            child: Row(
              children: [
                Icon(Icons.my_location, color: AppColors.orange),
                const SizedBox(width: 4.0),
                Text(
                  '重新定位',
                  style: TextStyle(color: AppColors.orange),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  _buildSearchList(WidgetRef ref) {
    if (_searchController.text.isEmpty) {
      ref.read(locationMainProvider(currentLocation).notifier).resetTips();
      return;
    }
    final mainPageState = ref.read(locationMainProvider(currentLocation));
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification notification) {
        if (notification is UserScrollNotification) {
          if (scrollDirection == ScrollDirection.idle &&
              notification.direction != scrollDirection) {
            print('列表开始滚动 ${notification.direction}');
            FocusManager.instance.primaryFocus?.unfocus();
          }
        }
        return false;
      },
      child: ListView.separated(
        key: ValueKey(_searchController.text),
        itemCount: mainPageState.tips.length,
        separatorBuilder: (BuildContext context, int index) =>
            Divider(height: 1.0, color: AppColors.dividerColor),
        padding: EdgeInsets.symmetric(horizontal: 15),
        itemBuilder: (context, index) {
          final tip = mainPageState.tips[index];
          return ListTile(
            onTap: () {
              final location = tip.location2D;
              if (location != null) {
                var locationDetail = LocationDetail(
                    longitude: location.longitude,
                    latitude: location.latitude,
                    cityName: mainPageState.cityName, // 可能不准确
                    adCode: tip.adcode,
                    townName: tip.name);
                LocationDetailService.addDetail(locationDetail);
                Navigator.of(context).pop(locationDetail);
              }
            },
            title: HilightText(
              text: tip.name,
              hilightText: _searchController.text,
              textStyle: TextStyle(
                  color: AppColors.textColor3,
                  fontSize: 16,
                  fontWeight: FontWeight.w600),
              hilightTextStyle: TextStyle(
                  color: AppColors.orange,
                  fontSize: 16,
                  fontWeight: FontWeight.w600),
            ),
            // title: Text(
            //   tip.name,
            //   style: const TextStyle(fontSize: 16),
            // ),
          );
        },
      ),
    );
  }

  Future<void> onRelocate(WidgetRef ref) async {
    final notifier = ref.read(locationNotifierProvider.notifier);
    final location = (await notifier.getCurrentLocation());
    if (location != null) {
      final latitude = location.latitude;
      final longitude = location.longitude;
      ref
          .read(locationMainProvider(currentLocation).notifier)
          .updateSystemLocation(longitude, latitude);
    }
  }

  onClickAddress(BuildContext context, WidgetRef ref) {
    final locationMain =
        ref.read(locationMainProvider(currentLocation).notifier);
    if (locationMain.systemLocation != null) {
      Navigator.of(context).pop(locationMain.systemLocation);
    }
  }
}
