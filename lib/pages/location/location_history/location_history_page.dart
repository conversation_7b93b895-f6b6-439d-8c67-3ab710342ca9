import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/common/models/location_detail/location_detail.dart';

import 'provider/location_history_provider.dart';

/// 位置详情页面
class LocationHistoryPage extends ConsumerWidget {
  const LocationHistoryPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final locationDetailAsync = ref.watch(locationHistoryNotifierProvider);

    return locationDetailAsync.when(
      data: (locations) {
        if (locations.isEmpty) {
          return const Center(
            child: Text('暂无位置详情'),
          );
        }
        return ListView.separated(
          separatorBuilder: (BuildContext context, int index) =>
              Divider(height: 1.0, color: AppColors.dividerColor),
          itemCount: locations.length,
          itemBuilder: (context, index) {
            final location = locations[index];
            return _buildLocationItem(context, ref, location);
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Text('加载失败: $error'),
      ),
    );
  }

  Widget _buildLocationItem(
    BuildContext context,
    WidgetRef ref,
    LocationDetail location,
  ) {
    return ListTile(
      title: Text(
        ' ${location.townName}',
        style: TextStyle(
            fontSize: 16,
            color: AppColors.textColor3,
            fontWeight: FontWeight.bold),
      ),
      trailing: IconButton(
        icon: const Icon(Icons.delete_outline),
        onPressed: () => _showDeleteDialog(context, ref, location),
      ),
      onTap: () {
        Navigator.pop(context, location);
      },
    );
  }

  Future<void> _showDeleteDialog(
    BuildContext context,
    WidgetRef ref,
    LocationDetail location,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: const Text('确定要删除这条位置详情吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await ref
          .read(locationHistoryNotifierProvider.notifier)
          .deleteLocation(location.id);
    }
  }
}
