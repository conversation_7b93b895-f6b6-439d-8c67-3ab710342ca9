import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:zrreport/common/models/location_detail/location_detail.dart';

/// 位置详情服务类
class LocationDetailService {
  static const String _tableName = 'location_detail';
  static const int _maxItems = 10;
  static Database? _database;

  /// 获取数据库实例
  static Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  /// 初始化数据库
  static Future<Database> _initDatabase() async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, 'location_detail.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: (db, version) async {
        await db.execute('''
          CREATE TABLE $_tableName(
            id TEXT PRIMARY KEY,
            longitude REAL NOT NULL,
            latitude REAL NOT NULL,
            city_name TEXT NOT NULL,
            town_name TEXT NOT NULL,
            ad_code TEXT NOT NULL,
            created_at INTEGER
          )
        ''');
      },
    );
  }

  /// 获取所有位置详情
  static Future<List<LocationDetail>> getAllDetails() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      orderBy: 'created_at DESC',
      limit: _maxItems,
    );

    return List.generate(maps.length, (i) {
      return LocationDetail(
        id: maps[i]['id'] as String? ?? '',
        longitude: maps[i]['longitude'] as double,
        latitude: maps[i]['latitude'] as double,
        cityName: maps[i]['city_name'] as String? ?? '',
        townName: maps[i]['town_name'] as String? ?? '',
        adCode: maps[i]['ad_code'] as String? ?? '',
        createdAt: maps[i]['created_at'] != null
            ? DateTime.fromMillisecondsSinceEpoch(maps[i]['created_at'] as int)
            : null,
      );
    });
  }

  /// 添加位置详情
  static Future<void> addDetail(LocationDetail location) async {
    final db = await database;

    // 检查是否已存在相同位置
    final existingLocations = await db.query(
      _tableName,
      where: 'latitude = ? AND longitude = ?',
      whereArgs: [location.latitude, location.longitude],
    );

    if (existingLocations.isNotEmpty) {
      // 如果已存在，则删除旧记录
      await db.delete(
        _tableName,
        where: 'id = ?',
        whereArgs: [existingLocations.first['id']],
      );
    }

    // 添加新记录
    await db.insert(
      _tableName,
      {
        'id': location.id.isEmpty
            ? DateTime.now().millisecondsSinceEpoch.toString()
            : location.id,
        'longitude': location.longitude,
        'latitude': location.latitude,
        'city_name': location.cityName,
        'town_name': location.townName,
        'ad_code': location.adCode,
        'created_at':
            (location.createdAt ?? DateTime.now()).millisecondsSinceEpoch,
      },
    );

    // 检查并删除超出限制的记录
    final count = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM $_tableName'),
    );

    if (count != null && count > _maxItems) {
      final oldestRecords = await db.query(
        _tableName,
        orderBy: 'created_at ASC',
        limit: count - _maxItems,
      );

      for (var record in oldestRecords) {
        await db.delete(
          _tableName,
          where: 'id = ?',
          whereArgs: [record['id']],
        );
      }
    }
  }

  /// 删除位置详情
  static Future<void> deleteDetail(String id) async {
    final db = await database;
    await db.delete(
      _tableName,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// 清空所有位置详情
  static Future<void> clearAll() async {
    final db = await database;
    await db.delete(_tableName);
  }
}
