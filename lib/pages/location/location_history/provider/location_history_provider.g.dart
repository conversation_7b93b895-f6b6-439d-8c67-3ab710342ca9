// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'location_history_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$locationHistoryNotifierHash() =>
    r'9d13418899cad4d29c985080b6dceee18dc448b3';

/// See also [LocationHistoryNotifier].
@ProviderFor(LocationHistoryNotifier)
final locationHistoryNotifierProvider = AutoDisposeAsyncNotifierProvider<
    LocationHistoryNotifier, List<LocationDetail>>.internal(
  LocationHistoryNotifier.new,
  name: r'locationHistoryNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$locationHistoryNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LocationHistoryNotifier
    = AutoDisposeAsyncNotifier<List<LocationDetail>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
