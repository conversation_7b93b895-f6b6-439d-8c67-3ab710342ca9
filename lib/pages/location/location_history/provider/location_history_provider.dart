import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/models/location_detail/location_detail.dart';

import '../service/location_history_service.dart';

part 'location_history_provider.g.dart';

@riverpod
class LocationHistoryNotifier extends _$LocationHistoryNotifier {
  @override
  FutureOr<List<LocationDetail>> build() async {
    // 获取所有位置详情
    return LocationDetailService.getAllDetails();
  }

  Future<void> addLocation(LocationDetail location) async {
    state = const AsyncValue.loading();
    try {
      await LocationDetailService.addDetail(location);
      state = AsyncValue.data(await LocationDetailService.getAllDetails());
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }

  Future<void> deleteLocation(String id) async {
    state = const AsyncValue.loading();
    try {
      await LocationDetailService.deleteDetail(id);
      state = AsyncValue.data(await LocationDetailService.getAllDetails());
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }

  Future<void> loadMore() async {
    if (state.isLoading) return;

    try {
      // 由于我们使用 SQLite 本地存储，所有数据都已经加载
      // 这里不需要额外的加载操作
      state = AsyncValue.data(await LocationDetailService.getAllDetails());
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }
}
