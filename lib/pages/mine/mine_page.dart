import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:zrreport/common/index.dart';

import 'widgets/usage_statistics.dart';

class MinePage extends ConsumerWidget {
  const MinePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {

    // Future(() {
    //   if (context.mounted) {
    //     ref.read(usageStatisticsNotifierProvider.notifier).refresh();
    //     // ref.read(userProfileNotifierProvider.notifier).refreshUserProfile();
    //   }
    // });
    
    final backgroundColor = Color(0xFFFAF8F9);
    return Scaffold(
        appBar: AppBar(
          // toolbarHeight: 0,
          title: Text('我的'),
          elevation: 0,
          backgroundColor: backgroundColor,
        ),
        backgroundColor: backgroundColor,
        body: Padding(
          padding: const EdgeInsets.only(left: 16, right: 16),
          child: ListView(
            children: [
              _buildUserWidget(ref),
              Si<PERSON><PERSON><PERSON>(height: 20),
              UsageStatistics(),
              SizedBox(height: 10),
              _buildMenus(),
            ],
          ),
        ));
  }

  _buildUserWidget(WidgetRef ref) {
    final profileProvider = ref.watch(userProfileNotifierProvider);
    return _userProfileWidget(profileProvider);
  }

  _userProfileWidget(UserProfile? profile) {
    final nickStyle = TextStyle(
        color: AppColors.textColor3,
        fontSize: 17,
        fontWeight: FontWeight.w600,
        height: 24 / 17);

    final phoneStyle =
        TextStyle(color: AppColors.textColor6, fontSize: 13, height: 18 / 13);
    return Row(
      children: [
        AvatarWidget(size: 60, imageUrl: profile?.icon),
        SizedBox(width: 12),
        Expanded(
          child: GestureDetector(
            onTap: () => Get.toNamed(RouteNames.mineEditProfile),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(profile?.nickname ?? '',
                          style: nickStyle, overflow: TextOverflow.ellipsis),
                    ),
                    SizedBox(width: 5),
                    Icon(Icons.arrow_forward_ios, size: 16),
                  ],
                ),
                SizedBox(height: 4),
                Text('资料完整，修改资料请进入详情…', style: phoneStyle),
              ],
            ),
          ),
        )
      ],
    );
  }

  Widget _buildMenus() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(15.0),
      child: Column(
        children: [
          _menuItem("匹配列表", () => Get.toNamed(RouteNames.matchList)),
          Divider(height: 1),
          _menuItem("维护地区查询", () => Get.toNamed(RouteNames.listProvince)),
          Divider(height: 1),
          _menuItem("在线客服", () => launchUrlString(Urls.weixinCustomerService)),
          Divider(height: 1),
          _menuItem("关于我们", () => Get.toNamed(RouteNames.about)),
          Divider(height: 1),
          _menuItem("意见反馈", () => Get.toNamed(RouteNames.feedback)),
          Divider(height: 1),
          _menuItem("隐私政策", () => gotoWeb(UrlPath.privacyPolicy)),
          Divider(height: 1),
          _menuItem("用户服务协议", () => gotoWeb(UrlPath.userAgreement)),
        ],
      ),
    );
  }

  Widget _menuItem(String title, VoidCallback action) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        action();
      },
      child: Container(
        color: Colors.white,
        height: 62,
        child: Row(
          children: [
            SizedBox(width: 20),
            Text(
              title,
              style: TextStyle(color: AppColors.textColor3, fontSize: 17),
            ),
            Spacer(),
            Icon(
              Icons.arrow_forward_ios,
              color: AppColors.textColor9,
              size: 15,
            ),
            SizedBox(width: 20),
          ],
        ),
      ),
    );
  }
}
