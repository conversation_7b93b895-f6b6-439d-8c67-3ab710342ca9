import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get/get_connect/http/src/utils/utils.dart';
import 'package:zrreport/common/index.dart';

import 'provider.dart';

class MemberPage extends ConsumerWidget {
  const MemberPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('我的会员'),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      backgroundColor: Colors.black,
      body: Column(
        children: [
          const SizedBox(height: 20),
          _buildGoldMemberCard(ref),
          const SizedBox(height: 28),
          Expanded(
            child: ClipRRect(
              borderRadius: BorderRadius.all(
                Radius.circular(16),
              ),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                ),
                padding: EdgeInsets.only(top: 20),
                child: <PERSON><PERSON><PERSON>(
                  child: <PERSON><PERSON>iew(
                    padding: EdgeInsets.symmetric(horizontal: 20),
                    children: [
                      _buildMemberBenefitsCard(),
                      const SizedBox(height: 16),
                      _buildHowToGetQueryCard(),
                      const SizedBox(height: 24),
                      _buildInviteButton(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGoldMemberCard(WidgetRef ref) {
    final stateAsync = ref.watch(spreadTotalProvider);
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      margin: EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFF9E7B2), Color(0xFFF6D06F)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.verified,
                        color: Color(0xFFF6B800), size: 28),
                    const SizedBox(width: 8),
                    const Text(
                      '黄金会员',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF7A4E00),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 6),
                const Text(
                  '黄金会员享受多种权益',
                  style: TextStyle(
                    fontSize: 15,
                    color: Color(0xFF7A4E00),
                  ),
                ),
                const SizedBox(height: 52),
                stateAsync.when(
                    data: _count,
                    error: (_, st) => _count(null),
                    loading: () => _count(null))
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _count(int? count) {
    return Text(
      '当前已邀请人数${count ?? "--"}人，已获得${count != null ? count * 10 : "--"}免费查询机会',
      style: TextStyle(
        fontSize: 14,
        color: Color(0xFF7A4E00),
      ),
    );
  }

  Widget _buildMemberBenefitsCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(18),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '黄金会员专属权益',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF222222),
            ),
          ),
          const SizedBox(height: 15),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildBenefitItem(
                image: AssetsImages.memberBenefit_1Png,
                iconColor: Color(0xFFF6B800),
                title: '税务查询',
                subtitle: '快速查询\n税务数据',
              ),
              SizedBox(width: 8),
              _buildBenefitItem(
                image: AssetsImages.memberBenefit_2Png,
                iconColor: Color(0xFFF6B800),
                title: '免费使用',
                subtitle: '免费查询',
              ),
              SizedBox(width: 8),
              _buildBenefitItem(
                image: AssetsImages.memberBenefit_3Png,
                iconColor: Color(0xFFF6B800),
                title: '无限叠加',
                subtitle: '邀请好友获得\n免费查询机会',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBenefitItem({
    required String image,
    required Color iconColor,
    required String title,
    required String subtitle,
  }) {
    return Expanded(
      flex: 1,
      child: Container(
        height: 140,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFFFDF7EF), Color(0xFFFCEEDA)],
          ),
        ),
        child: Column(
          children: [
            const SizedBox(height: 16),
            Image.asset(image, height: 37),
            const SizedBox(height: 10),
            Text(title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF423115),
                )),
            const SizedBox(height: 8),
            Text(
              subtitle,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 13,
                color: Color(0x99423115),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHowToGetQueryCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(18),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '如何获得查询次数',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF222222),
            ),
          ),
          const SizedBox(height: 16),
          _buildHowToGetItem(
            index: 1,
            title: '注册即可获得查询机会',
            desc: '账号注册成功即可获得10次免费查询机会',
          ),
          const SizedBox(height: 10),
          _buildHowToGetItem(
            index: 2,
            title: '邀请一位好友即可获得10次免费查询机会',
            desc: '目前邀请1人注册，获得10次查询记录',
          ),
          const SizedBox(height: 10),
          _buildHowToGetItem(
            index: 3,
            title: '每天登录即可获得10次限时查询机会',
            desc: '登录即可得到10次限时查询次数，每天23:59后失效',
          ),
        ],
      ),
    );
  }

  Widget _buildHowToGetItem({
    required int index,
    required String title,
    required String desc,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFFDF6E3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$index、',
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFFF6B800),
              fontWeight: FontWeight.bold,
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 15,
                    color: Color(0xFF7A4E00),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  desc,
                  style: const TextStyle(
                    fontSize: 13,
                    color: Color(0xFF7A4E00),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInviteButton() {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: ElevatedButton(
        onPressed: () {},
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          elevation: 0,
        ),
        child: Ink(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFFF9E7B2), Color(0xFFF6D06F)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(24),
          ),
          child: Container(
            alignment: Alignment.center,
            child: const Text(
              '立即邀请好友',
              style: TextStyle(
                fontSize: 18,
                color: Color(0xFF7A4E00),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
