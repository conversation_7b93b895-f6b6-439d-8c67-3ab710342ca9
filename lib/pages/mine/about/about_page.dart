import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:zrreport/common/index.dart';

class AboutView extends StatelessWidget {
  const AboutView({super.key});

  final text =
      '在数字化经济蓬勃发展、商业环境日益复杂的当下，企业对财税数据的精准分析与深度应用需求持续攀升。助融报告小程序作为一款聚焦财税领域的专业级智能分析工具，以数据驱动决策，为企业发展赋能。\n 依托强大的数据整合与分析能力，小程序可全面覆盖企业全维度信息查询。在企业基础信息层面，精准呈现注册登记、资本架构、业务范畴等核心数据；纳税信息板块，系统梳理企业纳税信用评级、税费缴纳明细及合规情况；发票信息模块支持全周期开票记录追溯，夯实财务核算基础。此外，小程序深度挖掘供应商资质信息，构建风险评估模型，同时整合工商变更动态、司法诉讼记录等关键数据，为企业风险防控提供有力支撑。\n 凭借先进算法与智能分析系统，小程序可依据多维数据自动生成标准化、结构化的专业财税报告。报告内容严谨详实、逻辑清晰，为企业战略规划、投融资决策、信用评估等场景提供可靠依据，助力企业提升运营效率与决策科学性。助融报告小程序，以专业服务与创新技术，成为企业财税管理的坚实伙伴。';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('关于'),
      ),
      body: ListView(
        children: [
          SizedBox(height: 50.h),
          Center(
            child: ClipOval(
              child: Image.asset(
                AssetsImages.appiconPng,
                width: 80,
                height: 80,
              ),
            ),
          ),
          SizedBox(height: 20.h),
          Text(
            '助融报告',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 10.h),
          FutureBuilder(
              future: PackageInfo.fromPlatform(),
              builder: (context, snapshot) {
                return Text(
                  '当前版本: v${snapshot.data?.version}(${snapshot.data?.buildNumber})',
            textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey,
                  ),
                );
              }),
          SizedBox(height: 40.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 30.w),
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.black54,
                height: 1.8,
              ),
              textAlign: TextAlign.justify,
            ),
          ),
          const Spacer(),
          // Text(
          //   '备案号: 陕ICP备2024041611号-6A',
          //   textAlign: TextAlign.center,
          //   style: TextStyle(
          //     fontSize: 12.sp,
          //     color: Colors.grey,
          //   ),
          // ),
          SizedBox(height: 30.h),
        ],
      ),
    );
  }
}
