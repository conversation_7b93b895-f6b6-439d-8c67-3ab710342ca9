import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/mine/feedback/feed_back_provider.dart';


class FeedBackPage extends ConsumerWidget {
  const FeedBackPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(feedBackProvider);
    return Scaffold(
      appBar: AppBar(
        title: const Text('意见反馈'),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: state.isSuccess ? const FeedBackSuccess() : const FeedBackForm(),
    );
  }
}

class FeedBackForm extends ConsumerWidget {
  const FeedBackForm({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(feedBackProvider);
    final notifier = ref.read(feedBackProvider.notifier);

    // 计算单个图片的宽度：(屏幕宽度 - 左右padding - 图片间距) / 图片数量
    final screenWidth = MediaQuery.of(context).size.width;
    final horizontalPadding = 32.0; // 16 * 2
    final spacing = 12.0; // 图片间距
    final imageCount = 4;
    final imageWidth =
        (screenWidth - horizontalPadding - (spacing * (imageCount - 1))) /
            imageCount;

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 图片上传区
            Row(
              children: [
                ...List.generate(state.images.length, (index) {
                  return Container(
                    margin: EdgeInsets.only(
                        right: index < imageCount - 1 ? spacing : 0),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Stack(
                        children: [
                          Container(
                            width: imageWidth,
                            height: imageWidth, // 保持1:1比例
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: CachedNetworkImage(
                              imageUrl: state.images[index],
                              fit: BoxFit.cover,
                            ),
                          ),
                          GestureDetector(
                            onTap: () => notifier.removeImage(index),
                            child: Container(
                                width: imageWidth,
                                height: imageWidth,
                                color: Colors.black.withAlpha(75),
                                alignment: Alignment.center,
                                child: Icon(
                                  Icons.delete_outlined,
                                  color: Colors.white,
                                )),
                          )
                        ],
                      ),
                    ),
                  );
                }),
                if (state.images.length < 4)
                  GestureDetector(
                    onTap: () async {
                      notifier.addImage(context);
                    },
                    child: Container(
                      width: imageWidth,
                      height: imageWidth,
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.add, size: 32, color: Colors.grey),
                            Text('（非必填）',
                                style: TextStyle(
                                    color: Colors.grey, fontSize: 12)),
                          ],
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 24),

            Divider(),
            // 文本输入区
            TextField(
              maxLines: 10,
              maxLength: 200,
              onChanged: notifier.setContent,
              decoration: const InputDecoration(
                hintText: '请详细描述您的问题或建议，我们将及时跟进解决（必填）',
                border: InputBorder.none,
                counterText: '',
              ),
              style: TextStyle(color: Color(0xFF111111), fontSize: 15),
            ),
            const SizedBox(height: 8),
            Align(
              alignment: Alignment.centerRight,
              child: Text(
                '${state.content.length}/200',
                style: const TextStyle(color: Colors.grey),
              ),
            ),
            const SizedBox(height: 32),
            // 提交按钮

            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  elevation: 0,
                  backgroundColor:
                      (state.content.trim().isEmpty || state.isLoading)
                          ? AppColors.disableBackground
                          : AppColors.primary,
                  textStyle:
                      TextStyle(fontSize: 17, fontWeight: FontWeight.bold),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12)),
                ),
                onPressed: (state.content.trim().isEmpty || state.isLoading)
                    ? null
                    : () => notifier.submit(),
                child: state.isLoading
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                            strokeWidth: 2, color: Colors.white),
                      )
                    : const Text('提交', style: TextStyle(fontSize: 18)),
              ),
            ),
          ],
        ),
      ),
    );
  }

}

class FeedBackSuccess extends ConsumerWidget {
  const FeedBackSuccess({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notifier = ref.read(feedBackProvider.notifier);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // const Icon(Icons.assignment_turned_in, size: 80, color: Colors.blue),
          Image.asset(AssetsImages.emptyPng, width: 174, height: 174),
          const SizedBox(height: 24),
          const Text('您的意见已反馈成功!',
              style: TextStyle(fontSize: 18, color: AppColors.textColor9)),
          const SizedBox(height: 32),
          SizedBox(
            width: 200,
            height: 48,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
              ),
              onPressed: () {
                notifier.reset();
              },
              child: const Text('再来一条', style: TextStyle(fontSize: 18)),
            ),
          ),
        ],
      ),
    );
  }
}
