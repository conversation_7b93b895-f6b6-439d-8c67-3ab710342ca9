import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:zrreport/common/index.dart';

class FeedBackState {
  final List<String> images; // 只保存图片url
  final String content;
  final bool isLoading;
  final bool isSuccess;
  FeedBackState({
    required this.images,
    required this.content,
    required this.isLoading,
    required this.isSuccess,
  });
  FeedBackState copyWith({
    List<String>? images,
    String? content,
    bool? isLoading,
    bool? isSuccess,
  }) {
    return FeedBackState(
      images: images ?? this.images,
      content: content ?? this.content,
      isLoading: isLoading ?? this.isLoading,
      isSuccess: isSuccess ?? this.isSuccess,
    );
  }
}

class FeedBackNotifier extends StateNotifier<FeedBackState> {
  FeedBackNotifier()
      : super(FeedBackState(
            images: [], content: '', isLoading: false, isSuccess: false));

  Future<String?> uploadImage(String filePath) async {
    try {
      final uploadResult =
          await UserApi.upload(filePath, ImageType.defaultPath);
      // newAvatarUrl.value = uploadResult.data?.url;
      return uploadResult.data?.url;
    } catch (e) {
      Loading.error('上传失败');
    }
    return null;
  }

  void addImage(BuildContext context) async {
    if (state.images.length >= 4) return;

    final file =
        await ImagePickerHelper(context).pickImage2(ImageSource.gallery);

    if (file != null) {
      state = state.copyWith(isLoading: true);
      final url = await uploadImage(file.path);
      if ((url ?? "").isNotEmpty) {
        state =
            state.copyWith(images: [...state.images, url!], isLoading: false);
      }
    }
  }

  void removeImage(int index) {
    final newImages = [...state.images]..removeAt(index);
    state = state.copyWith(images: newImages);
  }

  void setContent(String content) {
    state = state.copyWith(content: content);
  }

  Future<void> submit() async {
    if (state.content.trim().isEmpty || state.isLoading) return;
    state = state.copyWith(isLoading: true);
    // 模拟提交
    // await Future.delayed(const Duration(seconds: 2));

    final deviceInfoPlugin = DeviceInfoPlugin();
    String deviceBrand = "";
    String deviceModel = "";
    String deviceSystem = "";
    String devicePlatform = "";

    if (Platform.isAndroid) {
      final info = await deviceInfoPlugin.androidInfo;
      deviceBrand = info.brand;
      deviceModel = info.model;
      // deviceSystem = info.version;
      devicePlatform = "android";
    } else if (Platform.isIOS) {
      final info = await deviceInfoPlugin.iosInfo;
      deviceBrand = "apple";
      deviceModel = info.model;
      deviceSystem = info.systemVersion;
      devicePlatform = "iOS";
    }

    try {
      await UserApi.feedback(FeedbackEntity(
          content: state.content,
          pics: state.images.join(','),
          deviceBrand: deviceBrand,
          deviceModel: deviceModel,
          deviceSystem: deviceSystem,
          devicePlatform: devicePlatform));
      state = state.copyWith(isLoading: false, isSuccess: true);
    } catch (error) {
      state = state.copyWith(isLoading: false);
      Loading.error("反馈错误");
    }
  }

  void reset() {
    state = FeedBackState(
        images: [], content: '', isLoading: false, isSuccess: false);
  }
}

final feedBackProvider =
    StateNotifierProvider.autoDispose<FeedBackNotifier, FeedBackState>((ref) {
  return FeedBackNotifier();
});
