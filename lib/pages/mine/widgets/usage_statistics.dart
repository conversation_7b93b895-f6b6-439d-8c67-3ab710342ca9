import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get/get.dart';
import 'package:zrreport/common/index.dart';

class UsageStatistics extends ConsumerWidget {
  const UsageStatistics({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    Future(() {
      if (context.mounted) {
        ref.read(usageStatisticsNotifierProvider.notifier).refresh();
      }
    });
    return _buildUsageCard(ref);
  }

  Widget _buildUsageCard(WidgetRef ref) {
    final usageAsync = ref.watch(usageStatisticsNotifierProvider);
    final textColor = Colors.white;
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.primary,
        borderRadius: BorderRadius.all(
          Radius.circular(12),
        ),
        // image: DecorationImage(
        //   image: AssetImage(AssetsImages.homeUsageBackgroundPng),
        //   fit: BoxFit.fitWidth,
        // ),
      ),
      padding: EdgeInsets.only(left: 12, right: 12, top: 12, bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(children: [
            SizedBox(width: 4),
            Text(
              "使用情况",
              style: TextStyle(
                  fontSize: 18, fontWeight: FontWeight.w600, color: textColor),
            ),
            Spacer(),
            GestureDetector(
                onTap: () => Get.toNamed(RouteNames.usageStatistics),
                child: Row(
                  children: [
                    Text('查看详情', style: TextStyle(color: textColor)),
                    SizedBox(width: 3),
                    Icon(
                      Icons.arrow_forward_ios_rounded,
                      color: textColor,
                      size: 10,
                    )
                  ],
                )),
          ]),
          SizedBox(height: 15),
          Row(
            children: [
              _buildUsageItemByAsyncValue(usageAsync,
                  title: (data) => "${data.freeQueryNum}次",
                  titleColor: textColor,
                  subtitle: '总次数'),
              SizedBox(width: 8),
              _buildUsageItemByAsyncValue(usageAsync,
                  title: (data) => "${data.usedQueryNum}次",
                  titleColor: textColor,
                  subtitle: '已使用'),
              SizedBox(width: 8),
              _buildUsageItemByAsyncValue(usageAsync,
                  title: (data) => "${data.availQueryNum}次",
                  titleColor: textColor,
                  subtitle: '可用'),
            ],
          ),
          SizedBox(height: 14),
          Row(
            children: [
              SizedBox(width: 10),
              Text('推广增加查询次数\n可以无限叠加使用',
                  style: TextStyle(
                      fontSize: 13,
                      color: textColor,
                      height: 22 / 13,
                      fontWeight: FontWeight.w600)),
              Spacer(),
              buildFilledButton('推广得查询次数',
                  width: 140,
                  height: 34,
                  fontSize: 13,
                  backgroundColor: Colors.white,
                  fontColor: AppColors.primary,
                  fontWeight: FontWeight.w700,
                  onPressed: () => Get.toNamed(RouteNames.member)),
              SizedBox(width: 10),
            ],
          )
        ],
      ),
    );
  }

  _buildUsageItemByAsyncValue(AsyncValue<UsageStatisticsModel> usageAsync,
      {required String Function(UsageStatisticsModel data) title,
      required Color titleColor,
      required String subtitle}) {
    return Expanded(
        child: usageAsync.when(
      loading: () => _buildUsageItem("--", titleColor, subtitle),
      error: (_, st) => _buildUsageItem("--", titleColor, subtitle),
      data: (data) => _buildUsageItem(title(data), titleColor, subtitle),
    ));
  }

  _buildUsageItem(String title, Color titleColor, String subtitle) {
    return Container(
      decoration: const BoxDecoration(
        color: Color(0xFF689CF8),
        borderRadius: BorderRadius.all(
          Radius.circular(12),
        ),
      ),
      padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: Column(children: [
        Text(
          title,
          style: TextStyle(
              fontSize: 14, fontWeight: FontWeight.w600, color: titleColor),
        ),
        SizedBox(height: 2),
        Text(
          subtitle,
          style: TextStyle(fontSize: 12, color: Colors.white),
        ),
      ]),
    );
  }
}
