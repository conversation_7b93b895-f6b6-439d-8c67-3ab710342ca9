// @riverpod
// class HomePageProvider {}

import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';

createListProvider() {
  return StateNotifierProvider.autoDispose<PaginationViewStateNotifier<Article>,
      PaginationViewState<List<Article>>>((ref) {
    return PaginationViewStateNotifier<Article>(fetchItems:
        (int pageIndex, int pageSize, {CancelToken? cancelToken}) async {
      final result = await ArticleApi.articles(
          cancelToken: cancelToken,
          entity: ListEntity(
            pageIndex,
            pageSize,
          ));
      return result.data;
    });
  });
}

final homePageArticleListProvider = createListProvider();
