import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';

part 'provider.g.dart';

class ArticleState {
  // final bool isCollected;
  // final bool isLike;
  final ArticleDetail? articleDetail;

  final PageStatus pageStatus;

  final dynamic error;

  ArticleState({
    this.pageStatus = PageStatus.initial,
    // required this.isCollected,
    // required this.isLike,
    this.articleDetail,
    this.error,
  });

  bool get isCollected => articleDetail?.collectStatus ?? false;
  bool get isLiked => articleDetail?.likeStatus ?? false;

  int get likeCount => articleDetail?.likeCount ?? 0;
  int get collectCount => articleDetail?.collectCount ?? 0;

  ArticleState copyWith(
      {PageStatus? pageStatus,
      // bool? isCollected,
      // bool? isLike,
      ArticleDetail? articleDetail,
      String? error}) {
    return ArticleState(
        pageStatus: pageStatus ?? this.pageStatus,
        // isCollected: isCollected ?? this.isCollected,
        // isLike: isLike ?? this.isLike,
        articleDetail: articleDetail ?? this.articleDetail,
        error: error ?? this.error);
  }
}

@riverpod
class ArticlePageInfo extends _$ArticlePageInfo {
  @override
  ArticleState build() {
    return ArticleState();
  }

  String? articleId;

  void firstLoad(String articleId) {
    this.articleId = articleId;
    getArticleDetail(articleId);
  }

  Future<void> getArticleDetail(
    String articleId,
  ) async {
    try {
      final response = await ArticleApi.articleDetail(articleId: articleId);
      state = state.copyWith(
          articleDetail: response.data, pageStatus: PageStatus.success);
    } catch (e) {
      defaultLogger.error("get articleDetail($articleId) failed,error:$e");
      state = state.copyWith(
        pageStatus: PageStatus.error,
        error: e.toString(),
      );
    }
  }

  void refresh() {
    if (articleId != null) {
      getArticleDetail(articleId!);
    }
  }

  bool isLikeActionProcessing = false;

  Future<bool> likeAction() async {
    if (isLikeActionProcessing) return false;
    if (!(await loginIfNotLogin())) {
      return false;
    }

    try {
      isLikeActionProcessing = true;
      await ArticleApi.likeVideo(articleId!);
      refresh();
      return true;
    } catch (error) {
      return false;
    } finally {
      isLikeActionProcessing = false;
    }
  }

  bool isCollectActionProcessing = false;
  Future<bool> collectAction() async {
    if (isCollectActionProcessing) return false;
    if (!(await loginIfNotLogin())) {
      return false;
    }

    try {
      isCollectActionProcessing = true;
      await ArticleApi.collectVideo(articleId!);
      refresh();
      return true;
    } catch (error) {
      return false;
    } finally {
      isCollectActionProcessing = false;
    }
  }
}
