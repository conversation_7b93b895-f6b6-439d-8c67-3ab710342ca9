import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zrreport/common/index.dart';

class ArticelItem extends StatelessWidget {
  ArticelItem({super.key, required this.article});

  Article article;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20),
      // color: Colors.red,
      child: IntrinsicHeight(
        child: Row(
          children: [
            // 左侧时间线
            SizedBox(
              width: 15,
              child: CustomPaint(
                painter: TimelinePainter(),
                child: Container(),
              ),
            ),
            // 右边内容
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _time(),
                  SizedBox(height: 10),
                  _title(),
                  _content(),
                  _tags(),
                  SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  _time() {
    return Text(
      article.createTime ?? "",
      style: TextStyle(color: AppColors.textColor9, fontSize: 13),
    );
  }

  _title() {
    return Text(
      article.title ?? "",
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
          color: AppColors.textColor3,
          fontSize: 15,
          fontWeight: FontWeight.bold,
          height: 21 / 15),
    );
  }

  _content() {
    if ((article.summary ?? "").isNotEmpty) {
      return Container(
        margin: EdgeInsets.only(top: 7),
        child: Text(
          article.summary!.replaceAll("\n", ""),
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
              color: Color(0xFF666666), fontSize: 13, height: 18.5 / 13),
        ),
      );
    } else {
      return SizedBox.shrink();
    }
  }

  _tags() {
    if (article.tagList == null || article.tagList!.isEmpty) {
      return SizedBox.shrink();
    }
    final style = TextStyle(color: Color(0xFF999999), fontSize: 13);
    final tagBgColor = Color(0xFFF6F9F8);
    return Padding(
      padding: const EdgeInsets.only(top: 7.0),
      child: Wrap(
        spacing: 5,
        children: [
          ...article.tagList!.map((tag) {
            return Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                decoration: BoxDecoration(
                  color: tagBgColor,
                  borderRadius: BorderRadius.circular(3),
                ),
                child: Text(tag.name ?? '', style: style));
          }),
        ],
      ),
    );
  }
}

class TimelinePainter extends CustomPainter {
  final double circleRadius;
  final Color lineColor;
  final Color circleColor;

  TimelinePainter({
    this.circleRadius = 3.0,
    this.lineColor = const Color(0xFFE7E7E7),
    this.circleColor = Colors.blue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paintLine = Paint()
      ..color = lineColor
      ..strokeWidth = 1.0
      ..strokeCap = StrokeCap.round;

    final paintCircle = Paint()
      ..color = circleColor
      ..style = PaintingStyle.fill;

    final lineX = 0.0;
    canvas.drawLine(Offset(lineX, 0), Offset(lineX, size.height), paintLine);

    canvas.drawCircle(Offset(lineX, 8), circleRadius, paintCircle);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
