import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:like_button/like_button.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:zrreport/common/index.dart';

import 'provider.dart';

// ignore: must_be_immutable
class ArticlePage extends StatefulWidget {
  ArticlePage({super.key, required this.articleId});

  String articleId;

  @override
  State<ArticlePage> createState() => _ArticlePageState();
}

class _ArticlePageState extends State<ArticlePage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ProviderScope.containerOf(context)
          .read(articlePageInfoProvider.notifier)
          .firstLoad(widget.articleId);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('新闻详情',
            style: TextStyle(
                color: AppColors.textColor1, fontWeight: FontWeight.bold)),
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: AppColors.textColor1),
      ),
      bottomNavigationBar: Consumer(builder: (context, ref, child) {
        final state = ref.watch(articlePageInfoProvider);
        return BottomAppBar(
          height: 50,
          shape: const CircularNotchedRectangle(),
          child: state.pageStatus == PageStatus.success
              ? _buildBottomAppBar(ref, state)
              : Container(),
        );
      }),
      backgroundColor: AppColors.background,
      body: Consumer(builder: (context, ref, child) {
        final pageStatus = ref
            .watch(articlePageInfoProvider.select((state) => state.pageStatus));
        switch (pageStatus) {
          case PageStatus.initial:
          case PageStatus.loading:
            return LoadingWidget();
          case PageStatus.success:
            return _buildContent(
                ref.read(articlePageInfoProvider).articleDetail!);
          case PageStatus.empty:
            return Center(
              child: Text(''),
            );
          case PageStatus.error:
            return Center(
              child: ErrorStatusWidget(
                  onAttempt: () =>
                      ref.read(articlePageInfoProvider.notifier).refresh()),
            );
        }
      }),
    );
  }

  _buildBottomAppBar(WidgetRef ref, ArticleState state) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        SizedBox(width: 30),
        _collect(ref, state),
        SizedBox(width: 28),
        _like(ref, state),
        Spacer(),
      ],
    );
  }

  _buildContent(ArticleDetail article) {
    return ListView(
      padding: EdgeInsets.symmetric(horizontal: 16),
      children: [
        _title(article),
        _tags(article),
        Html(
          key: ValueKey(article.content),
          data: article.content ?? "",
          onLinkTap: (url, attributes, element) {
            defaultLogger.info("咨询详情，点击了链接 $url att:$attributes");
            if (url != null) launchUrl(Uri.parse(url));
          },
        ),
        Text(
            "本快讯所涉信息、数据可能来源于第三方，内容仅供参考，并不代表助融报告任何明示、暗示之观点或保证。如有任何疑问或需删除请联系助融报告官方客服。",
            style: TextStyle(color: AppColors.textColor9, fontSize: 12)),
      ],
    );
  }

  _tags(ArticleDetail article) {
    if (article.tagList == null || article.tagList!.isEmpty) {
      return SizedBox.shrink();
    }
    final style = TextStyle(color: Color(0xFF999999), fontSize: 13);
    final tagBgColor = Color(0xFFF6F9F8);
    return Padding(
      padding: const EdgeInsets.only(top: 7.0),
      child: Wrap(
        spacing: 5,
        children: [
          ...article.tagList!.map((tag) {
            return Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                decoration: BoxDecoration(
                  color: tagBgColor,
                  borderRadius: BorderRadius.circular(3),
                ),
                child: Text(tag.name ?? '', style: style));
          }),
        ],
      ),
    );
  }

  _title(ArticleDetail article) {
    return Text(
      article.title ?? "",
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
          color: AppColors.textColor3,
          fontSize: 15,
          fontWeight: FontWeight.bold,
          height: 21 / 15),
    );
  }

  _like(WidgetRef ref, ArticleState state) {
    final notifier = ref.read(articlePageInfoProvider.notifier);
    final buttonSize = 20.0;
    final orangeColor = AppColors.orange;
    return LikeButton(
      animationDuration: Duration.zero,
      size: buttonSize,
      circleColor:
          CircleColor(start: orangeColor.withAlpha(25), end: orangeColor),
      bubblesColor: BubblesColor(
        dotPrimaryColor: orangeColor.withAlpha(75),
        dotSecondaryColor: orangeColor.withAlpha(200),
      ),
      countPostion: CountPostion.bottom,
      likeBuilder: (bool isLiked) {
        return SvgPicture.asset(
          isLiked ? AssetsSvgs.likeHighlightSvg : AssetsSvgs.likeSvg,
        );
      },
      isLiked: state.isLiked,
      onTap: (isLiked) async => await notifier.likeAction(),
      likeCount: state.likeCount,
      likeCountPadding: EdgeInsets.only(top: 5),
      countBuilder: (count, isLiked, text) {
        return Text(
          text,
          style: TextStyle(
            fontSize: 10,
            color: isLiked ? orangeColor : AppColors.textColor9,
          ),
        );
      },
    );
  }

  _collect(WidgetRef ref, ArticleState state) {
    final notifier = ref.read(articlePageInfoProvider.notifier);
    final buttonSize = 20.0;
    final orangeColor = AppColors.orange;
    return LikeButton(
      animationDuration: Duration.zero,
      size: buttonSize,
      circleColor:
          CircleColor(start: orangeColor.withAlpha(25), end: orangeColor),
      bubblesColor: BubblesColor(
        dotPrimaryColor: orangeColor.withAlpha(75),
        dotSecondaryColor: orangeColor.withAlpha(200),
      ),
      countPostion: CountPostion.bottom,
      likeBuilder: (bool isLiked) {
        return SvgPicture.asset(
          isLiked ? AssetsSvgs.collectHighlightSvg : AssetsSvgs.collectSvg,
        );
      },
      isLiked: state.isCollected,
      onTap: (isLiked) async => await notifier.collectAction(),
      likeCount: state.collectCount,
      likeCountPadding: EdgeInsets.only(top: 5),
      countBuilder: (count, isLiked, text) {
        return Text(
          count.toString(),
          style: TextStyle(
            fontSize: 10,
            color: isLiked ? orangeColor : AppColors.textColor9,
          ),
        );
      },
    );
  }
}
