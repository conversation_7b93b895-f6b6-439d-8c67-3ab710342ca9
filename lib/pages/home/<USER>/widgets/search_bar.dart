import 'dart:math';

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:zrreport/common/index.dart';
import 'search_enterprise_viewmodel.dart';

class HomePageSearchBar extends StatefulWidget {
  const HomePageSearchBar({super.key});

  @override
  State<HomePageSearchBar> createState() => _HomePageSearchBarState();
}

class _HomePageSearchBarState extends State<HomePageSearchBar> {
  // 当前页码
  int currentPage = 1;
  bool hasMorePage = false;

  // 刷新控制器
  EasyRefreshController easyRefreshController = EasyRefreshController(
    controlFinishRefresh: true,
    controlFinishLoad: true,
  );

  @override
  void initState() {
    super.initState();
    // _debouncedSearch =
    //     _debounce<Iterable<SearchEnterprise>?, String>(_firstSearch);
  }

  TextEditingController? _textEditingController;

  List<SearchEnterprise> enterprises = [];

  SearchEnterpriseViewModel viewModel = SearchEnterpriseViewModel();

  @override
  Widget build(BuildContext context) {
    return _buildAutocomplete();
  }

  MyAutocomplete<SearchEnterprise> _buildAutocomplete() {
    return MyAutocomplete<SearchEnterprise>(
      fieldViewBuilder:
          (context, textEditingController, focusNode, onFieldSubmitted) {
        _textEditingController = textEditingController;
        return _buildSearchField(
            context, textEditingController, focusNode, onFieldSubmitted);
      },
      optionsViewBuilder: (context, onSelected, options) {
        return _optionsList(onSelected);
      },
      optionsBuilder: (TextEditingValue textEditingValue) async {
        final text = textEditingValue.text;
        if (text == '') {
          return [];
        }
        await viewModel.firstLoad(text);
        defaultLogger.info("optionsBuilder length:${viewModel.items.length}");
        return viewModel.items;
      },
      onSelected: (option) async {
        _textEditingController?.text = option.companyName ?? "";
        if ((!await loginIfNotLogin())) {
          return;
        }

        Get.toNamed(RouteNames.queryStep1,
            arguments: {"creditName": option.companyName});
      },
    );
  }

  _optionsList(AutocompleteOnSelected<SearchEnterprise> onSelected) {
    defaultLogger.info("_optionsList 11111 length:${viewModel.items.length}");
    return StatefulBuilder(builder: (context, setListState) {
      defaultLogger.info("_optionsList length:${viewModel.items.length}");
      if (viewModel.items.isEmpty) {
        return SizedBox.shrink();
      }
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 20),
            decoration: BoxDecoration(
              color: Color(0xFFF7F7F7),
              // color: Colors.red,
              borderRadius: BorderRadius.circular(10),
            ),
            height: min(140, max(viewModel.items.length * 40, 100)),
            child: EasyRefresh(
              controller: viewModel.easyRefreshController,
              onLoad: viewModel.hasMorePate
                  ? () async {
                      try {
                        await viewModel.loadMore();
                        setListState(() {});
                      } catch (error) {}
                    }
                  : null,
              child: ListView(
                key: ValueKey(viewModel.keyword),
                padding: EdgeInsets.only(top: 8, bottom: 5),
                children: [
                  ...viewModel.items.map((a) {
                    return GestureDetector(
                      onTap: () {
                        onSelected(a);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 5, horizontal: 20),
                        child: Text(
                          a.companyName ?? '',
                          style: TextStyle(
                              fontSize: 15, color: AppColors.textColor1),
                        ),
                      ),
                    );
                  }),
                ],
              ),
            ),
          ),
          Spacer(),
        ],
      );
    });
  }

  _buildSearchField(
      BuildContext context,
      TextEditingController textEditingController,
      FocusNode focusNode,
      VoidCallback onFieldSubmitted) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 17.0),
      child: StatefulBuilder(builder: (context, _setState) {
        return TextFormField(
          controller: textEditingController,
          focusNode: focusNode,
          onFieldSubmitted: (String value) {
            onFieldSubmitted();
          },
          onChanged: (value) {
            _setState(() {});
          },
          textInputAction: TextInputAction.search,
          style: TextStyle(color: Color(0xFF222222), fontSize: 14),
          decoration: InputDecoration(
            isDense: true,
            hintText: '请输入企业名称进行税务匹配',
            hintStyle: TextStyle(
                fontSize: 14,
                color: Color(0xFFE6E6E6),
                fontWeight: FontWeight.w600,
                height: 20 / 14),
            hintMaxLines: 1,
            fillColor: Colors.white,
            filled: true,
            prefixIcon: SvgPicture.asset(AssetsSvgs.searchIconSvg,
                width: 20, height: 20),
            prefixIconConstraints:
                BoxConstraints.tightFor(width: 50, height: 20),
            suffixIcon: (_textEditingController?.text ?? '').isNotEmpty
                ? GestureDetector(
                    onTap: () {
                      _textEditingController?.text = '';
                      _setState(() {});
                    },
                    child: Icon(
                      Icons.cancel,
                      size: 16,
                      color: AppColors.textColor8,
                    ),
                  )
                : null,
            suffixIconConstraints:
                BoxConstraints.tightFor(width: 60, height: 40),
            contentPadding:
                EdgeInsetsDirectional.symmetric(horizontal: 24, vertical: 15),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(
                color: Color(0xFFDDDDDD), // 边框颜色
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(
                color: Color(0xFFDDDDDD), // 边框颜色
              ),
            ),
          ),
        );
      }
      ),
    );
  }
}
