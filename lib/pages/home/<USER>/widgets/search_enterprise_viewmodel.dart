import 'dart:async';
import 'package:dio/dio.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:zrreport/common/index.dart';

class SearchEnterpriseViewModel {
  // 刷新控制器
  EasyRefreshController easyRefreshController = EasyRefreshController(
    controlFinishRefresh: false,
    controlFinishLoad: true,
  );

  String? keyword;

  List<SearchEnterprise> items = [];

  CancelToken? _cancelToken;
  int pageIndex = 1;
  int pageSize = 10;

  bool hasMorePate = false;

  var isDebounce = false;

  _DebounceTimer? _debounce;

  firstLoad(String keyword) async {
    reset();

    // if (isDebounce) {
    //   return;
    // }

    // isDebounce = true;

    // await Future.delayed(Duration(seconds: 1));
    // await _firstLoad(keyword);
    // isDebounce = false;

    // 取消之前的计时器
    // if (_debounce != null && !_debounce!.isCompleted) _debounce?.cancel();

    // 设置新的计时器
    _debounce = _DebounceTimer();

    try {
      // await _debounce!.future;
      await _firstLoad(keyword);
      return;
    } on _CancelException {
      return;
    }
  }

  Future<void> _firstLoad(String keyword) async {
    this.keyword = keyword;
    _cancelToken?.cancel();
    _cancelToken = CancelToken();

    defaultLogger.warning("搜索公司keyword:$keyword");

    try {
      final result = await QueryApi.searchEnterprise(
        cancelToken: _cancelToken,
        entity: SearchEnterpriseEntity(keyword, 1, pageSize),
      );

      final pagination = result.data;
      if (pagination != null) {
        final list = pagination.list;
        items = list;
        pageIndex = pagination.pageNum;
        hasMorePate = list.length < pagination.total;

        if (hasMorePate) {
          easyRefreshController.finishLoad();
        } else {
          easyRefreshController.finishLoad(IndicatorResult.noMore);
        }

        defaultLogger.warning("搜索公司成功, items:${list.length}");
      }
    } catch (error) {
      defaultLogger.warning("搜索公司失败，error:$error");
    }
  }

  loadMore() async {
    _cancelToken = CancelToken();
    if (keyword == null) {
      return;
    }
    try {
      final result = await QueryApi.searchEnterprise(
        cancelToken: _cancelToken,
        entity: SearchEnterpriseEntity(keyword!, pageIndex + 1, pageSize),
      );

      final pagination = result.data;
      if (pagination != null) {
        final list = pagination.list;
        items += list;

        pageIndex = pagination.pageNum;
        hasMorePate = items.length < pagination.total;

        if (hasMorePate) {
          easyRefreshController.finishLoad();
        } else {
          easyRefreshController.finishLoad(IndicatorResult.noMore);
        }
        defaultLogger
            .warning("加载更多公司成功, items:${list.length} hasMorePage:$hasMorePate");
      }
    } catch (error) {
      defaultLogger.warning("加载更多公司失败，error:$error");
      rethrow;
    }
  }

  void reset() {
    items = [];
    pageIndex = 0;
    hasMorePate = false;
    keyword = null;
  }
}

const Duration debounceDuration = Duration(milliseconds: 500);

class _DebounceTimer {
  _DebounceTimer() {
    _timer = Timer(debounceDuration, _onComplete);
  }

  late final Timer _timer;
  final Completer<void> _completer = Completer<void>();

  void _onComplete() {
    _completer.complete();
  }

  Future<void> get future => _completer.future;

  bool get isCompleted => _completer.isCompleted;

  void cancel() {
    _timer.cancel();
    _completer.completeError(const _CancelException());
  }
}

// An exception indicating that the timer was canceled.
class _CancelException implements Exception {
  const _CancelException();
}
