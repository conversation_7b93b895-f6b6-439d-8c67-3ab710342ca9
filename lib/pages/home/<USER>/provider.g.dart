// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$articlePageInfoHash() => r'b1204b4f329fa4bcdf1f5b70df821517a282439a';

/// See also [ArticlePageInfo].
@ProviderFor(ArticlePageInfo)
final articlePageInfoProvider =
    AutoDisposeNotifierProvider<ArticlePageInfo, ArticleState>.internal(
  ArticlePageInfo.new,
  name: r'articlePageInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$articlePageInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ArticlePageInfo = AutoDisposeNotifier<ArticleState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
