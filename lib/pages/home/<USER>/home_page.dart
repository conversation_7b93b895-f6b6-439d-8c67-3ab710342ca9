import 'dart:async';

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get/get.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/home/<USER>/provider.dart';
import 'package:zrreport/pages/home/<USER>/articel_item.dart';
import 'package:zrreport/pages/index.dart';
import 'package:zrreport/pages/system/main/main_page_provider.dart';

import 'widgets/search_bar.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    super.build(
        context); // Call super.build as required by AutomaticKeepAliveClientMixin
    return DefaultTabController(
      length: 3,
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Consumer(builder: (context, ref, child) {
          return Scaffold(
            backgroundColor: Color(0xFFFAF8F9),
            resizeToAvoidBottomInset: false,
            appBar: AppBar(
              backgroundColor: AppColors.primary,
              title: GestureDetector(
                  onLongPress: () {
                    if (AppConfig.to.isDevelopModel) {
                      Navigator.push<LocationDetail>(
                        context,
                        MaterialPageRoute(
                          builder: (context) => DevelopConfigPage(),
                        ),
                      );
                    }
                  },
                  child: Text('助融报告', style: TextStyle(color: Colors.white))),
              bottom: PreferredSize(
                preferredSize: Size.fromHeight(120),
                child: Column(
                  children: [
                    _slogan(),
                    SizedBox(height: 15),
                    HomePageSearchBar(),
                    SizedBox(height: 15),
                  ],
                ),
              ),
            ),
            body: _listView(context, ref),
          );
        }),
      ),
    );
  }

  _slogan() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset(AssetsImages.homeSlogan2Png),
        SizedBox(
          height: 34,
          child: VerticalDivider(
            color: Colors.white,
            width: 20,
            thickness: 1,
          ),
        ),
        Image.asset(AssetsImages.homeSlogan1Png),
      ],
    );
  }

  _listView(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homePageArticleListProvider);
    return state.when(
      empty: () => EmptyWidget(text: '暂无数据'),
      ready: (data) => _buildContent(context, ref, data),
      error: (String error) {
        return Center(
          child: ErrorStatusWidget(
              text: error,
              onAttempt: () => ref.refresh(homePageArticleListProvider)),
        );
      },
      loading: () => LoadingWidget(),
    );
  }

  var scrollDirection = ScrollDirection.idle;

  _buildContent(context, WidgetRef ref, List<Article> data) {
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification notification) {
        if (notification is UserScrollNotification) {
          if (scrollDirection == ScrollDirection.idle &&
              notification.direction != scrollDirection) {
            print('列表开始滚动 ${notification.direction}');
            FocusManager.instance.primaryFocus?.unfocus();
          }
          scrollDirection = notification.direction;
        }
        return false;
      },
      child: EasyRefresh(
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 30),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildMenu(AssetsImages.homeMenuIcon_1Png, "征信网点", () {
                      Get.toNamed(RouteNames.creditServicePoint);
                    }),
                    _buildMenu(AssetsImages.homeMenuIcon_2Png, "产品匹配", () {
                      Get.toNamed(RouteNames.loanMatching);
                    }),
                    _buildMenu(AssetsImages.homeMenuIcon_3Png, "税务查询",
                        () async {
                      if ((!await loginIfNotLogin())) {
                        return;
                      }
                      Get.toNamed(RouteNames.queryStep1);
                    }),
                  ],
                ),
              ),
            ),
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  return Padding(
                    padding: EdgeInsets.only(top: index == 0 ? 24 : 0),
                    child: GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () => Get.toNamed(RouteNames.aricleDetailPage,
                            arguments: {"articleId": data[index].id ?? ''}),
                        child: ArticelItem(article: data[index])),
                  );
                },
                childCount: data.length,
              ),
            ),
          ],
        ),
        onRefresh: () =>
            ref.watch(homePageArticleListProvider.notifier).firstLoadPage(),
        onLoad: () =>
            ref.watch(homePageArticleListProvider.notifier).loadMore(),
      ),
    );
  }

  _buildMenu(String image, String title, VoidCallback action) {
    return GestureDetector(
      onTap: action,
      child: Column(
        children: [
          Image.asset(image),
          SizedBox(height: 3),
          Text(title, style: TextStyle(color: AppColors.textColor6))
        ],
      ),
    );
  }
}
