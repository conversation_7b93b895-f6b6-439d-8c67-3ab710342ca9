import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';

class ListProvincePage extends ConsumerWidget {
  const ListProvincePage({super.key});

  static const grayColor = Color(0xFFF1F5F9);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final provincesAsync = ref.watch(provinceListNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('选择省份',
            style: TextStyle(
                color: AppColors.textColor1, fontWeight: FontWeight.bold)),
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: AppColors.textColor1),
      ),
      backgroundColor: AppColors.background,
      body: provincesAsync.when(
        loading: () => LoadingWidget(),
        error: (e, st) => Center(
          child: ErrorStatusWidget(
              onAttempt: () =>
                  ref.read(provinceListNotifierProvider.notifier).refresh()),
        ),
        data: (provinces) => Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                
                image: AssetImage(AssetsImages.historyBackgroundPng),
                fit: BoxFit.fill,
              ),
            ),
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Container(
                // color: Colors.white,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(
                    Radius.circular(12),
                  ),
                ),
                child: Column(
                  children: [
                    Container(
                      height: 48,
                      alignment: Alignment.center,
                      child: Text(
                        '当前维护地区查询',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppColors.textColor3,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    _buildTableHeader(),
                    Expanded(
                      child: CustomScrollView(
                        slivers: [_buildList(provinces)],
                      ),
                    ),
                  ],
                ))),
      ),
    );
  }

  Widget _buildTableHeader() {
    final textStyle = TextStyle(
        color: Color(0xFF488AFD), fontSize: 13, fontWeight: FontWeight.w600);
    return Container(
      height: 40,
      color: grayColor,
      child: Row(
        children: [
          Expanded(
              child: Text(
            '地区',
            textAlign: TextAlign.center,
            style: textStyle,
          )),
          Expanded(
              child: Text(
            '发票信息',
            textAlign: TextAlign.center,
            style: textStyle,
          )),
          Expanded(
              child: Text(
            '税务信息',
            textAlign: TextAlign.center,
            style: textStyle,
          )),
        ],
      ),
    );
  }

  Widget _buildListItem(Province province, Color backgroundColor) {
    final textStyle = TextStyle(
        color: AppColors.textColor1, fontSize: 13, fontWeight: FontWeight.w600,height: 20/13);

    Color warnColor = Color(0xFFFF5959);
    return Container(
                padding: const EdgeInsets.symmetric(vertical: 11),
          color: backgroundColor,
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                  child: Text(
                province.name ?? '',
                textAlign: TextAlign.center,
                style: textStyle,
              )),
          
              /// 发票状态
              Expanded(
                  child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      color:
                          province.isInvoiceOk ? AppColors.primary : warnColor,
                      borderRadius: BorderRadius.all(
                        Radius.circular(3),
                      ),
                    ),
                  ),
                  SizedBox(width: 4),
                  Text(
                    province.isInvoiceOk ? '正常使用' : '维护中',
                    textAlign: TextAlign.center,
                    style: textStyle,
                  ),
                ],
              )),
          
              /// 税务状态
              Expanded(
                  child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      color:
                          province.isInvoiceOk ? Color(0xFF51C3A5) : warnColor,
                      borderRadius: BorderRadius.all(
                        Radius.circular(3),
                      ),
                    ),
                  ),
                  SizedBox(width: 4),
                  Text(
                    province.isTaxOk ? '正常使用' : '维护中',
                    textAlign: TextAlign.center,
                    style: textStyle,
                  ),
                ],
              )),
            ],
          ),
          if (province.startTime != null || province.endTime != null)
          ..._buildMaintenanceWidget(province),
        ],
      ),
    );
  }

  List<Widget> _buildMaintenanceWidget(Province province) {
    final style = TextStyle(color: AppColors.textColor9, fontSize: 11);
    return [
      SizedBox(height: 6),

      Row(
        children: [
          Spacer(),
          Text('维护时间起: ${province.startTime ?? "未知"}',style: style,textAlign: TextAlign.end),
          SizedBox(width: 30),
        ],
      ),
      
      Row(
        children: [
          Spacer(),
          Text('维护时间起: ${province.endTime ?? "未知"}',style: style,textAlign: TextAlign.end),
          SizedBox(width: 30),
        ],
      ),
    ];
  }

  Widget _buildList(List<Province> provinces) {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final province = provinces[index];
          return _buildListItem(
              province, index % 2 == 0 ? Colors.white : grayColor);
        },
        childCount: provinces.length,
      ),
    );
  }
}

extension ProvinceStats on Province {
  bool get isTaxOk => taxStatus == "1";
  bool get isInvoiceOk => invoiceStatus == "1";
}
