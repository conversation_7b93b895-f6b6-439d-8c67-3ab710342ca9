name: zrreport
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  uuid: ^4.2.1
  lpinyin: ^2.0.3

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  get: ^4.7.2
  ducafe_ui_core: ^1.0.6
  flutter_inappwebview: ^6.0.0
  dio: ^5.8.0+1
  flutter_easyloading: ^3.0.5
  shared_preferences: ^2.5.2
  cached_network_image: ^3.4.1
  flutter_svg: ^2.0.17
  easy_refresh: ^3.4.0


  flutter_image_compress: ^2.4.0
  image_cropper: ^9.0.0

  path_provider: ^2.1.2
  image_picker: ^1.1.2
  
  
  photo_view: ^0.15.0
  flutter_image_gallery_saver: ^0.0.2
  
  url_launcher: ^6.3.1
  logger: ^2.5.0

  permission_handler: ^11.4.0
  geolocator: ^12.0.0

  crypto: ^3.0.3

  scrollable_positioned_list: ^0.3.8


  system_proxy:
    path: ./vendor_plugins/system_proxy

  visibility_detector: ^0.4.0+2

  package_info_plus: ^8.3.0

  # 修改应用图标
  flutter_launcher_icons: ^0.14.3

  # intl: ^0.20.2
  flutter_localizations:
    sdk: flutter
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1
  freezed_annotation: ^3.0.0
  json_annotation: ^4.8.1

  syncfusion_flutter_datagrid: ^29.1.38
  flutter_hooks: ^0.21.2

  flutter_html: ^3.0.0

  like_button: ^2.0.5

  device_info_plus: 11.3.0

  app_settings: ^6.1.1

  sqflite: ^2.3.2
  path: ^1.8.3

flutter_icons:
  android: "launcher_icon"  # Android 图标配置
  ios: true                 # iOS 图标配置（可选）
  image_path: "assets/icon/icon.png"  # 源图标路径（建议 1024x1024）
  

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  custom_lint: ^0.7.5
  riverpod_lint: ^2.6.5
  riverpod_generator: ^2.6.5
  json_serializable: ^6.7.1  # 添加这行
  freezed: ^3.0.6 

  build_runner: ^2.4.15

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  assets:
    - assets/images/
    - assets/svgs/


  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
