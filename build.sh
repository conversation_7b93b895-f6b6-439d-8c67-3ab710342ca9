#!/bin/bash

FLUTTER_PROJECT_DIR=`pwd`

APK_FILE=$FLUTTER_PROJECT_DIR/build/app/outputs/flutter-apk/app-release.apk
IPA_FILE=$FLUTTER_PROJECT_DIR/build/ios/ipa/shunxiao.ipa
API_KEY="c1fa185b1186dd64c70920b93e4c661c"
FLUTTER_VERSION="3.27.2"

# 检查 Flutter 版本并设置 FLUTTER 变量
check_flutter_version() {

  if command -v fvm &> /dev/null; then
    version=$(fvm flutter --version | grep "Flutter" | awk '{print $2}')
    if [ "$version" = "$FLUTTER_VERSION" ]; then
      echo "使用 fvm flutter $FLUTTER_VERSION"
      FLUTTER="fvm flutter"
      return 0
    fi
  fi
  if command -v flutter &> /dev/null; then
    version=$(flutter --version | grep "Flutter" | awk '{print $2}')
    if [ "$version" = "$FLUTTER_VERSION" ]; then
      echo "使用 flutter $FLUTTER_VERSION"
      FLUTTER="flutter"
      return 0
    fi
  fi
  
  echo "错误: 未找到 Flutter $FLUTTER_VERSION 版本"
  exit 1
}

# 检查 Flutter 版本
check_flutter_version


# 使用说明
show_usage() {
  echo "使用说明:"
  echo "  ./build.sh build android adhoc [--upload]   # 打包android 蒲公英包，可选上传到蒲公英" 
  echo "  ./build.sh build android appstore           # 打包android正式包,不支持上传"
  echo "  ./build.sh build iOS adhoc [--upload]   # 打包iOS adhoc包，可选上传到蒲公英" 
  echo "  ./build.sh build iOS appstore           # 打包iOS正式包(需手动上传到App Store Connect)"
  echo "  ./build.sh upload ipa                   # 仅上传iOS IPA包到蒲公英"
  echo "  ./build.sh upload apk                   # 仅上传Android APK到蒲公英"
}

# 上传IPA和APK到蒲公英
upload_to_pgyer() {
  local file_path=$1
  echo "开始上传文件到蒲公英..."
  
  RESULT=$(curl \
      -F "file=@${file_path}" \
      -F "_api_key=${API_KEY}" \
      "https://www.pgyer.com/apiv2/app/upload" | tee /dev/null)


  if command -v jq &> /dev/null; then
    # 解析返回的JSON并检查code字段
    CODE=$(echo "$RESULT" | jq -r '.code')
    if [ "$CODE" != "0" ]; then
      echo "$RESULT"
      MESSAGE=$(echo "$RESULT" | jq -r '.message')
      echo "上传失败: $MESSAGE"
      exit 1
    fi
  else
    echo "请安装jq工具, mac 安装命令: brew install jq"
    echo "上传结果:$RESULT"
    exit 0
  fi

  echo "文件上传蒲公英成功!"
} 

# android打包函数
build_android() {
  local export_method=$1
  local should_upload=$2

  echo "开始构建Android APK..."
  BUILD_NUMBER=$(git rev-list --count HEAD)
  echo "构建号: $BUILD_NUMBER"
  echo "export_method : $export_method"
  
  if [ "$export_method" == "adhoc" ]; then
    echo "构建测试包..."
    $FLUTTER build apk \
      --target-platform android-arm,android-arm64 \
      --release \
      --build-number="$BUILD_NUMBER" \
      --dart-define=CHANNEL=pgyer 
  elif [ "$export_method" == "appstore" ]; then
    echo "构建正式包..."
    $FLUTTER build apk \
      --target-platform android-arm,android-arm64 \
      --release \
      --build-number="$BUILD_NUMBER" \
      --dart-define=CHANNEL=appstore 
  else 
    echo "错误: Android打包需要指定打包类型(adhoc/appstore)"
    show_usage
    exit 1
  fi
    
  if [ $? -eq 0 ]; then
    echo "Android APK构建成功!"
    if [ "$should_upload" = "true" ]; then
      upload_to_pgyer $APK_FILE
    fi
  else
    echo "Android APK构建失败!"
    exit 1
  fi
}


build_ios() {

  local export_method=$1
  local should_upload=$2

  BUILD_NUMBER=$(git rev-list --count HEAD)
  echo "构建号: $BUILD_NUMBER"
  echo "export_method : $export_method"

  # adhoc 
  if [ "$export_method" == "adhoc" ]; then
    echo "开始打测试包..."
    $FLUTTER build ipa \
        --release --build-number="$BUILD_NUMBER" \
        --export-options-plist=$FLUTTER_PROJECT_DIR/ios/exportOptions/ad-hoc.plist \
        --dart-define=CHANNEL=pgyer 

    if [ $? -eq 0 ]; then
      echo "iOS ipa构建成功!"
      if [ "$should_upload" = "true" ]; then
        upload_to_pgyer $IPA_FILE
      fi
    else
      echo "iOS ipa构建失败!"
      exit 1
    fi
  elif [ "$export_method" == "appstore" ]; then
    echo "开始打正式包..."


    $FLUTTER build ipa \
        --release --build-number="$BUILD_NUMBER" \
        --export-options-plist=$FLUTTER_PROJECT_DIR/ios/exportOptions/app-store.plist \
        --dart-define=CHANNEL=appstore 
    if [ $? -eq 0 ]; then
      echo "iOS ipa构建成功!"
    else
      echo "iOS ipa构建失败!"
      exit 1
    fi
  else 
    echo "错误: Android打包需要指定打包类型(adhoc/appstore)"
    show_usage
    exit 1
  fi
}




main() {
  # 参数检查
  if [ $# -lt 1 ]; then
    show_usage
    exit 1
  fi


  local ACTION=$1

  echo "ACTION: $ACTION"


  case $ACTION in
    "build")

      local PLATFORM=$2
      echo "PLATFORM: $PLATFORM"
      
      # 检查是否包含 --upload 参数
      local SHOULD_UPLOAD="false"
      for arg in "$@"; do
        if [ "$arg" = "--upload" ]; then
          SHOULD_UPLOAD="true"
          break
        fi
      done

      case $PLATFORM in
        "android")
          local EXPORT_METHOD=$3
          if [ -z "$EXPORT_METHOD" ]; then
            echo "错误: Android打包需要指定打包类型(adhoc/appstore)"
            show_usage
            exit 1
          fi
          build_android $EXPORT_METHOD $SHOULD_UPLOAD
          ;;
        "iOS")

          local EXPORT_METHOD=$3
          echo "EXPORT_METHOD: $EXPORT_METHOD"
          if [ -z "$EXPORT_METHOD" ]; then
            echo "错误: iOS打包需要指定打包类型(adhoc/appstore)"
            show_usage
            exit 1
          fi
          build_ios $EXPORT_METHOD $SHOULD_UPLOAD
          ;;
        *)
          echo "错误: 不支持的平台类型"
          show_usage
          exit 1
          ;;
      esac
      ;;
    "upload")
      local PLATFORM=$2
      echo "PLATFORM: $PLATFORM"
      case $PLATFORM in
        "ipa")
          upload_to_pgyer $IPA_FILE
          ;;
        "apk") 
          upload_to_pgyer $APK_FILE
          ;;
        *)
          echo "错误: 上传类型必须是ipa或apk"
          show_usage
          exit 1
          ;;
      esac
      ;;
    *)
      echo "错误: 不支持的操作类型"
      show_usage
      exit 1
      ;;
  esac
}

main "$@"
