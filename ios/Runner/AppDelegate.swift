import Flutter
import UIKit

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}



public class ShunShunNativePlugin: NSObject, FlutterPlugin {
    public static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "com.shunshun.native_plugin", binaryMessenger: registrar.messenger())
        let instance = ShunShunNativePlugin()
        registrar.addMethodCallDelegate(instance, channel: channel)
    }
    
    var channel: String {
        let appStoreReceipt = Bundle.main.appStoreReceiptURL
#if DEBUG
        return "develop"
#elseif PROFILE
        return "develop"
#else
        if (appStoreReceipt?.absoluteString.contains("sandboxReceipt") == true) {
            return "testflight"
        } else {
            return "appstore"
        }
#endif
    }
    
    var uploadResult: FlutterResult?
    
    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch(call.method) {
        case "getChannel":
            result(channel)
        default :
            result(nil)
        }
        
    }
}
